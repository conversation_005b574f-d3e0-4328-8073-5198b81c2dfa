/* Importing Bootstrap SCSS file. */
@import 'variables';

@layer bootstrap, revert, primeng;


@layer bootstrap {
  @import "node_modules/bootstrap/scss/bootstrap";
}

@layer revert {
  fieldset, legend {
    all: revert;
  }
}

@layer primeng {
  @import 'node_modules/primeng/resources/themes/saga-blue/theme.css';
  @import 'node_modules/primeng/resources/primeng.min.css';
}

@import 'node_modules/ag-grid-community/styles/ag-grid.css';
@import 'node_modules/ag-grid-community/styles/ag-theme-alpine.css';


* {
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

*:focus {
  outline: none !important;
}

/* Prevent bootstrap override primeng p-fieldset */
//fieldset, legend {
//  all: revert;
//}

html {
  min-width: 1366px;
}

//input:disabled {
//  opacity: 0.9 !important;
//}

.p-button:disabled {
  opacity: 0.6 !important;
}

p-button {
  button {
    min-width: 90px;
  }
  button.p-button:focus {
    box-shadow: none;
  }
}

p-button:not([styleClass]) {
  button.p-button,
  button.p-button:active,
  span.p-button,
  span.p-button:active {
    background: $primary-color !important;
    border: $primary-color solid 1px !important;
    :enabled:hover {
      background: $primary-color !important;
      opacity: 0.8 !important;
    }
  }
}

div.p-checkbox-box {
  background: $secondary-color !important;
  border: $secondary-color solid 1px !important;
}

p-button:not([styleClass]) {
  p-button.secondary-button {
    button.p-button,
    button.p-button:active,
    button.p-button:focus {
      background: $background-green;
      border: $background-green solid 1px;
    }

    :enabled:hover {
      background: $background-green !important;
      border: $background-green solid 1px !important;
      opacity: 0.8;
    }
  }
}

p-button.cancel-button {
  button.p-button,
  button.p-button:active,
  button.p-button:focus {
    background: $background-red;
    border: $background-red solid 1px;
  }
  :enabled:hover {
    background: $background-red !important;
    border: $background-red solid 1px !important;
    opacity: 0.8;
  }
}

.p-panel .p-panel-header {
  background-color: $secondary-color !important;
  border: 1px solid $secondary-color !important;
}

.p-panel-title {
  color: $secondary-font-color;
}

//.p-panel .p-panel-header {
//  background: $blue !important;
//  color: white !important;
//  border: none !important;
//}
//
//.p-panel .p-panel-content {
//  padding: 0 !important;
//  border: none !important;
//  color: black !important;
//  background: white !important;
//}

.title {
  font-size: 1.7rem;
  color: #105798;
  font-family: 'Roboto';
  font-weight: bold;
}

.action-button-container {
  display: flex;
  justify-content: flex-end;
  margin: 15px 0;
  word-spacing: 10px;
  gap: 10px;
}

p-panel.detail-panel .card-body {
  > .row {
    margin: 1% 0;
    > div:nth-child(1) {
      font-weight: bold;
      text-align: right !important;
    }
  }
}

label {
  font-weight: bold;
  margin: 1% 0;
  > div:nth-child(1) {
    font-weight: bold;
  }
}

// PrimeNG overrides

.p-icon {
  width: 0.875rem;
  height: 0.875rem;
}

p-inputnumber input {
  width: inherit;
}

p-speeddial > div {
  min-width: 48px;
}

.p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
  background-color: $secondary-color !important;
}

.p-breadcrumb-chevron {
  font-size: 14px !important;
  margin-top: 2px !important;
  color: #FFFFFF !important;
}

p-fieldset legend {
  height: 35px;
  > a {
    text-decoration: none;
    padding: 0.5rem !important;
  }
}

.p-tabview .p-tabview-panels {
  border: 0 !important;
  padding: 0 !important;
  background: none !important;
}

.p-tabview .p-tabview-nav {
  border: none !important;
}

.p-tabview-nav-container {
  margin-bottom: 4px;
}

.p-tabview-nav > li {
  outline: none !important;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background: none !important;
  box-shadow: none !important;
}

body .ui-tabs.ui-tabs-top .ui-tabs-nav li.ui-state-active,
body .ui-tabs.ui-tabs-bottom .ui-tabs-nav li.ui-state-active,
body .ui-tabs.ui-tabs-left .ui-tabs-nav li.ui-state-active,
body .ui-tabs.ui-tabs-right .ui-tabs-nav li.ui-state-active {
  background: white;
}

.p-tabview-nav-link {
  background: none !important;
  border-bottom: 5px solid $tab-unselected !important;
  color: $tab-unselected !important;
  outline-color: none !important;
}

.p-tabview-nav-link:focus-visible {
  outline: none !important;
}

.p-tabview-nav-link[aria-selected='true'] {
  color: $secondary-color !important;
  border-bottom: 5px solid $secondary-color !important;
}

// AG Grid overrides

.ag-header {
  background-color: $primary-color;
}

.ag-header-cell-label .ag-header-cell-text {
  white-space: normal !important;
  text-align: center;
  word-wrap: break-word;
}

.ag-cell-wrap-text {
  white-space: normal;
  word-break: break-word;
}

.ag-theme-alpine {
  /* use theme parameters where possible */
  --ag-header-background-color: #005792;
  --ag-header-foreground-color: white;

  .ag-header-cell-label, .ag-header-group-cell-label {
    justify-content: center;
  }

  .align-right-data {
    text-align: right;
  }

  .align-center-data {
    text-align: center;
  }

  .ag-icon-menu,
  .ag-icon-asc,
  .ag-icon-desc {
    color: white;
  }

  .ag-header-group-cell-no-group {
    background: white;
  }

  .ag-header-group-cell-with-group {
    background: $secondary-color;
    > div.ag-header-group-cell-label {
      justify-content: center;
    }
  }
}

.row-span-left-column {
  background-color: #fff !important;
  border: 1px solid #dde2eb !important;
  border-left: none !important;
  display: flex;
  justify-content: center; /* align horizontal */
  align-items: center;
  font-weight: bold;
}

.row-span-right-column {
  background-color: #fff !important;
  border-left: 1px solid #dde2eb !important;
  border-bottom: 1px solid #dde2eb !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.col-span {
  background-color: white  !important;
  border: none !important;
  text-align: end !important;
}

.footer-cell {
  border: none !important;
  background-color: $primary-color !important;;
  color: $primary-font-color !important;;
  font-weight: bold !important;;
}

/* Bootstrap Card Over-Writing + Custom */
.card-header {
  background-color: $primary-color;
  color: $primary-font-color;
}

.card-context {
  border-top: solid 1px black;
  border-left: solid 1px black;
  border-right: solid 1px black;
  background-color: #ffe599;
  font-weight: 500;
  padding: 0.5em;
  white-space: pre-line;
  line-height: 100%;
}

//.card-body {
//  border: solid 1px black;
//  padding: 1.4rem 0;
//}
/*-----------------------------*/

.mw-fit {
  max-width: fit-content;
}

.spinner {
  overflow-y: hidden;
}
