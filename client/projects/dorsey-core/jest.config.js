module.exports = {
  preset: "jest-preset-angular",
  setupFilesAfterEnv: ["<rootDir>/setup-jest.ts"],
  testEnvironment: "@happy-dom/jest-environment",
  moduleDirectories: ["node_modules", "<rootDir>/node_modules"],
  roots: ["<rootDir>/src"],
  testMatch: [
    "**/__tests__/**/*.+(ts|tsx|js)",
    "**/?(*.)+(spec|test).+(ts|tsx|js)",
  ],
  transform: {
    "^.+\\.(ts|js|mjs|html|svg)$": [
      "jest-preset-angular",
      {
        tsconfig: "<rootDir>/tsconfig.spec.json",
        stringifyContentPathRegex: "\\.(html|svg)$",
      },
    ],
  },
  transformIgnorePatterns: [
    "node_modules/(?!(.*\\.mjs$|@angular|@ngrx|ngx-.*|ag-grid-.*|rxjs))"
  ],
  moduleNameMapper: {
    "^slash$": "<rootDir>/src/__mocks__/slash.js",
    "^@dc-lib/(.*)$": "<rootDir>/src/lib/$1",
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
  },
};

// module.exports = {
//   preset: "jest-preset-angular",
//   // globalSetup: "jest-preset-angular/global-setup",
//   testEnvironment: "jsdom",
//   setupFilesAfterEnv: ["<rootDir>/setup-jest.ts"],
//   testPathIgnorePatterns: ["/node_modules/", "/dist/"],
//   testMatch: ["**/+(*.)+(spec|test).+(ts|js)?(x)"],
//   moduleFileExtensions: ["ts", "html", "js", "json", "mjs"],
//   coverageReporters: ["html"],
//   moduleDirectories: ["node_modules", "<rootDir>/node_modules"], //just necessary in Angular Custom Modules
//   snapshotSerializers: [
//     "jest-preset-angular/build/serializers/no-ng-attributes",
//     "jest-preset-angular/build/serializers/ng-snapshot",
//     "jest-preset-angular/build/serializers/html-comment",
//   ],
//   transformIgnorePatterns: ["node_modules/(?!.*\\.mjs$)"],
//   moduleNameMapper: {
//     // "^src/(.*)$": "<rootDir>/src/$1",
//     // "^app/(.*)$": "<rootDir>/src/app/$1",
//     // "^assets/(.*)$": "<rootDir>/src/assets/$1",
//     // "^environments/(.*)$": "<rootDir>/src/environments/$1",
//     "^@dc-lib/(.*)$": "<rootDir>/src/lib/$1",
//   },
//   transform: {
//     "^.+\\.(ts|js|mjs|html|svg)$": [
//       "jest-preset-angular",
//       {
//         tsconfig: "<rootDir>/tsconfig.spec.json",
//         stringifyContentPathRegex: "\\.(html|svg)$",
//       },
//     ],
//   },
// };

// const esModules = ["@angular", "tslib", "rxjs", "@ngrx", "d3"];
//
// module.exports = {
//   preset: "jest-preset-angular",
//   testEnvironment: "jsdom",
//   setupFilesAfterEnv: ["<rootDir>/setup-jest.ts"],
//   extensionsToTreatAsEsm: [".ts"],
//   coverageThreshold: {
//     global: {
//       branches: 80,
//       functions: 80,
//       lines: 80,
//       statements: 80,
//     },
//   },
//   transform: {
//     "^.+\\.(ts|js|mjs|html|svg)$": [
//       "ts-jest",
//       {
//         tsconfig: "<rootDir>/projects/dorsey-core/tsconfig.spec.json",
//         stringifyContentPathRegex: "\\.(html|svg)$",
//         isolatedModules: true,
//       },
//     ],
//   },
//   // transformIgnorePatterns: ["/node_modules/(?!@ionic|ngx-socket-io/).+\\.js$"],
//   transformIgnorePatterns: ["node_modules/(?!@angular|rxjs)"],
//   snapshotSerializers: [
//     "jest-preset-angular/build/serializers/no-ng-attributes",
//     "jest-preset-angular/build/serializers/ng-snapshot",
//     "jest-preset-angular/build/serializers/html-comment",
//   ],
// moduleNameMapper: {
//   "^(\\.{1,2}/.*)\\.js$": "$1",
// },
//   moduleNameMapper: {
//     "@app/(.*)": "<rootDir>/src/app/$1",
//     "@assets/(.*)": "<rootDir>/src/assets/$1",
//     "@core/(.*)": "<rootDir>/src/app/core/$1",
//     "@env": "<rootDir>/src/environments/environment",
//     "@src/(.*)": "<rootDir>/src/src/$1",
//     "@state/(.*)": "<rootDir>/src/app/state/$1",
//   },
// moduleNameMapper: {
//   "^@app/(.*)$": "<rootDir>/src/lib/$1",
//   "^@assets/(.*)$": "<rootDir>/src/lib/assets/$1",
// },
// moduleFileExtensions: ["ts", "html", "js", "json", "mjs"],
// collectCoverage: true,
// coverageReporters: ["html", "lcov", "text-summary"],
// coverageDirectory: "<rootDir>/src/lib/coverage/",
// collectCoverageFrom: [
//   "<rootDir>/src/lib/**/*.ts",
//   "!main.d.ts",
//   "!<rootDir>/src/lib/**/*.module.ts",
//   "!<rootDir>/src/lib/**/environment*.ts",
// ],
// testMatch: [
//   "<rootDir>/projects/dorsey-core/**/*.spec.ts",
//   "<rootDir>/src/**/*.spec.ts",
// ],
// verbose: true,
// silent: false,
// roots: ["<rootDir>/src/"],
// };
