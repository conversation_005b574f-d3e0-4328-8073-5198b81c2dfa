import {Inject, Injectable} from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import {DorseyConfiguration} from "../models/configuration";

@Injectable()
export class AuthExpiredInterceptor implements HttpInterceptor {
  protected baseURL: string;

  constructor(@Inject('config') private config: DorseyConfiguration) {
    this.baseURL = config.environment.apiUrl;
  }

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    return next.handle(request).pipe(
      tap({
        error: (err: HttpErrorResponse) => {
          console.log('error', err);
          // backend throws cors error when not logged in (Azure)
          // backend throws 200 code error when not logged in (Okta)
          if ((!err.ok && err.status === 0) || err.url.includes('okta-saml')) {
            location.href = `${this.baseURL}/login`;
          }
        }
      })
    );
  }
}
