import { IRole } from '../roles/role.model';
import { IHierarchyTree } from '../organization/hierarchy-tree';
import { IUserBillingInformation } from './user-billing-information.model';

export interface IUser {
  userId?: string;
  email?: string;
  isActive?: boolean;
  firstName?: string;
  lastName?: string;
  title?: string;
  profileImage?: File;
  phone?: string;
  mobile?: string;
  address?: string;
  hierarchyData?: any;
  effectiveDate?: Date;
  terminationDate?: Date;
  roles?: IRole[];
  userBillingInformation?: IUserBillingInformation;
  notificationConfig?: any[];
}

export class User implements IUser {
  constructor(
    public userId?: string,
    public email?: string,
    public isActive?: boolean,
    public firstName?: string,
    public lastName?: string,
    public title?: string,
    public profileImage?: File,
    public phone?: string,
    public mobile?: string,
    public address?: string,
    public hierarchyData?: any,
    public effectiveDate?: Date,
    public terminationDate?: Date,
    public roles?: IRole[],
    public notificationConfig?: any[]
  ) {}
}
