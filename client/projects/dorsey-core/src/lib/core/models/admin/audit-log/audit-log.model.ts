export interface IAuditLog {
  auditId?: string;
  userId?: string;
  actionCode?: string;
  entityCode?: string;
  entityKey?: string;
  auditDate?: string;
  entityValue?: { type: string, value: string, null: boolean };
}

export class AuditLog implements IAuditLog {
  constructor(
    public auditId?: string,
    public userId?: string,
    public actionCode?: string,
    public entityCode?: string,
    public entityKey?: string,
    public auditDate?: string,
    public entityValue?: { type: string, value: string, null: boolean }
  ) {
  }
}
