import { Theme } from '../configuration';

export class Themes {
  static readonly THEME1: Theme = {
    number: 1,
    colors: ['#3A5976', '#0086ad', '#A9C3C5', '#DDDDDD'],
  };
  static readonly THEME2: Theme = {
    number: 2,
    colors: ['#3A5976', '#0086ad', '#A9C3C5', '#DDDDDD'],
  };
  static readonly THEME3: Theme = {
    number: 3,
    colors: ['#29B6F6', '#0089DC', '#F0F0F0', '#DDDDDD'],
  };
  static readonly THEME4: Theme = {
    number: 4,
    colors: ['#0a70a4', '#5C8630', '#FCFDFF', '#EFF4FF'],
  };
}

export const ThemesColors = Object.freeze([
  Themes.THEME1.colors,
  Themes.THEME2.colors,
  Themes.THEME3.colors,
  Themes.THEME4.colors,
]);
