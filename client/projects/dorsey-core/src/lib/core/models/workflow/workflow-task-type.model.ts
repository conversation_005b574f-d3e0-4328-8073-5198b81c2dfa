import { IWorkflow } from './workflow.model';
import { IWorkflowType } from './workflow-type.model';

export interface IWorkflowTaskType {
  taskId?: string;
  workflow?: IWorkflow;
  taskName?: string;
  isDecision?: boolean;
  isDataBased?: boolean;
  relevantDataField?: string;
  relevantDataValue?: string;
  comparisonOperator?: string;
  fields?: any[];
  hasEmail?: boolean;
  emailTo?: string;
  emailSubject?: string;
  emailBody?: string;
  triggerWorkflow?: boolean;
  workflowToTrigger?: IWorkflowType;
  hasDirectory?: boolean;
  serviceLevelAgreement?: number;
  serviceLevelAgreementWarning?: number;
  description?: string;
  assignedTo?: any[];
  completed?: boolean;
}
