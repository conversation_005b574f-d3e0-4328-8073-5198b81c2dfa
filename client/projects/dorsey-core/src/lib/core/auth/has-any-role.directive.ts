import {
  Directive,
  Input,
  TemplateRef,
  ViewContainerRef,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AccountService } from './account.service';
import { RoleActions } from '../models/enums/role-actions';

/**
 * @whatItDoes Conditionally includes an HTML element if current user has any
 * of the roles passed as the `expression`.
 *
 * @howToUse
 * ```
 *     <some-element *hasAnyRole="'AD'">...</some-element>
 *
 *     <some-element *hasAnyRole="['AD', 'SH']">...</some-element>
 * ```
 */
@Directive({
  selector: '[hasAnyRole]',
})
export class HasAnyRoleDirective implements OnInit, OnDestroy {
  @Input('hasAnyRole')
  action: RoleActions;

  @Input('hasAnyRolePath')
  set path(value: string) {
    this._path = value;
  }

  private _path: string;
  private readonly destroy$ = new Subject<void>();

  constructor(
    private accountService: AccountService,
    private templateRef: TemplateRef<any>,
    private viewContainerRef: ViewContainerRef
  ) {}

  ngOnInit() {
    this.updateView();
    // Get notified each time authentication state changes.
    this.accountService
      .getAuthenticationState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateView(): void {
    const hasAnyAuthority = this.accountService.hasAnyRole(
      this.action,
      this._path
    );
    this.viewContainerRef.clear();
    if (hasAnyAuthority) {
      this.viewContainerRef.createEmbeddedView(this.templateRef);
    }
  }
}
