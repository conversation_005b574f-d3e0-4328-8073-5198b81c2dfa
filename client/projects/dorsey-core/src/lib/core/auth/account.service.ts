import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Observable, ReplaySubject, of } from 'rxjs';
import { shareReplay, tap, catchError } from 'rxjs/operators';
import { UserService } from '../services/admin/user.service';
import { Account } from './account.model';
import { IUser } from '../models/admin/users/user.model';
import { RoleActions } from '../models/enums/role-actions';

@Injectable()
export class AccountService {
  private userIdentity: IUser | null = null;
  private authenticationState = new ReplaySubject<IUser | null>(1);
  private accountCache$?: Observable<Account> | null;

  constructor(
    private http: HttpClient,
    private router: Router,
    private userService: UserService
  ) {}

  authenticate(identity: Account | null): void {
    this.userIdentity = identity;
    this.authenticationState.next(this.userIdentity);
    if (!identity) {
      this.accountCache$ = null;
    }
  }

  hasAnyRole(action: RoleActions, component: string): boolean {
    if (!this.userIdentity) {
      return false;
    }

    component = component?.substring(1, component.length).replace(/\//gi, ',');
    return this.userIdentity.roles
      .flatMap((r) => r.capabilities)
      .filter((r) => r.component.join(',') === component)
      .some((r) => r?.[action]);
  }

  identity(force?: boolean): Observable<IUser | null> {
    if (!this.accountCache$ || force) {
      this.accountCache$ = this.fetch().pipe(
        tap((account: Account) => {
          this.authenticate(account);
        }),
        shareReplay()
      );
    }
    return this.accountCache$.pipe(catchError(() => of(null)));
  }

  isAuthenticated(): boolean {
    return this.userIdentity !== null;
  }

  getAuthenticationState(): Observable<IUser | null> {
    return this.authenticationState.asObservable();
  }

  gerCurrentUser(): IUser {
    return this.userIdentity;
  }

  private fetch(): Observable<IUser> {
    return this.userService.findLoggedUserInfo();
  }
}
