import { <PERSON><PERSON><PERSON>w<PERSON>nit, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Edge, Node } from '@swimlane/ngx-graph';

import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { IWorkflow } from '../../../../models/workflow/workflow.model';
import { RoleActions } from '../../../../models/enums/role-actions';
import { EditingStateService } from '../../../../services/editing-state.service';
import { ToastService } from '../../../../services/toast.service';
import { WorkflowService } from '../../../../services/workflow/workflow.service';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { FormAction } from '../../../../models/form-action';
import { handleCancelAction } from '../../../../utils/grid-utils';
import { IWorkflowData } from '../../../../models/workflow/workflow-data.model';

interface Item {
  code: string;
  name: string;
}

@Component({
  selector: 'cms-workflow-definition',
  templateUrl: './workflow-definition.component.html',
  styleUrls: ['./workflow-definition.component.scss'],
})
export class WorkflowDefinitionComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  readonly NODE_DIAMETER = 95;
  readonly INCOMPLETE_NODE_BACKGROUND_COLOR = '#DDDDDD';
  readonly COMPLETE_NODE_BACKGROUND_COLOR = '#D6CDEA';
  readonly WAIT_GRAPH_MS = 1000;

  currentWorkflow: IWorkflow;
  currentWorkflowItem: Item;
  workflowItems: Item[] = [];

  update$: Subject<any> = new Subject();
  center$: Subject<any> = new Subject();
  zoomToFit$: Subject<any> = new Subject();
  // zoomOk: boolean;
  zoomOk = true;
  canAdjust = true;

  actions = RoleActions;

  tasks: string[] = [
    'Estimate (Operations) review',
    'Sales Review',
    'Rejected',
    'CEO Review',
    'Awaiting Bid Results',
    'Bid Lost',
    'Bid Won',
  ];

  selectedTask: any;
  prevTask: any;

  filteredNodes: any[] | undefined;

  inputNodes: any[] = [];
  outputNodes: any[] = [];

  public nodes: Node[] = [];

  public links: Edge[] = [];

  errors: string[] = [];
  currVersion: number;

  constructor(
    public editingStateService: EditingStateService,
    private toastService: ToastService,
    private workflowService: WorkflowService,
    private dialogMessageService: DialogMessageService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        if (FormAction.SAVE === value) {
          this.saveData();
        }
        if (FormAction.EDIT === value) {
          this.editingStateService.setData([
            ['inputNodes', JSON.parse(JSON.stringify(this.inputNodes))],
          ]);
          this.editingStateService.setData([
            ['outputNodes', JSON.parse(JSON.stringify(this.outputNodes))],
          ]);
          this.editingStateService.setData([
            ['nodes', JSON.parse(JSON.stringify(this.nodes))],
          ]);
          this.editingStateService.setData([
            ['links', JSON.parse(JSON.stringify(this.links))],
          ]);
        }
      });
    handleCancelAction(this.editingStateService, this).subscribe(() => {});
  }

  ngOnInit(): void {
    this.loadWorkflowData();
  }

  private loadWorkflowData() {
    this.loadWorkflow().subscribe(
      (resp) => {
        this.currentWorkflow = resp;
        this.currVersion = this.currentWorkflow.version;
        this.loadNodes();
        this.workflowService
          .findWorkflowsByType(this.currentWorkflow.workflowType.id)
          .subscribe(
            (resp) => {
              this.workflowItems = resp.map((w) => {
                return { code: w.id, name: w.version.toString() };
              });
              this.currentWorkflowItem = {
                code: this.currentWorkflow.id,
                name: this.currentWorkflow.version.toString(),
              };
            },
            () => this.toastService.displayError('Workflows not found.')
          );
      },
      () => this.toastService.displayError('Workflow not found.')
    );
  }

  private loadWorkflow(): Observable<any> {
    if (this.route.snapshot.queryParams['version']) {
      return this.workflowService.findWorkflowByVersionAndType(
        +this.route.snapshot.queryParams['version'],
        this.currentWorkflow.workflowType.name
      );
    } else {
      return this.workflowService
        .findWorkflowByName(window.location.pathname.split('/').pop())
        .pipe(takeUntil(this.destroy$));
    }
  }

  onVersionChange(id: string) {
    history.replaceState(
      null,
      null,
      `${window.location.pathname}?version=${this.currentWorkflowItem.name}`
    );
    this.router.navigate(
      [`${window.location.pathname}?version=${this.currentWorkflowItem.name}`],
      {
        skipLocationChange: true,
      }
    );

    this.workflowService
      .findById(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        (resp) => {
          this.currentWorkflow = resp;
          this.loadNodes();
        },
        () => this.toastService.displayError('Workflow not found.')
      );
  }

  loadNodes() {
    this.workflowService
      .findWorkflowData(this.currentWorkflow.id)
      .subscribe((resp: IWorkflowData) => {
        this.nodes = [];
        this.links = [];
        this.inputNodes = [{ code: 'Start', name: 'Start' }];

        if (resp?.taskTypes?.length) {
          for (const t of resp.taskTypes) {
            if (t.taskName === 'Start') {
              this.nodes.push({
                id: 'Start',
                label: 'Start',
                data: {
                  taskId: t.taskId,
                  shape: 'circle',
                  backgroundColor: this.COMPLETE_NODE_BACKGROUND_COLOR,
                },
              });
            } else if (t.taskName === 'End') {
              this.nodes.push({
                id: 'End',
                label: 'End',
                data: {
                  taskId: t.taskId,
                  shape: 'circle',
                  backgroundColor: this.COMPLETE_NODE_BACKGROUND_COLOR,
                },
              });
            } else {
              this.nodes.push({
                id: t.taskName,
                label: t.taskName,
                data: {
                  taskId: t.taskId,
                  shape: 'rectangle',
                  backgroundColor: t.completed
                    ? this.COMPLETE_NODE_BACKGROUND_COLOR
                    : this.INCOMPLETE_NODE_BACKGROUND_COLOR,
                },
              });
              this.inputNodes.push({ code: t.taskName, name: t.taskName });
            }
          }

          for (const t of resp.relations) {
            this.links.push({
              id: `n${t.id}`,
              source: t.input,
              target: t.output,
            });
          }
        } else {
          this.nodes.push(
            {
              id: 'Start',
              label: 'Start',
              data: {
                shape: 'circle',
                backgroundColor: this.COMPLETE_NODE_BACKGROUND_COLOR,
              },
            },
            {
              id: 'End',
              label: 'End',
              data: {
                shape: 'circle',
                backgroundColor: this.COMPLETE_NODE_BACKGROUND_COLOR,
              },
            }
          );
        }

        this.update$.next(true);
      });
  }

  private saveData() {
    if (this.validateData()) {
      let workflowData: IWorkflowData = { taskTypes: [], relations: [] };

      workflowData.workflowId = this.currentWorkflow.id;

      for (const n of this.nodes) {
        workflowData.taskTypes.push({
          taskId: n.data?.taskId,
          taskName: n.label,
          isDecision: n.data?.isDecision,
          description: n.label,
        });
      }

      for (const l of this.links) {
        workflowData.relations.push({
          workflowId: this.currentWorkflow.id,
          input: l.source,
          output: l.target,
        });
      }

      this.workflowService
        .updateWorkflowData(workflowData)
        .pipe(takeUntil(this.destroy$))
        .subscribe(
          () => {
            this.editingStateService.setValue(FormAction.SUBMIT);
            this.toastService.displaySuccess(
              'Workflow data updated successfully.'
            );

            this.loadWorkflowData();
          },
          () => this.toastService.displayError('Failed updating workflow data.')
        );
    }
  }

  private validateData(): boolean {
    for (const n of this.nodes) {
      if (
        !this.links.some((l) => l.source === n.id) &&
        n.id !== 'Start' &&
        n.id !== 'End'
      ) {
        this.errors.push(`${n.id} must has a Output.`);
      }
      if (
        !this.links.some((l) => l.target === n.id) &&
        n.id !== 'Start' &&
        n.id !== 'End'
      ) {
        this.errors.push(`${n.id} must has a Input.`);
      }
    }

    if (this.errors.length) {
      this.dialogMessageService.displayError(this.errors.join('\n'), false);
      this.errors = [];
      return false;
    }

    return !this.errors.length;
  }

  onNodeSelect(object) {
    this.selectedTask = object;
    this.prevTask = object.id;
  }

  filterNode(event: AutoCompleteCompleteEvent) {
    if (this.selectedTask) {
      this.prevTask = JSON.parse(JSON.stringify(this.selectedTask));
    }

    const nodes = [...this.nodes].filter(
      (n) => n.label !== 'Start' && n.label !== 'End'
    );

    let filtered: any[] = [];
    let query = event.query;

    for (let n of nodes) {
      if (n.label.toLowerCase().indexOf(query.toLowerCase()) == 0) {
        filtered.push(n);
      }
    }

    this.filteredNodes = filtered;
  }

  createNode() {
    if (this.selectedTask.includes('/')) {
      this.dialogMessageService.displayError(
        `Task cannot contain Forward Slash <b>"/"</b>.`
      );
      return;
    }
    if (this.nodes.some((n) => n.id === this.selectedTask)) {
      this.dialogMessageService.displayError(
        `Task "<b>${this.selectedTask}</b>" already exist.`
      );
      return;
    }
    if (!this.selectedTask.trim()) {
      this.dialogMessageService.displayError(`Task cannot be empty.`);
      return;
    }
    this.nodes.push({
      id: this.selectedTask,
      label: this.selectedTask,
      data: {
        shape: 'rectangle',
        backgroundColor: this.INCOMPLETE_NODE_BACKGROUND_COLOR,
      },
    });

    this.inputNodes.push({ code: this.selectedTask, name: this.selectedTask });
    this.update$.next(true);

    // this.newState = null;
  }

  removeNode() {
    this.nodes = this.nodes.filter((n) => n.id !== this.prevTask);

    this.links = this.links.filter((l) => l.source !== this.prevTask);
    this.links = this.links.filter((l) => l.target !== this.prevTask);

    this.inputNodes = this.inputNodes.filter((i) => i.code !== this.prevTask);

    this.update$.next(true);
  }

  onInputChange(value) {
    if (value !== null) {
      this.outputNodes = [
        { code: 'End', name: 'End' },
        ...this.inputNodes.filter(
          (i) => i.code !== value && i.code !== 'Start'
        ),
      ];
    }
  }

  linkNodes(from: string, to: string) {
    if (this.links.some((l) => l.source === from && l.target === to)) {
      this.toastService.displayError(
        `The relation "${from} -> ${to}" already exist.`
      );
    } else if (from === 'Start' && to === 'End') {
      this.toastService.displayError(
        `The workflow should contain at least one task.`
      );
    } else if (
      this.links.some((l) => l.source === 'Start') &&
      from === 'Start'
    ) {
      this.toastService.displayError(`Start must have one transition only.`);
    } else {
      this.links.push({
        id: `n${this.idGen().toString()}`,
        source: from,
        target: to,
      });
      this.update$.next(true);
    }
  }

  unLinkNode(from: string, to: string) {
    if (this.links.some((l) => l.source === from && l.target === to)) {
      if (this.links.length > 1) {
        if (this.links.filter((l) => l.source === from).length === 2) {
          this.nodes.find((n) => n.label === from).data.isDecision = false;
        }

        this.links = this.links.filter(
          (l) => l.source !== from || l.target !== to
        );
      } else {
        this.links = [];
      }

      this.update$.next(true);
    } else {
      this.toastService.displayError(
        `The relation "${from} -> ${to}" doesn't exist.`
      );
    }
  }

  idGen(): number {
    if (this.links.length) {
      return (
        Math.max(...this.links.map((l) => +l.id.substring(1, l.id.length))) + 1
      );
    } else {
      return 1;
    }
  }

  adjustGraph(zoomChanged: boolean) {
    if (this.canAdjust) {
      this.zoomOk = false;
      setTimeout(() => {
        this.update$.next(true);
        this.center$.next(true);
        this.zoomToFit$.next(true);
        this.zoomOk = true;
      }, this.WAIT_GRAPH_MS);
    }

    // Prevent to adjust graph when still in process.
    if (zoomChanged && this.canAdjust) {
      this.canAdjust = false;
      setTimeout(() => {
        this.canAdjust = true;
      }, this.WAIT_GRAPH_MS);
    }
  }

  onDblClickNode(node) {
    if (!this.editingStateService.editingState.isEditing) {
      this.router.navigate(
        [
          'admin',
          'workflow',
          'manage-workflow',
          this.currentWorkflow.workflowType.name,
          node.id,
        ],
        {
          queryParams: {
            version: +this.currentWorkflowItem.name,
          },
        }
      );
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

interface AutoCompleteCompleteEvent {
  originalEvent: Event;
  query: string;
}
