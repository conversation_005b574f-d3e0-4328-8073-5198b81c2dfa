import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { WorkflowsComponent } from './workflows/workflows.component';
import { WorkflowDefinitionComponent } from './workflows/workflow-definition/workflow-definition.component';
import { TaskDefinitionComponent } from './workflows/workflow-definition/task-definition/task-definition.component';
import { CoreModule } from '../../core.module';
import { StepsModule } from 'primeng/steps';
import { DividerModule } from 'primeng/divider';
import { MultiSelectModule } from 'primeng/multiselect';
import { AccordionModule } from 'primeng/accordion';
import { DragDropModule } from 'primeng/dragdrop';
import { InputNumberModule } from 'primeng/inputnumber';
import { ChipsModule } from 'primeng/chips';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { NgxGraphModule } from '@swimlane/ngx-graph';
import { ManageWorkflowComponent } from './manage-workflow.component';
import { ManageWorkflowRoutingModule } from './manage-workflow-routing.module';
import { WorkflowHistoryComponent } from './workflow-history/workflow-history.component';
import { CalendarModule } from 'primeng/calendar';
import { InputTextareaModule } from 'primeng/inputtextarea';

@NgModule({
  declarations: [
    WorkflowsComponent,
    WorkflowDefinitionComponent,
    TaskDefinitionComponent,
    ManageWorkflowComponent,
    WorkflowHistoryComponent,
  ],
  imports: [
    ManageWorkflowRoutingModule,
    CommonModule,
    FormsModule,
    CoreModule,
    StepsModule,
    DividerModule,
    ReactiveFormsModule,
    MultiSelectModule,
    AccordionModule,
    DragDropModule,
    InputNumberModule,
    ChipsModule,
    CardModule,
    DialogModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
    NgxGraphModule,
    CalendarModule,
    InputTextareaModule,
  ],
})
export class ManageWorkflowModule {}
