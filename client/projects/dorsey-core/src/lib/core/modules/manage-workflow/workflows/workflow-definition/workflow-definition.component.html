<div class="d-flex flex-column p-3 h-100">
  <div class="text-end mb-2">
    <div class="action-button-container justify-content-between">
      <div class="d-flex">
        <label class="me-2 d-flex align-self-center">Version: </label>
        <p-dropdown
          class="align-self-center me-3 d-flex"
          [options]="workflowItems"
          [(ngModel)]="currentWorkflowItem"
          (onChange)="onVersionChange($event.value.code)"
          placeholder="Select"
          optionLabel="name"
        >
        </p-dropdown>
      </div>
      <dorsey-edition-ctas
        *hasAnyRole="
          actions.EDIT;
          path: '/admin/workflow/manage-workflow/:workflow'
        "
        class="col-3 p-0 text-end"
        [canEdit]="true"
        [editionDisabled]="currentWorkflow?.version !== currVersion"
      >
      </dorsey-edition-ctas>
    </div>
  </div>
  <p-panel>
    <ng-template pTemplate="header">
      <div class="row col-12 m-0">
        <div class="row p-0 text-end apply-gap justify-content-between">
          <div class="col-4 row p-0 m-0 align-items-center">
            <div class="col-2 text-end">Tasks:</div>
            <div class="col-10 p-0">
              <div class="row">
                <p-autoComplete
                  class="w-auto d-flex flex-grow-1 pe-0"
                  field="label"
                  placeholder="Type a task name"
                  [maxlength]="50"
                  [(ngModel)]="selectedTask"
                  [dropdown]="true"
                  [showClear]="true"
                  [suggestions]="filteredNodes"
                  (onSelect)="onNodeSelect($event)"
                  (completeMethod)="filterNode($event)"
                  [disabled]="!editingStateService.editingState.isEditing"
                  (onClear)="removeNode()"
                ></p-autoComplete>
                <p-button
                  id="add-node"
                  class="w-auto ps-0"
                  [ngbTooltip]="
                    editingStateService.editingState.isEditing
                      ? 'Add Task'
                      : null
                  "
                  container="body"
                  (onClick)="
                    createNode();
                    inPutDropdown.writeValue(null);
                    outPutDropdown.writeValue(null)
                  "
                  [disabled]="!editingStateService.editingState.isEditing"
                  ><i class="fa fa-plus"></i
                ></p-button>
              </div>
            </div>
          </div>
          <div class="col-8 row p-0 align-items-center">
            <div class="col-5 p-0">
              <p-dropdown
                #inPutDropdown
                [options]="inputNodes"
                placeholder="Select Input"
                optionLabel="name"
                optionValue="code"
                (onChange)="onInputChange($event.value)"
                [disabled]="!editingStateService.editingState.isEditing"
              ></p-dropdown>
            </div>
            <div class="arrow-container p-0 text-center">
              <i
                class="fa fa-long-arrow-right d-inline arrow"
                aria-hidden="true"
              ></i>
            </div>
            <div class="col-5 p-0">
              <p-dropdown
                #outPutDropdown
                [options]="outputNodes"
                placeholder="Select Output"
                optionLabel="name"
                optionValue="code"
                [disabled]="!editingStateService.editingState.isEditing"
              ></p-dropdown>
              <!--                            <p-autoComplete class="w-100" field="label" placeholder="Output..." [(ngModel)]="selectedTask" [dropdown]="true" [suggestions]="tasks" (onSelect)="onNodeSelect($event)" [disabled]="!editingStateService.editingState.isEditing"></p-autoComplete>-->
            </div>
            <div class="col-2 p-0 w-auto">
              <div class="row m-0">
                <p-button
                  id="remove-node-relation"
                  class="w-auto p-0"
                  styleClass="p-button-danger"
                  [ngbTooltip]="
                    editingStateService.editingState.isEditing
                      ? 'Remove Transition'
                      : null
                  "
                  container="body"
                  [disabled]="
                    !editingStateService.editingState.isEditing ||
                    !inPutDropdown.value ||
                    !outPutDropdown.value
                  "
                  (onClick)="
                    unLinkNode(inPutDropdown.value, outPutDropdown.value);
                    inPutDropdown.writeValue(null);
                    outPutDropdown.writeValue(null)
                  "
                  ><i class="fa fa-minus"></i
                ></p-button>
                <p-button
                  id="add-node-relation"
                  class="w-auto p-0"
                  [ngbTooltip]="
                    editingStateService.editingState.isEditing
                      ? 'Add Transition'
                      : null
                  "
                  container="body"
                  [disabled]="
                    !editingStateService.editingState.isEditing ||
                    !inPutDropdown.value ||
                    !outPutDropdown.value
                  "
                  (onClick)="
                    linkNodes(inPutDropdown.value, outPutDropdown.value);
                    inPutDropdown.writeValue(null);
                    outPutDropdown.writeValue(null)
                  "
                  ><i class="fa fa-plus"></i
                ></p-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-template>
    <div>
      <div>
        <div class="graph-space">
          <div
            [style.display]="!zoomOk ? 'flex' : 'none !important'"
            class="w-100 h-100 d-flex align-items-center justify-content-center"
          >
            <p-progressSpinner
              [style]="{ width: '50px', height: '50px', zIndex: 10000 }"
              styleClass="custom-spinner"
              strokeWidth="8"
              animationDuration=".5s"
            ></p-progressSpinner>
          </div>
          <ngx-graph
            layout="dagre"
            [style.visibility]="zoomOk ? 'visible' : 'hidden'"
            [links]="links"
            [nodes]="nodes"
            [draggingEnabled]="false"
            [update$]="update$"
            [center$]="center$"
            [zoomToFit$]="zoomToFit$"
            [layoutSettings]="{
              orientation: 'LR',
              marginX: 5,
              marginY: 5,
              edgePadding: 100,
              rankPadding: 70,
              nodePadding: 5,
              multigraph: true,
              compound: true,
              align: 'UL'
            }"
          >
            <ng-template #defsTemplate>
              <svg:marker
                id="arrow"
                viewBox="0 -5 10 10"
                refX="8"
                refY="0"
                markerWidth="4"
                markerHeight="4"
                orient="auto"
              >
                <svg:path d="M0,-5L10,0L0,5" class="arrow-head" />
              </svg:marker>
            </ng-template>

            <ng-template #nodeTemplate let-node>
              <svg:g
                class="node"
                (dblclick)="onDblClickNode(node)"
                xmlns="http://www.w3.org/2000/xhtml"
                width="150"
                height="100"
                *ngIf="node.data.shape === 'rectangle'"
              >
                <svg:foreignObject width="150" height="100">
                  <xhtml:div
                    class="card-container"
                    xmlns="http://www.w3.org/1999/xhtml"
                    [ngStyle]="{
                      'background-color': node.data.backgroundColor
                    }"
                  >
                    <label class="name">{{ node.label }}</label>
                  </xhtml:div>
                </svg:foreignObject>
              </svg:g>
              <svg:g
                class="node"
                xmlns="http://www.w3.org/2000/xhtml"
                *ngIf="node.data.shape === 'circle'"
              >
                <svg:circle
                  [attr.cx]="NODE_DIAMETER / 2"
                  [attr.cy]="NODE_DIAMETER / 2"
                  [attr.r]="NODE_DIAMETER / 2"
                  [attr.fill]="node.data.backgroundColor"
                />
                <svg:text
                  alignment-baseline="central"
                  text-anchor="middle"
                  font-weight="bold"
                  [attr.x]="45"
                  [attr.y]="node.dimension.height / 2"
                >
                  {{ node.label }}
                </svg:text>
              </svg:g>
            </ng-template>

            <ng-template #linkTemplate let-link>
              <svg:g class="edge">
                <svg:path
                  class="line"
                  stroke-width="2"
                  marker-end="url(#arrow)"
                ></svg:path>
                <svg:text class="edge-label" text-anchor="middle">
                  <textPath
                    class="text-path"
                    [attr.href]="'#' + link.id"
                    [style.dominant-baseline]="link.dominantBaseline"
                    startOffset="50%"
                  >
                    {{ link.label }}
                  </textPath>
                </svg:text>
              </svg:g>
            </ng-template>
          </ngx-graph>
        </div>
      </div>
    </div>
  </p-panel>
</div>
