import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AuthGuard } from '../../guards/auth.guard';
import { EditingCheckGuard } from '../../guards/editing-check.guard';
import { WorkflowComponent } from './workflow.component';
import { WorkflowPanelComponent } from './workflow-panel/workflow-panel.component';
import { TaskDetailComponent } from '../../../dorsey-core.main';

const parentPath = 'workflow';

const routes: Routes = [
  {
    path: '',
    title: 'Workflow',
    canActivate: [AuthGuard],
    canDeactivate: [EditingCheckGuard],
    component: WorkflowComponent,
    data: { parentPath },
    children: [
      { path: '', component: WorkflowPanelComponent },
      {
        path: 'task-detail',
        children: [{ path: '', component: TaskDetailComponent }],
        canActivate: [AuthGuard],
        canDeactivate: [EditingCheckGuard],
        title: 'Task Detail',
        data: {
          breadcrumb: {
            routerLink: `${parentPath}/workflow/task-detail`,
            label: 'Task Detail',
          },
        },
      },
    ],
  },
];

export const workflowRoutes: Routes = [
  {
    path: 'workflow',
    children: [
      { path: '', component: WorkflowPanelComponent },
      {
        path: 'task-detail',
        children: [{ path: '', component: TaskDetailComponent }],
        canActivate: [AuthGuard],
        canDeactivate: [EditingCheckGuard],
        title: 'Task Detail',
        data: {
          breadcrumb: {
            routerLink: `home/workflow/task-detail`,
            label: 'Task Detail',
          },
        },
      },
    ],
    canActivate: [AuthGuard],
    canDeactivate: [EditingCheckGuard],
    title: 'Workflow',
    data: {
      breadcrumb: {
        icon: 'fa fa-refresh',
        routerLink: `home/workflow`,
        label: 'Workflow',
      },
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class WorkflowRoutingModule {}
