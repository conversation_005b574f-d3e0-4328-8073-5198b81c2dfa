import { Component, OnInit } from '@angular/core';
import { <PERSON>rid<PERSON><PERSON>, Column<PERSON><PERSON> } from 'ag-grid-community';
import { formatDate, TitleCasePipe } from '@angular/common';
import { catchError, forkJoin, Observable, of } from 'rxjs';
import { Router } from '@angular/router';
import { cloneDeep } from 'lodash';
import { TaskLocation } from '../../../models/enums/workflow/task-location.enum';
import { IWorkflowTask } from '../../../models/workflow/workflow-task.model';
import { IWorkflowTaskType } from '../../../models/workflow/workflow-task-type.model';
import { WorkflowService } from '../../../services/workflow/workflow.service';
import { ToastService } from '../../../services/toast.service';
import { LovService } from '../../../services/lov/lov.service';
import { TaskSlaColors } from '../../../models/enums/home/<USER>';
import { DataGridMessage } from '../../../components/datagrid/models/datagrid-message.model';
import { DatagridActionsCta } from '../../../components/datagrid/models/enums/datagrid-actions-cta';
import { Workflow } from '../../../models/enums/home/<USER>';

@Component({
  selector: 'dorsey-workflow',
  templateUrl: './workflow-panel.component.html',
  styleUrls: ['./workflow-panel.component.scss'],
})
export class WorkflowPanelComponent implements OnInit {
  expanded = [false, false, false];
  expandedChartIds = ['assigned-pie', 'initiated-pie', 'completed-pie'];
  taskLocation = TaskLocation;

  assignedChartData: any;
  initiatedChartData: any;
  completedChartData: any;

  assignedTasks: IWorkflowTask[];
  taskTypes: IWorkflowTaskType[];

  chartOptions = {
    plugins: {
      align: 'end',
      legend: {
        display: false,
      },
    },
    onClick: (e, activeEls) => {
      let dataIndex = activeEls[0].index;
      let label = e.chart.data.labels[dataIndex];
      if (
        this.expanded[
          this.expandedChartIds.indexOf(
            e.native.target.parentElement.parentElement.id
          )
        ]
      ) {
        this.filterGrid(label);
      }
    },
  };

  gridAssignedApi: GridApi;
  gridInitiatedApi: GridApi;
  gridCompletedApi: GridApi;
  columnAssignedApi: ColumnApi;
  tasks: IWorkflowTask[] = [];
  taskData: [IWorkflowTask[], IWorkflowTask[], IWorkflowTask[]] = [[], [], []];
  columnDefs: any[];
  completedColumnDefs: any[];
  searchValue: string;
  gridPageSize = 10;

  constructor(
    private titleCasePipe: TitleCasePipe,
    private workflowService: WorkflowService,
    private toastService: ToastService,
    private router: Router,
    private lovService: LovService
  ) {}

  ngOnInit(): void {
    // this.setColumnDefs();
    this.loadData();
  }

  private filterGrid(label: string) {
    let filter: any;

    switch (label) {
      case 'Expiring':
        filter = {
          color: { values: [TaskSlaColors.YELLOW], filterType: 'set' },
        };
        break;
      case 'Expired':
        filter = {
          color: { values: [TaskSlaColors.RED], filterType: 'set' },
        };
        break;
      case 'Approved':
        filter = {
          approved: { values: ['Approved'], filterType: 'set' },
        };
        break;
      case 'Rejected':
        filter = {
          approved: { values: ['Rejected'], filterType: 'set' },
        };
        break;
      default:
        filter = {
          color: { values: [TaskSlaColors.GREEN], filterType: 'set' },
        };
        break;
    }

    this.gridAssignedApi?.setFilterModel(filter);
    this.gridInitiatedApi?.setFilterModel(filter);
    this.gridCompletedApi?.setFilterModel(filter);
  }

  clearFilter(grid: number) {
    if (this.expanded[grid]) {
      this.gridAssignedApi?.setFilterModel(null);
      this.gridInitiatedApi?.setFilterModel(null);
      this.gridCompletedApi?.setFilterModel(null);
    }
  }

  expandPane(pane: TaskLocation, expanded: boolean, dataIndex: number) {
    if (expanded) {
      const tasks = this.taskData[dataIndex];

      this.tasks = tasks;
    }

    switch (pane) {
      case TaskLocation.ASSIGNED:
        this.expanded = [expanded, false, false];
        break;
      case TaskLocation.INITIATED:
        this.expanded = [false, expanded, false];
        break;
      case TaskLocation.COMPLETED:
        this.expanded = [false, false, expanded];
        break;
    }
  }

  onAssignedGridIsReady(gridApi: GridApi) {
    this.gridAssignedApi = gridApi;
  }

  onAssignedColumnIsReady(columnApi: ColumnApi) {
    this.columnAssignedApi = columnApi;
  }

  onInitiatedGridIsReady(gridApi: GridApi) {
    this.gridInitiatedApi = gridApi;
  }

  onCompletedGridIsReady(gridApi: GridApi) {
    this.gridCompletedApi = gridApi;
  }

  private setColumnDefs() {
    this.columnDefs = [
      {
        field: 'color',
        minWidth: 175,
        hide: true,
      },
      {
        field: 'createdDate',
        minWidth: 175,
        headerName: 'Date',
        valueFormatter: (params) => {
          return params.value
            ? formatDate(params.value, 'MM/dd/yyyy HH:mm:ss', 'en-US')
            : null;
        },
      },
      {
        field: 'entityName',
        minWidth: 125,
      },
      {
        field: 'taskType.workflow.workflowType.name',
        headerName: 'Workflow',
        minWidth: 175,
        valueGetter: (params) =>
          params.data.taskType.workflow.workflowType.name,
      },
      {
        field: 'taskType.taskName',
        headerName: 'Task',
        minWidth: 175,
        valueGetter: (params) => params.data.taskType.taskName,
      },
      {
        field: 'createdBy',
        headerName: 'From',
        minWidth: 250,
        valueGetter: (params) =>
          `${params.data.createdBy.firstName} ${params.data.createdBy.lastName}`,
      },
      {
        field: 'assignedTo',
        headerName: 'Assigned to',
        minWidth: 150,
        valueGetter: (params) =>
          params.data.taskType.assignedTo
            .map((a) => '- ' + a.role.name)
            .join('\n'),
      },
      {
        field: 'approved',
        headerName: 'Status',
        minWidth: 250,
        valueGetter: (param) => (param.data.done ? 'Completed' : 'Open'),
      },
      {
        field: 'dueDate',
        minWidth: 250,
        filter: 'agDateColumnFilter',
        valueFormatter: (params) => {
          return params.value
            ? formatDate(params.value, 'MM/dd/yyyy HH:mm:ss', 'en-US')
            : null;
        },
        valueGetter: (params) =>
          new Date().setTime(
            Date.parse(params.data.createdDate) + 24 * 60 * 60 * 1000 * 10
          ),
        cellStyle: (params) => {
          let backgroundColor = '#85bb65';

          if (params.data.color === TaskSlaColors.YELLOW) {
            backgroundColor = '#fcf75e';
          }
          if (params.data.color === TaskSlaColors.RED) {
            backgroundColor = '#e3385a';
          }
          return {
            display: 'flex',
            'align-items': 'center',
            'white-space': 'normal',
            backgroundColor,
          };
        },
      },
    ];

    this.completedColumnDefs = cloneDeep(this.columnDefs);
    this.completedColumnDefs[5] = {
      ...this.completedColumnDefs[5],
      headerName: 'Status',
      valueGetter: (params) =>
        !params.data.taskType.workflow.createdBySystem
          ? 'Completed'
          : params.data.approved
          ? 'Approved'
          : 'Rejected',
      cellStyle: (params) => {
        return {
          display: 'flex',
          'align-items': 'center',
          'white-space': 'normal',
          backgroundColor: params.data.approved ? '#85bb65' : '#e3385a',
        };
      },
    };
    this.completedColumnDefs[6].cellStyle = () => {
      return {
        display: 'flex',
        'align-items': 'center',
        'white-space': 'normal',
      };
    };
    this.completedColumnDefs.splice(6, 1);
    this.completedColumnDefs.splice(7, 0, {
      field: 'workedBy',
      minWidth: 250,
      valueGetter: (params) =>
        `${params.data.createdBy.firstName} ${params.data.createdBy.lastName}`,
    });
    this.completedColumnDefs.splice(8, 0, {
      field: 'completedDate',
      minWidth: 175,
      valueFormatter: (params) => {
        return params.value
          ? formatDate(params.value, 'MM/dd/yyyy HH:mm:ss', 'en-US')
          : null;
      },
    });
  }

  onAssignedCellAction(message: DataGridMessage) {
    switch (message.action) {
      case DatagridActionsCta.VIEW:
        this.onView(message, Workflow.ASSIGNED);
        break;
    }
  }

  onInitiatedCellAction(message: DataGridMessage) {
    switch (message.action) {
      case DatagridActionsCta.VIEW:
        this.onView(message, Workflow.INITIATED);
        break;
      case DatagridActionsCta.DELETE:
        this.onDelete(
          message,
          this.workflowService.deleteById(message.rawData.id)
        );
        break;
    }
  }

  onCompletedCellAction(message: DataGridMessage) {
    switch (message.action) {
      case DatagridActionsCta.VIEW:
        this.onView(message, Workflow.COMPLETED);
        break;
      case DatagridActionsCta.DELETE:
        this.onDelete(
          message,
          this.workflowService.softDeleteById(message.rawData.id)
        );
        break;
    }
  }

  private onView(message: DataGridMessage, wf: Workflow) {
    this.router.navigate([window.location.pathname, 'task-detail'], {
      queryParams: { id: message.rawData.id, workflow: wf },
    });
  }

  private onDelete(message: DataGridMessage, service: Observable<any>) {
    service.subscribe(
      () => {
        this.toastService.displaySuccess('Task deleted successfully');
        this.assignedTasks = this.assignedTasks.filter(
          (t) => t.id !== message.rawData.id
        );
        this.taskData[0] = cloneDeep(this.assignedTasks);
      },
      () => this.toastService.displayError('Task deleting failed.')
    );
  }

  private loadData() {
    forkJoin({
      taskTypes: this.lovService
        .findAllWorkflowTypes()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading assigned tasks.'
              )
            )
          )
        ),
      assignedToMe: this.workflowService
        .findAssignedToMe()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading assigned tasks.'
              )
            )
          )
        ),
      initiatedByMe: this.workflowService
        .findInitiatedByMe()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading initiated tasks.'
              )
            )
          )
        ),
      completed: this.workflowService
        .findCompleted()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading completed tasks.'
              )
            )
          )
        ),
    })
      .subscribe((resp) => {
        this.taskData = [resp.assignedToMe, resp.initiatedByMe, resp.completed];
        for (const data of this.taskData) {
          data.forEach((t) => {
            const currTime = new Date().getTime();
            const warningDate =
              Date.parse(t.createdDate.toString()) +
              60 * 60 * 1000 * t.taskType.serviceLevelAgreementWarning;
            const violationDate =
              Date.parse(t.createdDate.toString()) +
              60 * 60 * 1000 * t.taskType.serviceLevelAgreement;

            t.color = TaskSlaColors.GREEN;

            if (currTime > warningDate) {
              t.color = TaskSlaColors.YELLOW;
            }
            if (currTime > violationDate) {
              t.color = TaskSlaColors.RED;
            }
          });
        }
        this.taskTypes = resp.taskTypes;
        console.log('taskTypes', resp.taskTypes);
        this.assignedTasks = resp.assignedToMe;
        this.setColumnDefs();
      })
      .add(() => this.loadCharts());
  }

  private loadCharts() {
    let assignedOnTime = 0,
      assignedExpiring = 0,
      assignedExpired = 0,
      initiatedOnTime = 0,
      initiatedExpiring = 0,
      initiatedExpired = 0,
      approved = 0,
      rejected = 0;

    for (const color of this.taskData[0].map((t) => t.color)) {
      switch (color) {
        case TaskSlaColors.GREEN:
          ++assignedOnTime;
          break;
        case TaskSlaColors.YELLOW:
          ++assignedExpiring;
          break;
        case TaskSlaColors.RED:
          ++assignedExpired;
          break;
      }
    }

    for (const color of this.taskData[1].map((t) => t.color)) {
      switch (color) {
        case TaskSlaColors.GREEN:
          ++initiatedOnTime;
          break;
        case TaskSlaColors.YELLOW:
          ++initiatedExpiring;
          break;
        case TaskSlaColors.RED:
          ++initiatedExpired;
          break;
      }
    }

    for (const a of this.taskData[2].map((t) => t.approved)) {
      if (a) {
        ++approved;
      } else {
        ++rejected;
      }
    }

    this.assignedChartData = {
      labels: ['On time', 'Expiring', 'Expired'],
      datasets: [
        {
          data: [assignedOnTime, assignedExpiring, assignedExpired],
          backgroundColor: ['#85bb65', '#fcf75e', '#e3385a'],
        },
      ],
    };

    this.initiatedChartData = {
      labels: ['On time', 'Expiring', 'Expired'],
      datasets: [
        {
          data: [initiatedOnTime, initiatedExpiring, initiatedExpired],
          backgroundColor: ['#85bb65', '#fcf75e', '#e3385a'],
        },
      ],
    };

    this.completedChartData = {
      labels: ['Approved', 'Rejected'],
      datasets: [
        {
          data: [approved, rejected],
          backgroundColor: ['#85bb65', '#e3385a'],
        },
      ],
    };
  }
}
