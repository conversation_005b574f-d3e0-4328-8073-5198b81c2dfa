import { NgModule } from '@angular/core';
import { CoreModule } from '../../core.module';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { WorkflowComponent } from './workflow.component';
import { WorkflowPanelComponent } from './workflow-panel/workflow-panel.component';
import { TaskDetailComponent } from './workflow-panel/task-detail/task-detail.component';
import { DialogModule } from 'primeng/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { CardModule } from 'primeng/card';
import { LibraryModule } from '../library/library.module';
import { ChartModule } from 'primeng/chart';
import { WorkflowRoutingModule } from './workflow-routing.module';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CalendarModule } from 'primeng/calendar';

@NgModule({
  declarations: [
    WorkflowComponent,
    WorkflowPanelComponent,
    TaskDetailComponent,
  ],
  imports: [
    WorkflowRoutingModule,
    CoreModule,
    FontAwesomeModule,
    DialogModule,
    ReactiveFormsModule,
    InputNumberModule,
    DividerModule,
    CardModule,
    LibraryModule,
    ChartModule,
    InputTextModule,
    InputTextareaModule,
    CalendarModule,
  ],
})
export class WorkflowModule {}
