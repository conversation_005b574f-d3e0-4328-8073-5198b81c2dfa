<p-card id="workflow-card">
<div class="row">
    <div class="col-4">
        <p-card class="task-card">
            <div class="row mx-3 pt-4 pb-1">
                <span class="assigned-font-color task-label">TASK ASSIGNED TO ME</span>
            </div>
            <div class="row justify-content-between m-1 p-3">
                <div class="d-flex w-auto h1 task-amount" (click)="clearFilter(0)"> {{ taskData[0]?.length }} </div>
                <div class="d-flex w-auto">
                    <p-chart [id]="expandedChartIds[0]" type="pie" height="100px" width="100px" [data]="assignedChartData" [options]="chartOptions"></p-chart>
                </div>
            </div>
            <div class="row justify-content-between m-0 p-3 details-container assigned-background">
                <div class="d-flex w-auto">View details</div>
                <div class="d-flex w-auto">
                    <i role='button' class="fa fa-arrow-circle-o-right" *ngIf="!expanded[0]" (click)="expandPane(taskLocation.ASSIGNED, true, 0)"></i>
                    <i role='button' class="fa fa-arrow-circle-o-down" *ngIf="expanded[0]" (click)="expandPane(taskLocation.ASSIGNED, false, 0)"></i>
                </div>
            </div>
        </p-card>
    </div>
    <div class="col-4">
        <p-card class="task-card">
            <div class="row mx-3 pt-4 pb-1">
                <span class="initiated-font-color task-label">TASK INITIATED BY ME</span>
            </div>
            <div class="row justify-content-between m-1 p-3">
                <div class="d-flex w-auto h1 task-amount" (click)="clearFilter(1)"> {{ taskData[1]?.length }} </div>
                <div class="d-flex w-auto">
                    <p-chart [id]="expandedChartIds[1]" type="pie" height="100px" width="100px" id="initiated-pie" [data]="initiatedChartData" [options]="chartOptions"></p-chart>
                </div>
            </div>
            <div class="row justify-content-between m-0 p-3 details-container initiated-background">
                <div class="d-flex w-auto">View details</div>
                <div class="d-flex w-auto">
                    <i role='button' class="fa fa-arrow-circle-o-right" *ngIf="!expanded[1]" (click)="expandPane(taskLocation.INITIATED, true, 1)"></i>
                    <i role='button' class="fa fa-arrow-circle-o-down" *ngIf="expanded[1]" (click)="expandPane(taskLocation.INITIATED, false, 1)"></i>
                </div>
            </div>
        </p-card>
    </div>
    <div class="col-4">
        <p-card class="task-card">
            <div class="row mx-3 pt-4 pb-1">
                <span class="completed-font-color task-label">COMPLETED TASK</span>
            </div>
            <div class="row justify-content-between m-1 p-3">
                <div class="d-flex w-auto h1 task-amount" (click)="clearFilter(2)"> {{ taskData[2]?.length }} </div>
                <div class="d-flex w-auto">
                    <p-chart [id]="expandedChartIds[2]" type="pie" height="100px" width="100px" id="completed-pie" [data]="completedChartData" [options]="chartOptions"></p-chart>
                </div>
            </div>
            <div class="row justify-content-between m-0 p-3 details-container completed-background">
                <div class="d-flex w-auto">View details</div>
                <div class="d-flex w-auto">
                    <i role='button' class="fa fa-arrow-circle-o-right" *ngIf="!expanded[2]" (click)="expandPane(taskLocation.COMPLETED, true, 2)"></i>
                    <i role='button' class="fa fa-arrow-circle-o-down" *ngIf="expanded[2]" (click)="expandPane(taskLocation.COMPLETED, false, 2)"></i>
                </div>
            </div>
        </p-card>
    </div>
</div>
    <ng-container *ngIf="expanded[0]">
        <div class="card row mt-3 mx-0 assigned-border">
            <div class="mt-2 assigned-font-color grid-label">
                TASK ASSIGNED TO ME:
            </div>
            <div class="p-3">
                <datagrid
                        #assignedGrid
                        id="assigned-grid"
                        [name]="'Assigned task list'"
                        class="ag-theme-alpine"
                        (gridIsReady)="onAssignedGridIsReady($event)"
                        (columnIsReady)="onAssignedColumnIsReady($event)"
                        [rows]="assignedTasks"
                        [columns]="columnDefs"
                        [searchValue]="searchValue"
                        [pageSize]="gridPageSize"
                        [hasAutoHeight]="false"
                        (cellAction)="onAssignedCellAction($event)"
                        [showActions]="assignedGrid.ACTION_STATE.VIEW"
                >
                </datagrid>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="expanded[1]">
        <div class="card row mt-3 mx-0 initiated-border">
            <div class="mt-2 initiated-font-color grid-label">
                TASK INITIATED BY ME:
            </div>
            <div class="p-3">
                <datagrid
                        #initiatedGrid
                        id="initiated-grid"
                        [name]="'Initiated task list'"
                        class="ag-theme-alpine"
                        (gridIsReady)="onInitiatedGridIsReady($event)"
                        [rows]="tasks"
                        [columns]="columnDefs"
                        [searchValue]="searchValue"
                        [pageSize]="gridPageSize"
                        [hasAutoHeight]="false"
                        [isPermanentlyDeleting]="true"
                        (cellAction)="onInitiatedCellAction($event)"
                        [showActions]="initiatedGrid.ACTION_STATE.VIEW_SINGLE_DELETE"
                >
                </datagrid>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="expanded[2]">
        <div class="card row mt-3 mx-0 completed-border">
            <div class="mt-2 completed-font-color grid-label">
                COMPLETED TASK:
            </div>
            <div class="p-3">
                <datagrid
                        #completedGrid
                        id="completed-grid"
                        [name]="'Completed task list'"
                        class="ag-theme-alpine"
                        (gridIsReady)="onCompletedGridIsReady($event)"
                        [rows]="tasks"
                        [columns]="completedColumnDefs"
                        [searchValue]="searchValue"
                        [pageSize]="gridPageSize"
                        [hasAutoHeight]="false"
                        [isPermanentlyDeleting]="true"
                        [showActions]="completedGrid.ACTION_STATE.VIEW_SINGLE_DELETE"
                        (cellAction)="onCompletedCellAction($event)"
                >
                </datagrid>
            </div>
        </div>
    </ng-container>
</p-card>
