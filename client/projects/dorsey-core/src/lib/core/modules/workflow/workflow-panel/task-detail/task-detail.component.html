<input type="file" id="fileUpload" (change)="uploadFile()" hidden />
<p-card
  [header]="
    task?.taskType.workflow.workflowType.name +
    ': ' +
    task?.taskType.taskName +
    (currWorkflow === workflow.COMPLETED
      ? ' - ' + (task?.approved ? 'Approved' : 'Rejected')
      : '')
  "
>
  <div *ngIf="currentTemplate" class="row justify-content-center">
    <div class="col-5">
      <p-panel header="Current">
        <ng-container [ngTemplateOutlet]="currentTemplate"></ng-container>
      </p-panel>
    </div>
    <div class="col-1"></div>
    <div class="col-5">
      <p-panel header="Requested">
        <ng-container [ngTemplateOutlet]="newTemplate"></ng-container>
      </p-panel>
    </div>
  </div>

  <div *ngIf="!currentTemplate" class="row justify-content-center">
    <div class="job-container">
      <p-panel id="item-container">
        <ng-template pTemplate="header">
          <div class="d-flex justify-content-between w-100">
            <div class="align-self-center text-light">
              Job Details - {{ config.business.itemName }}
            </div>
            <p-button
              label="History"
              styleClass="shadow-4 p-button-secondary"
              class="ms-1"
              (onClick)="onDisplayHistory()"
            ></p-button>
          </div>
        </ng-template>
        <ng-container [ngTemplateOutlet]="newTemplate"></ng-container>
        <p-divider></p-divider>
        <div class="d-flex mb-2 me-3">
          <div class="d-flex me-2 justify-content-between text-left">
            <label class="label-width m-0 d-flex">Notes*</label>
            <span class="d-flex font-bold">:</span>
          </div>
          <div class="d-flex w-100">
            <textarea
              rows="3"
              class="w-100 me-2"
              pInputTextarea
              [(ngModel)]="justification"
            ></textarea>
          </div>
        </div>
      </p-panel>
    </div>
    <div class="files-container" *ngIf="task?.taskType?.hasDirectory">
      <p-panel id="file-container">
        <ng-template pTemplate="header">
          <div class="d-flex justify-content-end w-100">
            <div>
              <p-button
                label="Get Templates"
                styleClass="p-button-raised p-button-secondary"
                (onClick)="onGetTemplates()"
              ></p-button>
              <p-button
                label="Upload"
                styleClass="p-button-raised p-button-secondary"
                class="ms-1"
                onclick="document.getElementById('fileUpload').click()"
              ></p-button>
            </div>
          </div>
        </ng-template>
        <div class="px-2">
          <div class="row">
            <div class="col-4 text-center"><b>Name</b></div>
            <div class="col-4 text-center"><b>Date Modified</b></div>
            <div class="col-4 text-center"><b>Type</b></div>
          </div>
          <ng-container *ngIf="jobFiles.length">
            <div class="row" *ngFor="let data of jobFiles">
              <div class="col-4 p-0 pr-2">
                <fa-icon
                  tabindex="0"
                  role="button"
                  [icon]="'trash-alt'"
                  title="Delete"
                  (click)="onFileDelete(data.file)"
                ></fa-icon>

                {{ data.name }}
              </div>
              <div class="col-4 p-0 pr-2">{{ data.dateModified }}</div>
              <div class="col-4 p-0">{{ data.type }}</div>
            </div>
          </ng-container>
          <div class="text-center" *ngIf="!jobFiles.length">
            <br />
            <span><i>&lt;&lt;None file found>> </i></span>
          </div>
        </div>
      </p-panel>
    </div>
  </div>

  <ng-container *ngIf="currWorkflow === workflow.ASSIGNED">
    <p-divider></p-divider>
    <div
      *ngIf="!task?.taskType?.workflow?.workflowType?.createdBySystem"
      class="d-flex justify-content-end"
    >
      <p-button
        label="Next"
        styleClass="p-button-raised p-button-success"
        (onClick)="next()"
      ></p-button>
    </div>
    <div
      *ngIf="task?.taskType?.workflow?.workflowType?.createdBySystem"
      class="d-flex justify-content-between"
    >
      <p-button
        label="Reject"
        styleClass="p-button-raised p-button-danger"
        (onClick)="displayJustification = true"
      ></p-button>
      <p-button
        label="Approve"
        styleClass="p-button-raised p-button-success"
        [class.opacity-50]="!canApprove"
        [class.cursor-auto]="!canApprove"
        [class.cursor-pointer]="canApprove"
        [ngbTooltip]="!canApprove ? cannotApproveMsg : null"
        placement="left"
        (onClick)="approve()"
      ></p-button>
    </div>
  </ng-container>

  <div
    class="mt-5"
    *ngIf="currWorkflow === workflow.COMPLETED && task?.justification"
  >
    <h6>Rejection reason:</h6>
    <span class="text-red-500">{{ task.justification }}</span>
  </div>
</p-card>

<p-dialog
  appendTo="body"
  header="Justification"
  [(visible)]="displayJustification"
  [modal]="true"
  [style]="{ width: '500px' }"
>
  <textarea
    rows="5"
    class="w-100"
    pInputTextarea
    [(ngModel)]="justification"
    (keydown)="onJustificationTyping($event)"
  ></textarea>

  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-between">
      <button
        pButton
        type="button"
        label="cancel"
        class="p-button-link"
        (click)="displayJustification = false"
      ></button>
      <p-button
        label="Reject"
        styleClass="p-button-raised p-button-danger"
        [disabled]="!justification.length"
        (onClick)="reject()"
      ></p-button>
    </div>
  </ng-template>
</p-dialog>

<ng-template #roleChangeCurrent>
  <ul *ngIf="currentData.length">
    <li *ngFor="let role of currentData">
      {{ role }}
    </li>
  </ul>
  <span *ngIf="!currentData.length">No role data</span>
</ng-template>

<ng-template #roleChangeNew>
  <ul *ngIf="newData.length">
    <li *ngFor="let role of newData">
      {{ role }}
    </li>
  </ul>
  <span *ngIf="!newData.length">No role data</span>
</ng-template>

<ng-template #hierarchyChangeCurrent>
  <span *ngIf="currentData"
    >{{ currentData.hierarchyName }}: {{ currentData.hierarchyValue }}</span
  >
  <span *ngIf="!currentData">No hierarchy data</span>
</ng-template>

<ng-template #hierarchyChangeNew>
  <span>{{ newData.hierarchyName }}: {{ newData.hierarchyValue }}</span>
</ng-template>

<ng-template #jifChangeNew>
  <form [formGroup]="form" class="">
    <div class="d-flex flex-wrap">
      <ng-container
        *ngFor="let data of task.taskType.workflow.workflowType.type.data"
      >
        <ng-container
          *ngIf="
            data.field !== config.business.itemId && data.dataType === 'numeric'
          "
        >
          <div *ngIf="isFieldVisible(data.field)" class="d-flex mb-2 me-3">
            <div
              class="align-self-center d-flex me-2 justify-content-between text-left"
            >
              <label class="label-width m-0"
                >{{ data.label
                }}{{ isFieldRequired(data.field) ? "*" : "" }}</label
              >
              <span class="d-flex font-bold">:</span>
            </div>
            <div class="d-flex field-width">
              <p-inputNumber
                class="w-100"
                [name]="data.field"
                [formControlName]="data.field"
                placeholder="Type a value"
                inputId="currency-us"
                mode="currency"
                currency="USD"
                locale="en-US"
              >
              </p-inputNumber>
            </div>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            data.field !== config.business.itemId &&
            data.dataType === 'string' &&
            !data.values
          "
        >
          <div *ngIf="isFieldVisible(data.field)" class="d-flex mb-2 me-3">
            <div
              class="align-self-center d-flex me-2 justify-content-between text-left"
            >
              <label class="label-width m-0 d-flex"
                >{{ data.label
                }}{{ isFieldRequired(data.field) ? "*" : "" }}</label
              >
              <span class="d-flex font-bold">:</span>
            </div>
            <div class="d-flex field-width">
              <input
                class="w-100"
                [name]="data.field"
                [formControlName]="data.field"
                type="text"
                pInputText
                [placeholder]="'Type a ' + data.label"
                [maxLength]="data.fieldLength"
              />
            </div>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            data.field !== config.business.itemId &&
            data.dataType === 'string' &&
            data.values
          "
        >
          <div *ngIf="isFieldVisible(data.field)" class="d-flex mb-2 me-3">
            <div
              class="align-self-center d-flex me-2 justify-content-between text-left"
            >
              <label class="label-width m-0"
                >{{ data.label }}
                {{ isFieldRequired(data.field) ? "*" : "" }}</label
              >
              <span class="d-flex font-bold">:</span>
            </div>
            <div class="d-flex field-width">
              <p-dropdown
                [options]="loadDropdownOptions(data.values)"
                class="w-100"
                [placeholder]="'Select ' + data.label"
                optionLabel="name"
                optionValue="code"
                [showClear]="true"
                [name]="data.field"
                [formControlName]="data.field"
                [disabled]="!editingStateService.editingState.isEditing"
              ></p-dropdown>
            </div>
          </div>
        </ng-container>
        <ng-container
          *ngIf="
            data.field !== config.business.itemId && data.dataType === 'date'
          "
        >
          <div *ngIf="isFieldVisible(data.field)" class="d-flex mb-2 me-3">
            <div
              class="align-self-center d-flex me-2 justify-content-between text-left"
            >
              <label class="label-width m-0"
                >{{ data.label
                }}{{ isFieldRequired(data.field) ? "*" : "" }}</label
              >
              <span class="d-flex font-bold">:</span>
            </div>
            <div class="d-flex field-width">
              <p-calendar
                id="startDate"
                [showIcon]="true"
                [dataType]="'string'"
                dateFormat="yy-mm-dd"
                name="proposalSentDate"
                [formControlName]="data.field"
                appendTo="body"
                placeholder="Select Date"
              ></p-calendar>
            </div>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </form>
</ng-template>

<p-dialog
  appendTo="body"
  header="Library"
  [(visible)]="displayLibrary"
  [modal]="true"
  [style]="{ width: '80vw', height: '80vh' }"
>
  <dorsey-file-browser></dorsey-file-browser>
</p-dialog>

<p-dialog
  appendTo="body"
  [(visible)]="displayHistory"
  header="Workflow History"
  [modal]="true"
  [style]="{ width: '1200px' }"
>
  <p-divider></p-divider>
  <div class="px-2">
    <div class="row">
      <div class="col-2 text-center"><b>Workflow Step</b></div>
      <div class="col-2 text-center"><b>Performer</b></div>
      <div class="col-2 text-center"><b>Assign Date/Time</b></div>
      <div class="col-2 text-center"><b>Completed Date/Time</b></div>
      <div class="col-4 text-center"><b>Comment/Notes</b></div>
    </div>
    <ng-container *ngIf="histories.length">
      <div class="row" *ngFor="let data of histories">
        <div class="col-2">{{ data.workflowStep }}</div>
        <div class="col-2">{{ data.performer }}</div>
        <div class="col-2">
          {{ data.assignedDate | date : "yyyy-MM-dd HH:mm:ss" }}
        </div>
        <div class="col-2">
          {{ data.completedDate | date : "yyyy-MM-dd HH:mm:ss" }}
        </div>
        <div class="col-4">{{ data.comment }}</div>
      </div>
    </ng-container>
    <div class="text-center" *ngIf="!histories.length">
      <br />
      <span><i>&lt;&lt;No history to display>> </i></span>
    </div>
    <p-divider></p-divider>
  </div>
</p-dialog>
