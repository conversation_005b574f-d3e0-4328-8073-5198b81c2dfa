<!--<app-header [user]="loggedInUser"></app-header>-->
<input type="file" id="fileUpload" (change)="uploadFile()" hidden>
<div class="alert m-2" [class.alert-success]="alertSuccess" [class.alert-danger]="!alertSuccess" role="alert"
  style="display: none;">
  <strong>{{alertSuccess?'Success!':'Error!'}} </strong> {{alertMessage}}
</div>
<div *ngIf="loading"><i class="fas fa-4x fa-spin fa-circle-notch" style="position: absolute;top:30%;left:50%;"></i>
</div>
<div *ngIf="!loading" class="card m-2">
  <div class="card-header bg-transparent text-center">
<!--    <span class="float-left">Folders <i class="fas fa-arrow-right"></i>-->
<!--      {{dirs.length}} & Files <i class="fas fa-arrow-right"></i> {{files.length}}</span>-->
    <!-- <strong class="text-secondary">IMI File Manager</strong> -->
    <span class="float-end ms-1"><button class="btn btn-sm btn-primary mr-1"
        onclick="document.getElementById('fileUpload').click()">Upload <i class="fa fa-upload"></i></button></span>
    <span class="float-end">
      <div class="row m-0">
	<div class="col p-0">
		<input
                id="typeahead-template"
                type="text"
                class="search form-control col"
                [(ngModel)]="searchValue"
                [ngbTypeahead]="search"
                [resultTemplate]="rt"
                [inputFormatter]="formatter"
                (selectItem)="onOptionSelect($event.item)"
        />
	</div>
</div>
    </span>
    <br>
    <span *ngIf="breadcrumbs?.length>0" class="float-left">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb bg-transparent" style="max-height: 15px;">
          <li class="mr-1"><i class="fa fa-folder-open-o"></i></li>
          <li *ngFor="let breadcrumb of breadcrumbs" class="breadcrumb-item" aria-current="page">
            <a class="breadcrumb-item-link" href="#"
              (click)="onBreadcrumbClick(breadcrumb, $event)">{{breadcrumb.name}}</a>
          </li>
          <li class="mx-2"><a href="#" (click)="refresh($event)" title="Refresh Files & Folders"><i
                class="fa fa-refresh text-secondary"></i></a>
          </li>
        </ol>
      </nav>
    </span>
  </div>
  <div class="card-body row">
    <div class="col-2 pe-4">
      <div class="text-secondary ml-1"><strong>({{dirs.length}}) List of Folders <a href="#collapseAddFolder" data-toggle="collapse"
            title="Add new folder here" onclick="$('#addFolderInput').val('')">
        <fa-icon   [icon]="['fas', 'folder-plus']"></fa-icon>
      </a></strong>
      </div>
      <div class="collapse" id="collapseAddFolder">
        <div class="input-group mb-2">
          <input id="addFolderInput" type="text" class="form-control" placeholder="Folder Name" aria-label="Folder Name"
            aria-describedby="addFolder">
          <div class="input-group-append">
            <button (click)="addFolder()" class="btn btn-outline-primary" type="button" id="addFolder">Add</button>
          </div>
        </div>
      </div>
      <div class="list-group">
        <ng-container *ngIf="dirs.length>0">
          <span *ngFor="let dir of dirs" class="list-group-item list-group-item-action">
            <a href="#" (click)="onDirClick(dir, $event)" class="text-secondary" style="text-decoration: none;">
              <i class="fa fa-folder-o"></i> {{dir.name}}</a>
            <a title="Delete folder {{dir.name}}" class="float-end" href="#" (click)="deleteFolder(dir, $event)"><i
                class="fa fa-trash-o text-danger"></i></a>
          </span>
        </ng-container>
        <ng-container *ngIf="dirs.length==0">
          <span class="list-group-item list-group-item-action">
            No Folders Available!
          </span>
        </ng-container>
      </div>
    </div>
    <div class="col-10" style="padding-top: 12px;">
      <div class="text-secondary ml-1"><strong>Files: {{files.length}} </strong>
      </div>
      <div class="row">
        <div *ngIf="files.length==0" class="jumbotron jumbotron-fluid">
          <div class="container">
            <p class="lead">No files available in the current directory.</p>
          </div>
        </div>
        <div *ngFor="let file of files" class="card col-lg-2 col-md-3 col-sm-6 m-1 shadow">
          <div *ngIf="validImgFile(file.name)" class="card-header bg-transparent">
            <a href="#" (click)="onFileClick(file, $event)">
              <img [attr.src]="getFileUrl(file)" style="position: relative;height: 100%;width: 100%; max-height: 150px">
            </a>
          </div>
          <div class="card-body">
            <!-- <ng-template [ngIf]="validFile(file.name)"> -->
            <a href="#" (click)="onFileClick(file, $event)" class="text-secondary">
              <i class="fa fa-2x fa-file-o" [class.fa-file]="!validFile(file.name)"
                [class.fa-file-pdf-o]="file.name.toLowerCase().indexOf('.pdf') != -1"
                [class.text-danger]="file.name.toLowerCase().indexOf('.pdf') != -1"
                [class.fa-file-image-o]="validImgFile(file.name)"></i>
              {{file.name}}
            </a>
            <!-- </ng-template> -->
            <!-- <ng-template [ngIf]="!validFile(file.name)">
              <i class="far fa-2x" [class.fa-file]="!validFile(file.name)"
                [class.fa-file-pdf]="file.name.toLowerCase().indexOf('.pdf') != -1"
                [class.fa-file-image]="validImgFile(file.name)"></i>
              {{file.name}}
            </ng-template> -->
          </div>
          <div class="card-footer bg-transparent">
            <a href="#" class="text-secondary float-end" title="Delete" (click)="onFileDelete(file, $event)"><i
                class="fa fa-trash-o text-danger"></i></a>
            <a href="#" class="text-secondary float-end mx-2" title="Download"
              (click)="onFileDownload(file, $event)"><i class="fa fa-download text-info"></i></a>
            <a href="#" class="float-end text-info" title="Copy File Path to Clipboard"
              (click)="copyPath(file,$event)"><i class="fa fa-clipboard"></i></a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card-footer" hidden>Folders <i class="fas fa-arrow-right"></i> {{dirs.length}} Files <i
      class="fas fa-arrow-right"></i> {{files.length}}</div>
</div>

<div id="addFolder" class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-body d-flex text-center">
        <label for="addFolderInput" class="mr-1">Folder Name</label>
        <input class="form-input" id="addFolderInput" autofocus>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary float-left" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="addFolder()">Add</button>
      </div>
    </div>
  </div>
</div>

<div class="toast bg-success text-white" data-delay="2000" style="position: fixed; top: 40%; right: 40%;">
  <div class="toast-body">
    <strong>Copied to Clipboard!</strong>
  </div>
</div>

<ng-template #rt let-r="result" let-t="term">
  <i class="me-1" [class]="r.directory ? 'fa fa-folder-o' : 'fa fa-file-o'"></i>
  <ngb-highlight [result]="r.name" [term]="t"></ngb-highlight>
  <br/>
  <em>({{r.path}})</em>
</ng-template>
