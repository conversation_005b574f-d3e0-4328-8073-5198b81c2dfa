import { Directive, ElementRef, HostListener, Input, TemplateRef, Type } from '@angular/core';
import { EditingStateService } from '../services/editing-state.service';
import { FormAction } from '../models/form-action';

@Directive({
  selector: '[appEditCta]'
})
export class EditingStateDirective {
  action: FormAction;

  constructor(private el: ElementRef, private editingStateService: EditingStateService) {}

  @Input() set appEditCta(action: FormAction) {
    if (!this.el.nativeElement.querySelector('button').disabled) {
      this.action = action;
    }
  }

  @HostListener('click', ['$event']) onClick() {
    if (!this.el.nativeElement.querySelector('button').disabled) {
      this.editingStateService.setValue(this.action);
    }
  }
}
