import {
  Directive,
  ElementRef,
  HostListener,
  Optional,
  Renderer2,
  Self,
} from '@angular/core';
import { NgControl, NgModel } from '@angular/forms';

/**
 * @whatItDoes Trim value's spaces on the left and right when user leaves an input field.
 * If value is blank it'll be set as null.
 */

@Directive({
  selector: '[dorseyTrim]',
  providers: [NgModel],
})
export class TrimDirective {
  constructor(
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private ngModel: NgModel,
    @Optional() @Self() private control: NgControl
  ) {}

  @HostListener('blur')
  onBlur() {
    let value = this.control.control.value ?? this.ngModel;

    if (typeof value === 'string' && value) {
      value = value.trim() ?? '';
      this.renderer.setProperty(this.elementRef.nativeElement, 'value', value);
      this.renderer.setAttribute(this.elementRef.nativeElement, 'value', value);
      this.ngModel.update.emit(value);
      this.control.control.setValue(value);
    } else if (!this.elementRef.nativeElement.value.trim()) {
      this.renderer.setProperty(this.elementRef.nativeElement, 'value', null);
      this.renderer.setAttribute(this.elementRef.nativeElement, 'value', null);
      this.ngModel.update.emit('');
      this.control.control.setValue('');
    }
  }
}
