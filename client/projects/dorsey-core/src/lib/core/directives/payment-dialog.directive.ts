import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { PaymentFormComponent } from '../components/payment-form/payment-form.component';
import { DialogService } from 'primeng/dynamicdialog';
import { PaymentService } from '../services/payment/payment.service';
import { DialogMessageService } from '../services/dialog-message.service';
import { Router } from '@angular/router';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Directive({
  selector: '[appPaymentDialog]',
  providers: [DialogService],
})
export class PaymentDialogDirective {
  @Input() amount: number;

  constructor(
    private el: ElementRef,
    private dialogService: DialogService,
    private dialogMessageService: DialogMessageService,
    private paymentService: PaymentService,
    private router: Router,
    private sanitizer: DomSanitizer
  ) {}

  @HostListener('click', ['$event'])
  onClick(event: Event): void {
    this.paymentService.hasCard().subscribe((resp) => {
      if (resp) {
        this.openPaymentDialog();
      } else {
        const profileUrl = `${window.location.origin}/user/profile`;
        const message = `You have not created a payment method. Please go to your <b><a href="${profileUrl}" target="_self">profile</a></b> and update your billing information.`;

        const safeMessage: SafeHtml =
          this.sanitizer.bypassSecurityTrustHtml(message);
        this.dialogMessageService.displayError(safeMessage);
      }
    });
  }

  private openPaymentDialog(): void {
    this.dialogService.open(PaymentFormComponent, {
      header: 'Payment Information',
      width: '600px',
      contentStyle: { 'max-height': '500px', overflow: 'auto' },
      baseZIndex: 10000,
      data: {
        amount: this.amount, // Pass the amount to the PaymentFormComponent
      },
    });
  }

  navigateToProfile() {
    this.router.navigate(['/user/profile']);
  }
}
