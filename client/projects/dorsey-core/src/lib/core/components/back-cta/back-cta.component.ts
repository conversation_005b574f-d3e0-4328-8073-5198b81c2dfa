import { Component, EventEmitter, Output } from '@angular/core';
import { Router } from '@angular/router';
import { EditingStateService } from '../../services/editing-state.service';
import { PersistenceService } from '../../services/persistence.service';

@Component({
  selector: 'dorsey-back-cta',
  templateUrl: './back-cta.component.html',
  styleUrls: ['./back-cta.component.scss'],
})
export class BackCtaComponent {
  @Output() onBackClicked: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private router: Router,
    private editingStateService: EditingStateService,
    private persistenceService: PersistenceService
  ) {}

  onBack() {
    if (
      !this.editingStateService.getEditingState() ||
      !this.editingStateService.getPrevUrl()
    ) {
      this.persistenceService.onReturning();
      window.history.back();
    } else {
      this.router.navigate([this.editingStateService.getPrevUrl()], {
        replaceUrl: true,
      });
    }
    this.onBackClicked.emit();
  }
}
