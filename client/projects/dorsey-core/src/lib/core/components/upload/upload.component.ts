import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'dorsey-upload',
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.scss'],
})
export class UploadComponent implements OnInit {
  @Input() display = false;
  @Input() accept: string[] = [];
  @Output() upload = new EventEmitter<any>();

  file: File;

  constructor() {}

  ngOnInit(): void {}

  fileChangeEvent(event: Event) {
    const target = event.target as HTMLInputElement;
    const files = target.files as FileList;

    this.file = files[0];
  }

  onUpload() {
    this.upload.emit(this.file);
    this.display = false;
  }

  onHide() {
    this.display = false;
  }

  onShow() {
    this.file = null;
  }
}
