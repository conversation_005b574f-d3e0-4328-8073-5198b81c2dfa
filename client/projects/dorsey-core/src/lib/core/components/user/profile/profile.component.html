<div class="d-flex flex-column h-100 p-4">
  <div class="row mb-3">
    <input
      #profileImageVar
      type="file"
      accept="image/*"
      (change)="onImageSelected($event)"
      hidden
      [disabled]="!editingStateService.editingState.isEditing"
    />
    <div
      class="d-flex w-10rem"
      [class.cursor-pointer]="editingStateService.editingState.isEditing"
      (click)="profileImageVar.click()"
      [title]="
        editingStateService.editingState.isEditing ? 'Click to upload' : ''
      "
    >
      <i
        *ngIf="!user?.profileImage"
        class="fa fa-user-circle"
        aria-hidden="true"
      ></i>
      <img
        width="120"
        height="120"
        *ngIf="user?.profileImage"
        [src]="
          profileImage
            ? user.profileImage
            : 'data:image/png;base64,' + user.profileImage
        "
        alt="profile-image"
      />
    </div>
    <div class="d-flex w-fit flex-column">
      <div class="row">
        <h3 class="d-block mb-0">{{ user?.firstName }} {{ user?.lastName }}</h3>
      </div>
      <div class="row">
        <h5 class="text-muted">{{ user?.title }}</h5>
      </div>
      <div class="row">
        <h6>{{ user?.email }}</h6>
      </div>
      <div class="row">
        <h6>Since {{ user?.effectiveDate | date : "longDate" }}</h6>
      </div>
    </div>
    <div class="d-flex w-fit flex-grow-1 justify-content-end mb-2">
      <div class="action-button-container">
        <!--                <cms-edition-ctas *hasAnyRole="actions.EDIT;path:path" class="col-3 p-0 text-end" [canEdit]="true">-->
        <dorsey-edition-ctas class="col-3 p-0 text-end" [canEdit]="true">
        </dorsey-edition-ctas>
      </div>
    </div>
  </div>
  <div class="row">
    <p-tabView
      styleClass="tabview-custom"
      (onChange)="onTabChange($event)"
      [(activeIndex)]="activeTab"
    >
      <p-tabPanel [disabled]="editingStateService.editingState.isEditing">
        <ng-template pTemplate="header">
          <i class="pi pi-id-card me-1"></i>
          <span>Profile</span>
        </ng-template>
        <div class="mt-4 ms-3 d-flex">
          <div class="mb-3 d-flex w-75">
            <dorsey-profile-information
              *ngIf="user && activeTab === 0"
              [user]="user"
              (isFormValid)="isUserFormValid($event)"
              (userChange)="onUserDataChange($event)"
            ></dorsey-profile-information>
          </div>
          <div class="d-flex w-25 flex-column">
            <div class="mb-3 d-flex w-100">
              <p-fieldset legend="Roles" class="w-100">
                <div class="row">
                  <div class="d-flex w-100 justify-content-end mb-1">
                    <i
                      [ngbTooltip]="
                        pendingRoleReq ? 'A role request is pending.' : null
                      "
                      placement="left"
                      [class.cursor-auto]="pendingRoleReq"
                      [class.cursor-pointer]="!pendingRoleReq"
                      class="fa fa-pencil-square-o"
                      aria-hidden="true"
                      (click)="onRoleRequest()"
                    ></i>
                  </div>
                </div>
                <div class="d-flex w-100">
                  <p-listbox
                    class="w-100"
                    [options]="userRoles"
                    [(ngModel)]="selectedRole"
                    optionLabel="name"
                    [style]="{ width: '100%' }"
                  ></p-listbox>
                </div>
              </p-fieldset>
            </div>
            <div class="mb-3 d-flex w-100">
              <p-fieldset legend="Hierarchy" class="w-100">
                <div class="row">
                  <div class="d-flex w-100 justify-content-end mb-1">
                    <i
                      [ngbTooltip]="
                        pendingHierarchyReq
                          ? 'A hierarchy request is pending.'
                          : null
                      "
                      placement="left"
                      [class.cursor-auto]="pendingHierarchyReq"
                      [class.cursor-pointer]="!pendingHierarchyReq"
                      class="fa fa-pencil-square-o"
                      aria-hidden="true"
                      (click)="onHierarchyRequest()"
                    ></i>
                  </div>
                </div>
                <div class="flex justify-content-left">
                  <span>
                    {{
                      user?.hierarchyData
                        ? user.hierarchyData.levelData.hierarchyName +
                          ": " +
                          user?.hierarchyData.hierarchyValue
                        : "No result found"
                    }}</span
                  >
                </div>
              </p-fieldset>
            </div>
          </div>
        </div>
      </p-tabPanel>
      <p-tabPanel [disabled]="editingStateService.editingState.isEditing">
        <ng-template pTemplate="header">
          <i class="pi pi-money-bill me-1"></i>
          <span>Billing Information</span>
        </ng-template>
        <div class="mt-4 ms-3 d-flex">
          <div class="mb-3 d-flex w-75 flex-column">
            <dorsey-billing-information
              *ngIf="activeTab === 1"
              [user]="user"
              [isActive]="activeTab === 1"
            ></dorsey-billing-information>
          </div>
          <div class="d-flex w-25 flex-column">
            <div class="mb-3 d-flex w-100">
              <p-fieldset legend="Roles" class="w-100">
                <div class="row">
                  <div class="d-flex w-100 justify-content-end mb-1">
                    <i
                      [ngbTooltip]="
                        pendingRoleReq ? 'A role request is pending.' : null
                      "
                      placement="left"
                      [class.cursor-auto]="pendingRoleReq"
                      [class.cursor-pointer]="!pendingRoleReq"
                      class="fa fa-pencil-square-o"
                      aria-hidden="true"
                      (click)="onRoleRequest()"
                    ></i>
                  </div>
                </div>
                <div class="d-flex w-100">
                  <p-listbox
                    class="w-100"
                    [options]="userRoles"
                    [(ngModel)]="selectedRole"
                    optionLabel="name"
                    [style]="{ width: '100%' }"
                  ></p-listbox>
                </div>
              </p-fieldset>
            </div>
            <div class="mb-3 d-flex w-100">
              <p-fieldset legend="Hierarchy" class="w-100">
                <div class="row">
                  <div class="d-flex w-100 justify-content-end mb-1">
                    <i
                      [ngbTooltip]="
                        pendingHierarchyReq
                          ? 'A hierarchy request is pending.'
                          : null
                      "
                      placement="left"
                      [class.cursor-auto]="pendingHierarchyReq"
                      [class.cursor-pointer]="!pendingHierarchyReq"
                      class="fa fa-pencil-square-o"
                      aria-hidden="true"
                      (click)="onHierarchyRequest()"
                    ></i>
                  </div>
                </div>
                <div class="flex justify-content-left">
                  <span>
                    {{
                      user?.hierarchyData
                        ? user.hierarchyData.levelData.hierarchyName +
                          ": " +
                          user?.hierarchyData.hierarchyValue
                        : "No result found"
                    }}</span
                  >
                </div>
              </p-fieldset>
            </div>
          </div>
        </div>
      </p-tabPanel>
      <p-tabPanel *ngIf="template1">
        <ng-template pTemplate="header">
          <i [class]="'pi me-1' + template1.icon"></i>
          <span> {{ template1.header }}</span>
        </ng-template>
        <ng-container *ngTemplateOutlet="template1.view"> </ng-container>
      </p-tabPanel>
      <p-tabPanel *ngIf="template2">
        <ng-template pTemplate="header">
          <i [class]="'pi me-1' + template2.icon"></i>
          <span> {{ template2.header }}</span>
        </ng-template>
        <ng-container *ngTemplateOutlet="template2.view"> </ng-container>
      </p-tabPanel>
      <p-tabPanel *ngIf="template3">
        <ng-template pTemplate="header">
          <i [class]="'pi me-1' + template3.icon"></i>
          <span> {{ template3.header }}</span>
        </ng-template>
        <ng-container *ngTemplateOutlet="template3.view"> </ng-container>
      </p-tabPanel>
    </p-tabView>
  </div>
</div>

<p-dialog
  appendTo="body"
  header="Role Request"
  [(visible)]="displayRoleReq"
  [modal]="true"
  [style]="{ width: '800px' }"
  (onShow)="copyPrevData()"
  (onHide)="resetPickList()"
>
  <p-pickList
    [source]="sourceRoles"
    [target]="targetRoles"
    sourceHeader="Available"
    targetHeader="Selected"
    [dragdrop]="true"
    [responsive]="true"
    [showSourceControls]="false"
    [showTargetControls]="false"
    [sourceStyle]="{ height: '30rem' }"
    [targetStyle]="{ height: '30rem' }"
    breakpoint="1400px"
    (onMoveToSource)="onRoleChange()"
    (onMoveToTarget)="onRoleChange()"
  >
    <ng-template let-role pTemplate="item">
      <div class="flex flex-wrap p-2 align-items-center gap-3">
        <div class="flex-1 flex flex-column gap-2">
          <span class="font-bold">{{ role.name }}</span>
        </div>
      </div>
    </ng-template>
  </p-pickList>

  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-between">
      <button
        pButton
        type="button"
        label="cancel"
        class="p-button-link"
        (click)="displayRoleReq = false"
      ></button>
      <p-button
        label="Submit"
        [disabled]="!roleChanged"
        (onClick)="submitRoleRequest()"
      ></p-button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  appendTo="body"
  header="Hierarchy Request"
  [(visible)]="displayHierarchyReq"
  [modal]="true"
  [style]="{ width: '500px' }"
  (onHide)="resetTreeSelect()"
>
  <p-treeSelect
    class="md:w-20rem w-full"
    containerStyleClass="w-full"
    [selectionMode]="'single'"
    [(ngModel)]="selectedNodes"
    [options]="nodes"
    placeholder="Select Item"
    (onNodeSelect)="onHierarchyChange()"
  ></p-treeSelect>

  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-between">
      <button
        pButton
        type="button"
        label="cancel"
        class="p-button-link"
        (click)="displayHierarchyReq = false"
      ></button>
      <p-button
        label="Submit"
        [disabled]="!hierarchyChanged"
        (onClick)="submitHierarchyRequest()"
      ></p-button>
    </div>
  </ng-template>
</p-dialog>
