import {
  Compo<PERSON>,
  <PERSON>ementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { firstValueFrom, Subject } from 'rxjs';
import { RoleActions } from '../../../models/enums/role-actions';
import { IUser } from '../../../models/admin/users/user.model';
import { UserService } from '../../../services/admin/user.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { ToastService } from '../../../services/toast.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { RoleService } from '../../../services/admin/role.service';
import { OrganizationService } from '../../../services/admin/organization.service';
import { FormAction } from '../../../models/form-action';
import { handleCancelAction } from '../../../utils/grid-utils';
import { addHierarchyLabelToNodes } from '../../../utils/organization-hierarchy.util';
import { TaskType } from '../../../models/enums/workflow/task-type.enum';
import { IWorkflowTask } from '../../../models/workflow/workflow-task.model';
import { WorkflowService } from '../../../services/workflow/workflow.service';
import { ITabPanelView } from '../../../models/tab-panel-view';
import { blobToBase64 } from '../../../utils/file-utils';
import { StripeService } from 'ngx-stripe';
import { FileService } from '../../../services/file/file.service';
import { UserDataService } from '../../../services/admin/user-data.service';

@Component({
  selector: 'dorsey-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent implements OnInit, OnDestroy {
  @ViewChild('profileImageVar') profileImageVar: ElementRef;
  // @ViewChild('postalCode', { static: false }) postalCodeRef;

  @Output() profileData = new EventEmitter<any>();
  @Input() useSave: boolean;
  @Input() template1: ITabPanelView;
  @Input() template2: ITabPanelView;
  @Input() template3: ITabPanelView;
  private readonly destroy$ = new Subject<void>();

  activeTab = 0;

  path = location.pathname;
  actions = RoleActions;
  user: IUser;
  validUserData: boolean;
  profileImage: File = null;

  selectedRole: string;
  userRoles: any[];
  roleList: any[];

  displayRoleReq = false;
  displayHierarchyReq = false;
  roleChanged = false;
  hierarchyChanged = false;

  sourceRoles = [];
  targetRoles = [];
  prevSourceRoles = [];
  prevTargetRoles = [];

  nodes!: any[];
  selectedNodes: any;

  pendingRoleReq = false;
  pendingHierarchyReq = false;

  stripe: any;
  elements: any;

  countries: string[];
  selectedCountry: string;

  stateData: any;
  states: string[];
  selectedState: string;

  cities: string[];
  selectedCity: string;

  constructor(
    private userService: UserService,
    private fb: FormBuilder,
    public editingStateService: EditingStateService,
    private toastService: ToastService,
    private dialogMessageService: DialogMessageService,
    private roleService: RoleService,
    private organizationService: OrganizationService,
    private workflowService: WorkflowService,
    private stripeService: StripeService,
    private fileService: FileService,
    private userDataService: UserDataService
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        if (this.activeTab === 0) {
          switch (value) {
            case FormAction.SAVE:
              if (this.useSave) {
                this.saveData();
              } else {
                if (this.validUserData) {
                  this.profileData.emit(this.user);
                }
              }
              break;
            case FormAction.EDIT:
              this.editingStateService.setData([
                ['user', JSON.parse(JSON.stringify(this.user))],
              ]);
              break;
            case FormAction.CANCEL:
              this.profileImage = null;
              this.profileImageVar.nativeElement.value = '';
              break;
            case FormAction.SUBMIT:
              break;
          }
        }
      });
    firstValueFrom(handleCancelAction(this.editingStateService, this));
  }

  ngOnInit() {
    this.checkPendingReq();
    this.loadUserData();
  }

  private loadCountries() {
    this.fileService.loadFile('countries').subscribe((resp) => {
      this.countries = resp;
    });
  }

  private loadStates() {
    this.fileService.loadFile(this.selectedCountry).subscribe((resp) => {
      this.stateData = resp;
      this.states = resp.states.map((s) => s.name);
    });
  }

  private checkPendingReq() {
    this.workflowService.findInitiatedByMe().subscribe((resp) => {
      this.pendingRoleReq = resp.some(
        (v) => v.taskType.taskName === TaskType.URC
      );
      this.pendingHierarchyReq = resp.some(
        (v) => v.taskType.taskName === TaskType.UHC
      );
    });
  }

  private loadUserData() {
    this.userService.findUser().subscribe(
      (resp) => {
        this.user = resp;
        this.selectedNodes = resp.hierarchyData;
        this.targetRoles = [...resp.roles];
        this.userRoles = resp.roles.map((r) => {
          return { name: `- ${r.name}`, code: r.name };
        });
        // this.setFormEditing();

        this.loadRoles();
        this.loadHierarchy();
      },
      (error) => {
        this.user = {};
      }
    );
  }

  private loadRoles() {
    this.roleService.findAll().subscribe(
      (resp) => {
        this.roleList = resp;
        this.sourceRoles = this.roleList.filter(
          (r) => !this.targetRoles.some((tr) => tr.name === r.name)
        );
      },
      () =>
        this.toastService.displayError('Error occurred while retrieving roles')
    );
  }

  private loadHierarchy() {
    this.organizationService.findOrgTree().subscribe((resp) => {
      this.nodes = resp;
      if (this.selectedNodes)
        this.selectedNodes.label = `${this.selectedNodes?.levelData.hierarchyName}: ${this.selectedNodes?.hierarchyValue}`;
      addHierarchyLabelToNodes(this.nodes);
    });
  }

  onImageSelected(event: any) {
    blobToBase64((event.target as HTMLInputElement).files[0]).then(
      (result: any) => {
        this.user.profileImage = result;
        this.profileImage = (event.target as HTMLInputElement).files[0];
      }
    );
  }

  saveData() {
    if (this.validUserData) {
      const user: IUser = JSON.parse(JSON.stringify(this.user));
      user.profileImage = null;
      this.userService.updateUser(user, this.profileImage).subscribe(
        () => {
          this.editingStateService.setValue(FormAction.SUBMIT);
          this.toastService.displaySuccess(
            'Profile information updated successfully.'
          );

          this.userDataService.setUser(user);
        },
        () =>
          this.toastService.displayError(
            'Failed while saving profile information.'
          )
      );
    } else {
      this.dialogMessageService.displayError('Some fields are invalid.');
    }
  }

  onRoleRequest() {
    this.displayRoleReq = !this.pendingRoleReq;
  }

  onHierarchyRequest() {
    this.displayHierarchyReq = !this.pendingHierarchyReq;
  }

  submitRoleRequest() {
    const task: IWorkflowTask = {
      taskType: {
        taskName: TaskType.URC,
        workflow: { id: '6dff2664-3216-463c-9451-009791a41c68' },
      },
      entityName: `${this.user.firstName} ${this.user.lastName}`,
      value: this.user.roles.map((v) => v.name),
      reqValue: this.targetRoles.map((v) => v.name),
    };

    this.workflowService.createTask(task).subscribe(() => {
      this.toastService.displaySuccess(
        'Role change request sent successfully.'
      );
      this.pendingRoleReq = true;
    });

    this.displayRoleReq = false;
  }

  onRoleChange() {
    this.roleChanged =
      this.user.roles.length != this.targetRoles.length
        ? this.targetRoles.length === 0 ||
          this.targetRoles.length > this.user.roles.length
          ? true
          : !this.user.roles.every((r) =>
              this.targetRoles.some((sr) => sr.name === r.name)
            )
        : !this.user.roles.every((r) =>
            this.targetRoles.some((sr) => sr.name === r.name)
          );
  }

  onHierarchyChange() {
    this.hierarchyChanged =
      this.user?.hierarchyData?.hierarchyValue !==
      this.selectedNodes.hierarchyValue;
  }

  resetPickList() {
    this.sourceRoles = JSON.parse(JSON.stringify(this.prevSourceRoles));
    this.targetRoles = JSON.parse(JSON.stringify(this.prevTargetRoles));
  }

  copyPrevData() {
    this.prevSourceRoles = JSON.parse(JSON.stringify(this.sourceRoles));
    this.prevTargetRoles = JSON.parse(JSON.stringify(this.targetRoles));
  }

  submitHierarchyRequest() {
    let value;

    if (this.user?.hierarchyData) {
      value = {
        id: this.user.hierarchyData.id,
        hierarchyName: this.user.hierarchyData.levelData.hierarchyName,
        hierarchyValue: this.user.hierarchyData.hierarchyValue,
      };
    }

    const task: IWorkflowTask = {
      taskType: {
        taskName: TaskType.UHC,
        workflow: { id: '02d25f3b-5758-4e91-bc3d-c34eda3d4c4f' },
      },
      entityName: `${this.user.firstName} ${this.user.lastName}`,
      value,
      reqValue: {
        id: this.selectedNodes.id,
        hierarchyName: this.selectedNodes.levelData.hierarchyName,
        hierarchyValue: this.selectedNodes.hierarchyValue,
      },
    };

    this.workflowService.createTask(task).subscribe(() => {
      this.toastService.displaySuccess(
        'Hierarchy change request sent successfully.'
      );
      this.pendingHierarchyReq = true;
    });

    this.displayHierarchyReq = false;
  }

  resetTreeSelect() {
    if (this.user?.hierarchyData) {
      this.selectedNodes = JSON.parse(JSON.stringify(this.user.hierarchyData));
      this.selectedNodes.label = `${this.selectedNodes.levelData.hierarchyName}: ${this.selectedNodes.hierarchyValue}`;
    }
  }

  onCountryChange(item) {
    this.selectedCountry = item.value;
    this.loadStates();
  }

  onStateChange(item) {
    this.selectedState = item.value;
    this.cities = [item.value];
    const cities = this.stateData.states.find(
      (s) => s.name === this.selectedState
    ).cities;
    if (cities.length) this.cities = cities;
    if (typeof cities === 'string') {
      this.cities = [cities];
    }
  }

  onStateCity(item) {
    this.selectedCity = item.value;
  }

  onTabChange(event) {}

  isUserFormValid(value) {
    this.validUserData = value;
  }

  onUserDataChange(data) {
    this.user = data;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
