import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ProfileInformationComponent } from './profile-information.component';
import { EditingStateService } from '@dc-lib/core/services/editing-state.service';
import { UserDataService } from '@dc-lib/core/services/admin/user-data.service';
import { Subject } from 'rxjs';
import { FormAction } from '@dc-lib/core/models/form-action';
import { IUser } from '@dc-lib/core/models/admin/users/user.model';
import { FieldsetModule } from 'primeng/fieldset';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { InputMaskModule } from 'primeng/inputmask';

describe('ProfileInformationComponent', () => {
  let component: ProfileInformationComponent;
  let fixture: ComponentFixture<ProfileInformationComponent>;
  let editingStateService: EditingStateService;
  let userDataService: UserDataService;
  let editingStateSubject: Subject<FormAction>;

  const mockUser: IUser = {
    firstName: 'John',
    lastName: 'Doe',
    title: 'Developer',
    email: '<EMAIL>',
    phone: '************',
    mobile: '************',
    address: '123 Main St',
    isActive: true,
  };

  beforeEach(async () => {
    editingStateSubject = new Subject<FormAction>();

    const editingStateServiceMock = {
      getValue: jest.fn().mockReturnValue(editingStateSubject),
      setValue: jest.fn(),
      getData: jest.fn().mockReturnValue([]),
      setData: jest.fn(),
      clearData: jest.fn(),
      editingState: { isEditing: false },
    };

    const userDataServiceMock = {
      setUser: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [ProfileInformationComponent],
      imports: [
        ReactiveFormsModule,
        BrowserAnimationsModule,
        FieldsetModule,
        DividerModule,
        InputTextModule,
        InputMaskModule,
      ],
      providers: [
        FormBuilder,
        { provide: EditingStateService, useValue: editingStateServiceMock },
        { provide: UserDataService, useValue: userDataServiceMock },
      ],
    }).compileComponents();
  });

  beforeEach(fakeAsync(() => {
    fixture = TestBed.createComponent(ProfileInformationComponent);
    component = fixture.componentInstance;
    editingStateService = TestBed.inject(EditingStateService);
    userDataService = TestBed.inject(UserDataService);
    component.user = mockUser;

    // Run initial lifecycle hooks
    component.ngOnInit();
    component.ngAfterViewInit();
    tick(); // Handle async operations
    fixture.detectChanges();
    tick(); // Handle any subsequent async operations
    fixture.detectChanges(); // Run another change detection cycle
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with disabled form', fakeAsync(() => {
    component.ngOnInit();
    tick();
    fixture.detectChanges();
    expect(component.form.disabled).toBeTruthy();
  }));

  describe('Form Validation', () => {
    beforeEach(fakeAsync(() => {
      // Enable form first
      component.form.enable();
      tick();
      fixture.detectChanges();

      // Set initial valid data
      component.form.patchValue({
        firstName: 'John',
        lastName: 'Doe',
        title: 'Developer',
        email: '<EMAIL>',
        phone: '************',
        mobile: '************',
        address: '123 Main St',
      });
      tick();
      fixture.detectChanges();
    }));

    it('should validate required fields', fakeAsync(() => {
      const form = component.form;

      // Clear required fields
      form.patchValue({
        firstName: '',
        lastName: '',
        email: 'invalid-email',
        title: 'Developer', // Keep non-required fields
        phone: '************',
        mobile: '************',
        address: '123 Main St',
      });
      tick();
      fixture.detectChanges();

      expect(form.get('firstName').errors).toHaveProperty('required');
      expect(form.get('lastName').errors).toHaveProperty('required');
      expect(form.get('email').errors).toHaveProperty('email');
      expect(form.valid).toBeFalsy();
    }));

    it('should be valid with correct data', fakeAsync(() => {
      // Verify current form state
      expect(component.form.valid).toBeTruthy();

      // Test with different valid data
      const newValidData = {
        firstName: 'Jane',
        lastName: 'Smith',
        title: 'Manager',
        email: '<EMAIL>',
        phone: '************',
        mobile: '************',
        address: '456 Oak St',
      };

      component.form.patchValue(newValidData);
      tick();
      fixture.detectChanges();

      expect(component.form.valid).toBeTruthy();
    }));
  });

  describe('Editing States', () => {
    beforeEach(fakeAsync(() => {
      component.ngOnInit();
      tick();
      fixture.detectChanges();
    }));

    it('should enable form on EDIT action', fakeAsync(() => {
      editingStateSubject.next(FormAction.EDIT);
      tick();
      fixture.detectChanges();
      expect(component.form.enabled).toBeTruthy();
    }));

    it('should disable form on CANCEL action', fakeAsync(() => {
      editingStateSubject.next(FormAction.CANCEL);
      tick();
      fixture.detectChanges();
      expect(component.form.disabled).toBeTruthy();
    }));

    it('should disable form on SUBMIT action', fakeAsync(() => {
      editingStateSubject.next(FormAction.SUBMIT);
      tick();
      fixture.detectChanges();
      expect(component.form.disabled).toBeTruthy();
    }));
  });

  describe('Form Changes', () => {
    beforeEach(fakeAsync(() => {
      component.ngOnInit();
      component.ngAfterViewInit();
      tick();
      fixture.detectChanges();
      component.form.enable();
    }));

    it('should emit form validity and user changes when form values change', fakeAsync(() => {
      const isFormValidSpy = jest.spyOn(component.isFormValid, 'emit');
      const userChangeSpy = jest.spyOn(component.userChange, 'emit');

      const newValues = {
        ...mockUser,
        firstName: 'Jane',
        email: '<EMAIL>',
      };

      component.form.patchValue(newValues);
      tick();
      fixture.detectChanges();

      expect(isFormValidSpy).toHaveBeenCalledWith(true);
      expect(userChangeSpy).toHaveBeenCalledWith(
        expect.objectContaining(newValues)
      );
      expect(userDataService.setUser).toHaveBeenCalledWith(
        expect.objectContaining(newValues)
      );
    }));
  });

  describe('Lifecycle Hooks', () => {
    it('should clean up subscriptions on destroy', () => {
      const nextSpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Template Integration', () => {
    beforeEach(fakeAsync(() => {
      component.ngOnInit();
      tick();
      fixture.detectChanges();
    }));

    it('should display user status correctly', () => {
      // Use a more specific selector to get the status span
      const statusElement = fixture.nativeElement.querySelector('.col-10 span');
      expect(statusElement.textContent.trim()).toBe('Active');
    });

    it('should disable inputs when not in editing mode', () => {
      const inputs = fixture.nativeElement.querySelectorAll('input');
      inputs.forEach((input: HTMLInputElement) => {
        expect(input.disabled).toBeTruthy();
      });
    });

    it('should enable inputs when in editing mode', fakeAsync(() => {
      editingStateSubject.next(FormAction.EDIT);
      tick();
      fixture.detectChanges();

      const inputs = fixture.nativeElement.querySelectorAll('input');
      inputs.forEach((input: HTMLInputElement) => {
        expect(input.disabled).toBeFalsy();
      });
    }));
  });

  describe('Animation Integration', () => {
    it('should handle fieldset animations', fakeAsync(() => {
      fixture.detectChanges();

      const fieldset = fixture.nativeElement.querySelector('p-fieldset');
      const event = new Event('animationend');
      fieldset.dispatchEvent(event);

      tick();
      fixture.detectChanges();

      expect(fieldset).toBeTruthy();
    }));
  });
});
