import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule } from '@angular/common';
import { Observable, of, Subject } from 'rxjs';
import { ProfileComponent } from './profile.component';
import { UserService } from '../../../services/admin/user.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { ToastService } from '../../../services/toast.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { RoleService } from '../../../services/admin/role.service';
import { OrganizationService } from '../../../services/admin/organization.service';
import { WorkflowService } from '../../../services/workflow/workflow.service';
import { FileService } from '../../../services/file/file.service';
import { UserDataService } from '../../../services/admin/user-data.service';
import { StripeService } from 'ngx-stripe';
import { FormAction } from '../../../models/form-action';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { TabViewModule } from 'primeng/tabview';
import { DialogModule } from 'primeng/dialog';
import { PickListModule } from 'primeng/picklist';
import { FieldsetModule } from 'primeng/fieldset';
import { InputTextModule } from 'primeng/inputtext';
import { InputMaskModule } from 'primeng/inputmask';
import { ListboxModule } from 'primeng/listbox';
import { DividerModule } from 'primeng/divider';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { TreeSelectModule } from 'primeng/treeselect';

// Mock styles
const mockStyles = `
  .p-tabview .p-tabview-nav { }
  .p-dialog .p-dialog-content { }
  .p-picklist .p-picklist-list { }
`;

// Add mock ProfileInformationComponent
@Component({
  selector: 'dorsey-profile-information',
  template: '<div>Mock Profile Information</div>',
})
class MockProfileInformationComponent {
  @Input() user: any;
  @Output() isFormValid = new EventEmitter<boolean>();
  @Output() userChange = new EventEmitter<any>();
}

// Mock EditionCTAsComponent
@Component({
  selector: 'dorsey-edition-ctas',
  template: '<div>Mock Edition CTAs</div>',
})
class MockEditionCTAsComponent {
  @Input() canEdit: boolean;
}

// Mock FileReader for testing
class MockFileReader {
  result: any = null;
  onloadend: any = null;

  readAsDataURL(file: any) {
    this.result = 'data:image/png;base64,mockbase64data';
    if (this.onloadend) {
      this.onloadend();
    }
  }
}

// Set up global FileReader mock
(window as any).FileReader = MockFileReader;

describe('ProfileComponent', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let userService: jest.Mocked<UserService>;
  let editingStateService: jest.Mocked<EditingStateService>;
  let toastService: jest.Mocked<ToastService>;
  let dialogMessageService: jest.Mocked<DialogMessageService>;
  let roleService: jest.Mocked<RoleService>;
  let organizationService: jest.Mocked<OrganizationService>;
  let workflowService: jest.Mocked<WorkflowService>;
  let fileService: jest.Mocked<FileService>;
  let userDataService: jest.Mocked<UserDataService>;

  const mockUser = {
    id: '1',
    firstName: 'Test',
    lastName: 'User',
    roles: [
      {
        name: 'User',
        code: 'USER',
        department: 'test',
        description: 'test',
        capabilities: [
          {
            component: ['unreal', 'path'],
            view: true,
            edit: true,
            create: true,
            delete: true,
          },
        ],
      },
    ],
    hierarchyData: {
      id: '1',
      levelData: { hierarchyName: 'Division' },
      hierarchyValue: 'TestDivision',
    },
  };

  beforeEach(async () => {
    const editingStateSubject = new Subject<FormAction>();

    const mockServices = {
      userService: {
        findUser: jest.fn().mockReturnValue(of(mockUser)),
        updateUser: jest.fn().mockReturnValue(of({})),
      },
      editingStateService: {
        getValue: jest.fn().mockReturnValue(editingStateSubject),
        setValue: jest.fn(),
        setData: jest.fn(),
        editingState: {
          // Add this property
          isEditing: false,
        },
      },
      toastService: {
        displaySuccess: jest.fn(),
        displayError: jest.fn(),
      },
      dialogMessageService: {
        displayError: jest.fn(),
      },
      roleService: {
        findAll: jest.fn().mockReturnValue(
          of([
            { name: 'User', code: 'USER' },
            { name: 'Admin', code: 'ADMIN' },
          ])
        ),
      },
      organizationService: {
        findOrgTree: jest.fn().mockReturnValue(of([])),
      },
      workflowService: {
        findInitiatedByMe: jest.fn().mockReturnValue(of([])),
        createTask: jest.fn().mockReturnValue(of({})),
      },
      fileService: {
        loadFile: jest.fn().mockReturnValue(of({ states: [{ name: 'Test State' }] })),
      },
      userDataService: {
        setUser: jest.fn(),
      },
    };

    await TestBed.configureTestingModule({
      declarations: [
        ProfileComponent,
        MockEditionCTAsComponent,
        MockProfileInformationComponent,
      ],
      imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        NoopAnimationsModule,
        TabViewModule,
        DialogModule,
        PickListModule,
        FieldsetModule,
        InputTextModule,
        InputMaskModule,
        ListboxModule,
        DividerModule,
        NgbTooltipModule,
        TreeSelectModule,
      ],
      providers: [
        FormBuilder,
        { provide: UserService, useValue: mockServices.userService },
        {
          provide: EditingStateService,
          useValue: mockServices.editingStateService,
        },
        { provide: ToastService, useValue: mockServices.toastService },
        {
          provide: DialogMessageService,
          useValue: mockServices.dialogMessageService,
        },
        { provide: RoleService, useValue: mockServices.roleService },
        {
          provide: OrganizationService,
          useValue: mockServices.organizationService,
        },
        { provide: WorkflowService, useValue: mockServices.workflowService },
        { provide: FileService, useValue: mockServices.fileService },
        { provide: UserDataService, useValue: mockServices.userDataService },
        { provide: StripeService, useValue: {} },
        {
          provide: 'STYLE_LOADER',
          useValue: {
            load: () => Promise.resolve(mockStyles),
          },
        },
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    userService = TestBed.inject(UserService) as jest.Mocked<UserService>;
    editingStateService = TestBed.inject(
      EditingStateService
    ) as jest.Mocked<EditingStateService>;
    toastService = TestBed.inject(ToastService) as jest.Mocked<ToastService>;
    dialogMessageService = TestBed.inject(
      DialogMessageService
    ) as jest.Mocked<DialogMessageService>;
    roleService = TestBed.inject(RoleService) as jest.Mocked<RoleService>;
    organizationService = TestBed.inject(
      OrganizationService
    ) as jest.Mocked<OrganizationService>;
    workflowService = TestBed.inject(
      WorkflowService
    ) as jest.Mocked<WorkflowService>;
    fileService = TestBed.inject(FileService) as jest.Mocked<FileService>;
    userDataService = TestBed.inject(
      UserDataService
    ) as jest.Mocked<UserDataService>;

    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    component.profileImageVar = { nativeElement: { value: '' } } as any;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user data on init', () => {
    component.ngOnInit();
    expect(workflowService.findInitiatedByMe).toHaveBeenCalled();
    expect(userService.findUser).toHaveBeenCalled();
    expect(component.user).toEqual(mockUser);
  });

  it('should save user data when valid', () => {
    component.user = mockUser;
    component.validUserData = true;
    component.saveData();
    expect(userService.updateUser).toHaveBeenCalled();
    expect(toastService.displaySuccess).toHaveBeenCalledWith(
      'Profile information updated successfully.'
    );
  });

  it('should not save user data when invalid', () => {
    component.validUserData = false;
    component.saveData();
    expect(dialogMessageService.displayError).toHaveBeenCalledWith(
      'Some fields are invalid.'
    );
    expect(userService.updateUser).not.toHaveBeenCalled();
  });

  it('should handle role change detection', () => {
    component.user = mockUser;
    component.targetRoles = [{ name: 'Admin', code: 'ADMIN' }];
    component.onRoleChange();
    expect(component.roleChanged).toBeTruthy();
  });

  it('should handle hierarchy change detection', () => {
    component.user = mockUser;
    component.selectedNodes = {
      ...mockUser.hierarchyData,
      hierarchyValue: 'NewDivision',
    };
    component.onHierarchyChange();
    expect(component.hierarchyChanged).toBeTruthy();
  });

  it('should submit role request', () => {
    component.user = mockUser;
    component.targetRoles = [{ name: 'Admin', code: 'ADMIN' }];
    component.submitRoleRequest();
    expect(workflowService.createTask).toHaveBeenCalled();
    expect(component.pendingRoleReq).toBeTruthy();
  });

  it('should submit hierarchy request', () => {
    component.user = mockUser;
    component.selectedNodes = {
      id: '2',
      levelData: { hierarchyName: 'NewDivision' },
      hierarchyValue: 'NewValue',
    };
    component.submitHierarchyRequest();
    expect(workflowService.createTask).toHaveBeenCalled();
    expect(component.pendingHierarchyReq).toBeTruthy();
  });

  it('should handle country change', () => {
    const countryItem = { value: 'USA' };
    component.onCountryChange(countryItem);
    expect(component.selectedCountry).toBe('USA');
    expect(fileService.loadFile).toHaveBeenCalled();
  });

  it('should clean up subscriptions on destroy', () => {
    const nextSpy = jest.spyOn(component['destroy$'], 'next');
    const completeSpy = jest.spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(nextSpy).toHaveBeenCalled();
    expect(completeSpy).toHaveBeenCalled();
  });

  describe('Template Integration', () => {
    it('should display user name in header', () => {
      component.user = mockUser;
      fixture.detectChanges();

      const headerElement = fixture.nativeElement.querySelector('h3');
      expect(headerElement.textContent).toContain(
        `${mockUser.firstName} ${mockUser.lastName}`
      );
    });

    it('should show profile information component', () => {
      const profileInfoElement = fixture.nativeElement.querySelector(
        'dorsey-profile-information'
      );
      expect(profileInfoElement).toBeTruthy();
    });

    it('should show edition CTAs component', () => {
      const ctasElement = fixture.nativeElement.querySelector(
        'dorsey-edition-ctas'
      );
      expect(ctasElement).toBeTruthy();
    });

    it('should display tabs correctly', () => {
      const tabView = fixture.nativeElement.querySelector('p-tabView');
      const tabs = fixture.nativeElement.querySelectorAll('.p-tabview-nav li');

      expect(tabView).toBeTruthy();
      expect(tabs.length).toBeGreaterThan(0);
    });

    it('should show role management section when user has permissions', () => {
      component.user = {
        ...mockUser,
        roles: [
          {
            ...mockUser.roles[0],
            capabilities: [
              {
                component: ['user', 'roles'],
                view: true,
                edit: true,
                create: true,
                delete: true,
              },
            ],
          },
        ],
      };

      component.displayRoleReq = true;
      fixture.detectChanges();

      const rolePickList = fixture.nativeElement.querySelector('p-pickList');
      expect(rolePickList).toBeTruthy();
    });

    it('should hide role management section when user lacks permissions', () => {
      component.user = {
        ...mockUser,
        roles: [
          {
            ...mockUser.roles[0],
            capabilities: [
              {
                component: ['user'],
                view: false,
                edit: false,
                create: false,
                delete: false,
              },
            ],
          },
        ],
      };
      fixture.detectChanges();

      const roleSection = fixture.nativeElement.querySelector('p-pickList');
      expect(roleSection).toBeFalsy();
    });

    it('should disable role request button when changes are pending', () => {
      component.pendingRoleReq = true;
      fixture.detectChanges();

      const roleRequestButton = fixture.nativeElement.querySelector(
        '.fa-pencil-square-o'
      );
      expect(roleRequestButton.classList.contains('cursor-auto')).toBeTruthy();
    });

    it('should show hierarchy selection when available', () => {
      // Configurar el usuario con los permisos necesarios
      component.user = {
        ...mockUser,
        roles: [
          {
            ...mockUser.roles[0],
            capabilities: [
              {
                component: ['user', 'hierarchy'],
                view: true,
                edit: true,
                create: true,
                delete: true,
              },
            ],
          },
        ],
      };

      // Configurar los nodos de jerarquía
      component.nodes = [
        {
          key: '1',
          label: 'Division 1',
          children: [],
        },
      ];

      // Activar el diálogo de jerarquía si es necesario
      component.displayHierarchyReq = true;
      fixture.detectChanges();

      const hierarchySelect =
        fixture.nativeElement.querySelector('p-treeSelect');
      expect(hierarchySelect).toBeTruthy();
    });

    it('should show loading state while fetching user data', () => {
      // Reset the mock to simulate loading
      userService.findUser.mockReturnValue(new Observable(() => {}));

      // Reset component's user property
      component.user = undefined;

      component.ngOnInit();
      fixture.detectChanges();

      // Verify loading state
      const profileInfo = fixture.nativeElement.querySelector(
        'dorsey-profile-information'
      );
      expect(profileInfo).toBeFalsy();

      // Verify service calls
      expect(userService.findUser).toHaveBeenCalled();
      expect(workflowService.findInitiatedByMe).toHaveBeenCalled();
    });

    it('should handle profile image upload', () => {
      const uploadSpy = jest.spyOn(component, 'onImageSelected');
      const fileInput =
        fixture.nativeElement.querySelector('input[type="file"]');

      const mockFile = new File([''], 'test.png', { type: 'image/png' });
      const event = { target: { files: [mockFile] } };

      fileInput.dispatchEvent(new Event('change'));
      component.onImageSelected(event);

      expect(uploadSpy).toHaveBeenCalled();
    });

    it('should show validation state', () => {
      component.validUserData = false;
      component.saveData();
      fixture.detectChanges();

      // Verify that the dialog service was called with the error message
      expect(dialogMessageService.displayError).toHaveBeenCalledWith(
        'Some fields are invalid.'
      );
    });
  });
});
