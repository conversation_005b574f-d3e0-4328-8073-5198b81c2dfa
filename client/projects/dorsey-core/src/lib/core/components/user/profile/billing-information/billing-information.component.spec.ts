import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
  flush,
} from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import {
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  EventEmitter,
} from '@angular/core';
import { BillingInformationComponent } from './billing-information.component';
import {
  StripeService,
  NgxStripeModule,
  StripeCardNumberComponent,
} from 'ngx-stripe';
import { FileService } from '../../../../services/file/file.service';
import { EditingStateService } from '../../../../services/editing-state.service';
import { PaymentService } from '../../../../services/payment/payment.service';
import { ToastService } from '../../../../services/toast.service';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { TitleCasePipe } from '@angular/common';
import { LoadingService } from '../../../../services/loading.service';
import { of, Subject } from 'rxjs';
import { FormAction } from '../../../../models/form-action';
import { FieldsetModule } from 'primeng/fieldset';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { InputMaskModule } from 'primeng/inputmask';
import { ButtonModule } from 'primeng/button';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { PaymentMethodResult } from '@stripe/stripe-js';
import { By } from '@angular/platform-browser';
import { StripeElements } from '@stripe/stripe-js';

describe('BillingInformationComponent', () => {
  let component: BillingInformationComponent;
  let fixture: ComponentFixture<BillingInformationComponent>;
  let editingStateService: EditingStateService;
  let fileService: FileService;
  let paymentService: PaymentService;
  let toastService: ToastService;
  let dialogMessageService: DialogMessageService;
  let loadingService: LoadingService;

  const mockUser = {
    email: '<EMAIL>',
    phone: '1234567890',
  };

  const mockCards = [
    {
      id: '1',
      cardNumber: '**** **** **** 1234',
      brand: 'visa',
      isPreferred: true,
      isExpired: false,
    },
    {
      id: '2',
      cardNumber: '**** **** **** 5678',
      brand: 'mastercard',
      isPreferred: false,
      isExpired: false,
    },
  ];

  const mockCountriesData = [
    { name: 'United States', iso2: 'US' },
    { name: 'Canada', iso2: 'CA' },
  ];

  beforeEach(async () => {
    const fileServiceMock = {
      loadFile: jest.fn().mockImplementation((file) => {
        if (file === 'countries') {
          return of(mockCountriesData);
        }
        return of({
          states: [{ name: 'California', cities: ['Los Angeles'] }],
        });
      }),
    };

    const paymentServiceMock = {
      findCards: jest.fn().mockReturnValue(of(mockCards)),
      createPaymentMethod: jest.fn().mockReturnValue(of({})),
      deleteCard: jest.fn().mockReturnValue(of({})),
      setPreferredCard: jest.fn().mockReturnValue(of({})),
    };

    const editingStateServiceMock = {
      getValue: jest.fn().mockReturnValue(new Subject()),
      setValue: jest.fn(),
      editingState: { isEditing: false },
    };

    await TestBed.configureTestingModule({
      declarations: [BillingInformationComponent],
      imports: [
        ReactiveFormsModule,
        FormsModule,
        NgxStripeModule.forRoot('dummy-key'),
        FieldsetModule,
        DividerModule,
        DropdownModule,
        InputMaskModule,
        ButtonModule,
        NoopAnimationsModule,
      ],
      providers: [
        FormBuilder,
        TitleCasePipe,
        { provide: FileService, useValue: fileServiceMock },
        { provide: PaymentService, useValue: paymentServiceMock },
        { provide: EditingStateService, useValue: editingStateServiceMock },
        {
          provide: ToastService,
          useValue: { displaySuccess: jest.fn(), displayError: jest.fn() },
        },
        {
          provide: DialogMessageService,
          useValue: { displayError: jest.fn(), displayWarning: jest.fn() },
        },
        { provide: LoadingService, useValue: { setLoading: jest.fn() } },
        {
          provide: StripeService,
          useValue: { createPaymentMethod: jest.fn().mockReturnValue(of({})) },
        },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(BillingInformationComponent);
    component = fixture.componentInstance;
    component.user = mockUser;
    component.isActive = true;

    editingStateService = TestBed.inject(EditingStateService);
    fileService = TestBed.inject(FileService);
    paymentService = TestBed.inject(PaymentService);
    toastService = TestBed.inject(ToastService);
    dialogMessageService = TestBed.inject(DialogMessageService);
    loadingService = TestBed.inject(LoadingService);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with disabled form', () => {
    expect(component.billingForm.disabled).toBeTruthy();
  });

  it('should load cards on init', (done) => {
    expect(paymentService.findCards).toHaveBeenCalled();
    expect(component.cards.length).toBe(2);

    // Use setTimeout to match the component's behavior
    setTimeout(() => {
      expect(component.selectedCard).toEqual(mockCards[0]);
      done();
    });
  });

  it('should load countries on init', () => {
    expect(fileService.loadFile).toHaveBeenCalledWith('countries');
    expect(component.countries).toEqual(['United States', 'Canada']);
  });

  it('should handle country change', () => {
    component.onCountryChange({ value: 'United States' });
    expect(component.selectedCountry).toBe('United States');
    expect(fileService.loadFile).toHaveBeenCalledWith('United States');
  });

  it('should handle state change', () => {
    component.stateData = {
      states: [{ name: 'California', cities: ['Los Angeles'] }],
    };
    component.onStateChange({ value: 'California' });
    expect(component.selectedState).toBe('California');
    expect(component.cities).toEqual(['Los Angeles']);
  });

  it('should handle add card action', () => {
    component.onAddCard();
    expect(component.isAddingCard).toBeTruthy();
    expect(component.selectedCard).toBeNull();
  });

  it('should validate form before creating payment method', () => {
    component.createPaymentMethod();
    expect(dialogMessageService.displayError).toHaveBeenCalledWith(
      'Some required fields are empty or invalid.'
    );
    expect(loadingService.setLoading).not.toHaveBeenCalled();
  });

  it('should handle card deletion', () => {
    component.selectedCard = mockCards[0];
    component.onDeleteCard();
    expect(dialogMessageService.displayWarning).toHaveBeenCalled();
  });

  it('should clean up on destroy', () => {
    const nextSpy = jest.spyOn(component['destroy$'], 'next');
    const completeSpy = jest.spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(nextSpy).toHaveBeenCalled();
    expect(completeSpy).toHaveBeenCalled();
  });

  describe('Template Integration', () => {
    beforeEach(() => {
      component.cards = mockCards.map((card) => ({
        label: `${card.cardNumber}: ${card.brand} ${
          card.isPreferred ? '(Preferred)' : ''
        } ${card.isExpired ? '(Expired)' : ''}`,
        value: card,
      }));
      component.selectedCard = mockCards[0];
      component.cardsData = mockCards;
      component.countries = ['United States', 'Canada'];
      component.stateData = {
        states: [
          {
            name: 'California',
            cities: ['Los Angeles', 'San Francisco'],
          },
        ],
      };
      component.cities = ['Los Angeles', 'San Francisco'];
      component.isAddingCard = false;
      component.selectedCountry = 'United States';
      component.selectedState = 'California';
      component.billingForm.patchValue({
        country: 'United States',
        state: 'California',
        city: 'Los Angeles',
      });
      fixture.detectChanges();
    });

    it('should display cards dropdown when cards exist', () => {
      const fieldset = fixture.nativeElement.querySelector('p-fieldset');
      expect(fieldset).toBeTruthy();

      const dropdown = fixture.nativeElement.querySelector('p-dropdown');
      expect(dropdown).toBeTruthy();

      // Verify the number of cards in the dropdown options
      expect(component.cards.length).toBe(2);
    });

    it('should show preferred status in card dropdown', () => {
      // Select the preferred card
      component.selectedCard = mockCards[0];
      fixture.detectChanges();

      // Find the dropdown element
      const dropdown = fixture.nativeElement.querySelector('p-dropdown');
      expect(dropdown).toBeTruthy();

      // Verify the card label includes "Preferred"
      const cardOption = component.cards.find(
        (card) => card.value.id === mockCards[0].id
      );
      expect(cardOption.label).toContain('(Preferred)');
    });

    it('should display add card form when isAddingCard is true', fakeAsync(() => {
      // Create mock DOM elements
      const mockElement = document.createElement('div');
      mockElement.setAttribute('id', 'postal-code-element');

      // Create a mock input element that will be used as the _element
      const mockInputElement = document.createElement('input');
      mockInputElement.type = 'text';
      mockInputElement.value = '';

      // Mock the ElementRef
      component.postalCodeRef = {
        nativeElement: mockElement,
      } as ElementRef;

      // Create a mock postal code element
      const mockPostalCodeElement = {
        mount: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === 'change') {
            mockPostalCodeElement.changeCallback = callback;
          }
        }),
        destroy: jest.fn(),
        update: jest.fn(),
        blur: jest.fn(),
        clear: jest.fn(),
        focus: jest.fn(),
        unmount: jest.fn(),
        changeCallback: null,
        _element: mockInputElement,
      };

      // Mock stripe elements
      const mockStripeElements = {
        create: jest.fn().mockReturnValue(mockPostalCodeElement),
        update: jest.fn(),
        getElement: jest.fn().mockReturnValue(mockPostalCodeElement),
      };

      const stripeService = TestBed.inject(StripeService);

      // Mock the stripe service
      (stripeService as any).stripe = mockStripeElements;
      stripeService.elements = jest
        .fn()
        .mockReturnValue(of(mockStripeElements));

      // Setup initial component state
      component.isAddingCard = true;
      component.isReady = true;
      component.isActive = true;

      // Enable editing mode
      editingStateService.editingState = { isEditing: true };
      const editingStateSubject = new Subject<FormAction>();
      (editingStateService.getValue as jest.Mock).mockReturnValue(
        editingStateSubject
      );

      // Call lifecycle methods in correct order
      component.ngOnInit();
      fixture.detectChanges();
      tick();

      // Trigger editing mode
      editingStateSubject.next(FormAction.EDIT);
      fixture.detectChanges();
      tick();

      // Explicitly enable the form
      component.billingForm.enable();
      fixture.detectChanges();
      tick();

      // Initialize form with valid data
      component.billingForm.patchValue({
        cardholderName: 'John Doe',
        addressLine1: '123 Main St',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90001',
        country: 'United States',
      });

      // Setup countries data
      component.countriesData = mockCountriesData;
      component.countries = mockCountriesData.map((country) => country.name);

      // Call loadStripeZipInput
      component.loadStripeZipInput();
      fixture.detectChanges();
      tick();

      // Simulate a change event on the postal code element
      if (mockPostalCodeElement.changeCallback) {
        mockPostalCodeElement.changeCallback({ complete: true });
      }

      fixture.detectChanges();
      tick();

      // Verify the postal code input was properly initialized
      expect(mockPostalCodeElement.mount).toHaveBeenCalled();

      // Verify form state
      expect(component.billingForm.disabled).toBeFalsy();
      expect(component.isAddingCard).toBeTruthy();
      expect(component.isReady).toBeTruthy();

      // Clean up
      mockPostalCodeElement.destroy();
    }));

    it('should disable form fields when not in editing mode', () => {
      const formInputs = fixture.nativeElement.querySelectorAll('input');
      formInputs.forEach((input: HTMLInputElement) => {
        expect(input.disabled).toBeTruthy();
      });
    });

    it('should enable form fields when in editing mode', () => {
      const editingStateSubject = new Subject<FormAction>();
      (editingStateService.getValue as jest.Mock).mockReturnValue(
        editingStateSubject
      );
      editingStateService.editingState = { isEditing: true };

      editingStateSubject.next(FormAction.EDIT);
      component.billingForm.enable();
      fixture.detectChanges();

      const formInputs = fixture.nativeElement.querySelectorAll('input');
      formInputs.forEach((input: HTMLInputElement) => {
        expect(input.disabled).toBeFalsy();
      });
    });

    it('should show country dropdown with loaded countries', () => {
      // Verify the countries data is loaded correctly in the component
      expect(component.countries).toEqual(['United States', 'Canada']);

      // Verify the form control value
      expect(component.billingForm.get('country').value).toBe('United States');
      expect(component.selectedCountry).toBe('United States');

      // First ensure we're not in add card mode
      component.isAddingCard = false;
      fixture.detectChanges();

      // Verify the dropdown exists
      const countryDropdown = fixture.nativeElement.querySelector('p-dropdown');
      expect(countryDropdown).toBeTruthy();
    });

    it('should show delete button for non-preferred cards', () => {
      // Enable editing mode
      editingStateService.editingState = { isEditing: true };

      // Select non-preferred card
      component.selectedCard = mockCards[1];
      fixture.detectChanges();

      const deleteButton = fixture.nativeElement.querySelector(
        'p-button[label="Delete"]'
      );
      expect(deleteButton).toBeTruthy();
      expect(deleteButton.getAttribute('ng-reflect-disabled')).toBe('false');
    });

    it('should disable delete button for preferred cards', () => {
      // Select preferred card
      component.selectedCard = mockCards[0];
      fixture.detectChanges();

      const deleteButton = fixture.nativeElement.querySelector(
        'p-button[label="Delete"]'
      );
      expect(deleteButton).toBeTruthy();
      expect(deleteButton.getAttribute('ng-reflect-disabled')).toBe('true');
    });

    it('should show validation errors when form is invalid', () => {
      // Setup card validation states to trigger validation error
      component.cardDataValidation = { complete: false };
      component.expDateDataValidation = { complete: false };
      component.cvcDataValidation = { complete: false };

      component.isAddingCard = true;
      component.billingForm.enable();
      fixture.detectChanges();

      // Directly call the method that performs validation
      component.createPaymentMethod();

      // Verify that error dialog was shown using the injected service
      expect(dialogMessageService.displayError).toHaveBeenCalledWith(
        'Some required fields are empty or invalid.'
      );

      // Verify form was marked as touched
      expect(component.billingForm.touched).toBeTruthy();

      // Verify required fields are marked as invalid
      const cardholderNameControl = component.billingForm.get('cardholderName');
      expect(cardholderNameControl.errors).toBeTruthy();
      expect(cardholderNameControl.errors['required']).toBeTruthy();
    });

    it('should show loading state during card operations', fakeAsync(() => {
      // Get the injected services
      const stripeService = TestBed.inject(StripeService);
      const loadingService = TestBed.inject(LoadingService);
      const paymentService = TestBed.inject(PaymentService);

      // Mock stripe elements
      const mockElements = {
        create: jest.fn().mockReturnValue({
          mount: jest.fn(),
          on: jest.fn(),
          destroy: jest.fn(),
        }),
        update: jest.fn(),
        fetchUpdates: jest.fn(),
        submit: jest.fn(),
        getElement: jest.fn(),
        _elements: [],
      } as any;

      // Mock card element with required StripeCardNumberElement properties
      component.card = {
        element: {
          mount: jest.fn(),
          destroy: jest.fn(),
          on: jest.fn(),
          once: jest.fn(),
          off: jest.fn(),
          update: jest.fn(),
          blur: jest.fn(),
          clear: jest.fn(),
          focus: jest.fn(),
          unmount: jest.fn(),
        },
        options: {},
        elementsOptions: {},
        id: 'card-element',
        disabled: false,
        complete: true,
        empty: false,
        error: null,
        value: {},
        nativeElement: document.createElement('div'),
        stripeElementsService: TestBed.inject(StripeService),
        cardGroup: null,
        stripeElementRef: { nativeElement: document.createElement('div') },
        containerClass: '',
        createElementOptions: jest.fn(),
        ngOnInit: jest.fn(),
        ngOnDestroy: jest.fn(),
        ngOnChanges: jest.fn(),
        createElement: jest.fn(),
        destroyStripeElement: jest.fn(),
        updateElement: jest.fn(),
        onChange: new EventEmitter(),
        onReady: new EventEmitter(),
        onFocus: new EventEmitter(),
        onBlur: new EventEmitter(),
        onEscape: new EventEmitter(),
      } as unknown as StripeCardNumberComponent;

      // Mock the stripe property
      Object.defineProperty(stripeService, 'stripe', {
        value: {
          elements: () => mockElements,
          createPaymentMethod: jest.fn(),
          confirmCardPayment: jest.fn(),
        },
        writable: true,
      });

      // Mock the elements method
      Object.defineProperty(stripeService, 'elements', {
        value: () => of(mockElements),
        writable: true,
      });

      // Enable the form first
      component.billingForm.enable();

      // Setup valid form state
      component.isAddingCard = true;
      component.cardDataValidation = { complete: true };
      component.expDateDataValidation = { complete: true };
      component.cvcDataValidation = { complete: true };

      // Setup form with all required fields
      component.billingForm.patchValue({
        cardholderName: 'John Doe',
        addressLine1: '123 Main St',
        addressLine2: '', // optional
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90001',
        country: 'United States',
      });

      // Mark all fields as touched to trigger validation
      Object.keys(component.billingForm.controls).forEach((key) => {
        const control = component.billingForm.get(key);
        control.markAsTouched();
        control.updateValueAndValidity();
      });

      // Ensure form is valid
      fixture.detectChanges();
      expect(component.billingForm.valid).toBeTruthy();

      // Mock payment service responses
      jest.spyOn(paymentService, 'createPaymentMethod').mockReturnValue(of({}));
      jest.spyOn(loadingService, 'setLoading');

      // Mock stripe service response with proper PaymentMethodResult type
      jest.spyOn(stripeService, 'createPaymentMethod').mockReturnValue(
        of({
          paymentMethod: {
            id: 'pm_123',
            object: 'payment_method',
            billing_details: {},
            card: {
              brand: 'visa',
              exp_month: 12,
              exp_year: 2024,
              last4: '4242',
              funding: 'credit',
              country: 'US',
            },
            created: 1234567890,
            customer: null,
            livemode: false,
            type: 'card',
            metadata: {},
          },
          error: undefined,
        } as PaymentMethodResult)
      );

      fixture.detectChanges();

      // Call the method
      component.createPaymentMethod();

      // Advance the virtual timer
      tick();
      fixture.detectChanges();

      // Complete all pending async operations
      flush();

      // Verify loading states were called in order
      expect(loadingService.setLoading).toHaveBeenNthCalledWith(1, true);
      expect(loadingService.setLoading).toHaveBeenNthCalledWith(2, false);
    }));

    it('should have correct selected card in dropdown', () => {
      const titleCasePipe = new TitleCasePipe();

      // Setup mock cards data
      component.cardsData = mockCards;
      component.selectedCard = mockCards[0];

      // Manually trigger loadCards logic
      component.cards = mockCards.map((pm) => ({
        label: `${pm.cardNumber}: ${titleCasePipe.transform(pm.brand)} ${
          pm.isPreferred ? '(Preferred)' : ''
        } ${pm.isExpired ? '(Expired)' : ''}`,
        value: pm,
      }));

      fixture.detectChanges();

      // Verify the selected card in the component
      expect(component.selectedCard).toEqual(mockCards[0]);

      // Verify the dropdown options contain the correct card
      const cardOption = component.cards.find(
        (card) => card.value === mockCards[0]
      );
      expect(cardOption).toBeTruthy();
      expect(cardOption.value).toEqual(mockCards[0]);
    });
  });
});
