<p-fieldset *ngIf="!isAddingCard" class="w-100" legend="My Cards">
  <div class="d-flex justify-content-between mb-2">
    <div class="d-flex">
      <p-dropdown class="d-flex w-auto col-10 me-2 p-0" emptyMessage="No card found" placeholder="Select a Card" [options]="cards"
                  [(ngModel)]="selectedCard" (onChange)="onCardChange($event)"></p-dropdown>
      <p-button *ngIf="!selectedCard?.isPreferred" label="Make Preferred" styleClass="p-button-success" [disabled]="!editingStateService.editingState.isEditing" (onClick)="onMakePreferred()"></p-button>
    </div>
    <div class="d-flex">
      <p-button class="me-2" label="Delete" styleClass="p-button-danger" [disabled]="!editingStateService.editingState.isEditing || !selectedCard" (onClick)="onDeleteCard()"></p-button>
      <p-button label="Add" styleClass="p-button-success" [disabled]="!editingStateService.editingState.isEditing" (onClick)="onAddCard()"></p-button>
    </div>
  </div>
</p-fieldset>
<ng-container *ngIf="isAddingCard">
  <form [formGroup]="billingForm">
    <p-fieldset class="w-100" legend="Card Information">
      <div class="row">
        <div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">*Cardholder Name</label>
            </div>
            <div class="col-10 p-0">
              <input [ngClass]="{'border-0': !editingStateService.editingState.isEditing, 'is-invalid': check.cardholderName.invalid && (check.cardholderName.dirty || check.cardholderName.touched)}" type="text" pInputText name="cardholderName" formControlName="cardholderName"  />
            </div>
          </div>
          <p-divider></p-divider>

          <ngx-stripe-card-group #cardGroup *ngIf="isReady">
            <div class="d-flex flex-row">
              <div class="col-2">
                <label class="m-0">*Credit Card Number</label>
              </div>
              <div class="stripe-card-input-cont" [ngClass]="{'is-invalid-stripe-input': (cardDataValidation && !cardDataValidation.complete)}">
                <ngx-stripe-card-number
                  [ngClass]="{'disabled': !editingStateService.editingState.isEditing}"
                  class="p-0"
                  [options]="stripeOptions"
                  (change)="onCardNumberChange($event)"
                >
                </ngx-stripe-card-number>
              </div>
            </div>
            <p-divider></p-divider>
            <div class="d-flex flex-row">
              <div class="col-2">
                <label class="m-0">*Expiration Date</label>
              </div>
              <div class="stripe-exp-input-cont" [ngClass]="{'is-invalid-stripe-input': (expDateDataValidation && !expDateDataValidation.complete)}">
                <ngx-stripe-card-expiry
                  [ngClass]="{'disabled': !editingStateService.editingState.isEditing}"
                  class="p-0 stripe-exp-input-width" [options]="stripeOptions"
                  (change)="onExpDateChange($event)"
                >
                </ngx-stripe-card-expiry>
              </div>
            </div>
            <p-divider></p-divider>
            <div class="d-flex flex-row">
              <div class="col-2">
                <label class="m-0">*CVV/CVC</label>
              </div>
              <div class="stripe-cvc-input-cont" [ngClass]="{'is-invalid-stripe-input': (cvcDataValidation && !cvcDataValidation.complete)}">
                <ngx-stripe-card-cvc
                  [ngClass]="{'disabled': !editingStateService.editingState.isEditing}"
                  class="p-0 stripe-cvc-input-width"
                  [options]="stripeOptions"
                  (change)="onCvcChange($event)">
                </ngx-stripe-card-cvc>
              </div>
            </div>
            <p-divider></p-divider>
          </ngx-stripe-card-group>
        </div>
      </div>
    </p-fieldset>
    <p-fieldset class="w-100" legend="Billing Address">
      <div class="row">
        <div>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">*Country</label>
            </div>
            <div class="col-10 p-0">
              <p-dropdown class="d-flex w-auto col-10 p-0"  placeholder="Select a Country" [ngClass]="{'is-invalid-dropdown': check.country.invalid && (check.country.dirty || check.country.touched)}" [options]="countries"
                          formControlName="country" (onChange)="onCountryChange($event)"
                          [disabled]="!editingStateService.editingState.isEditing"></p-dropdown>
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">*State/Province</label>
            </div>
            <div class="col-10 p-0">
              <p-dropdown class="d-flex w-auto col-10 p-0"  placeholder="Select a State/Province" [ngClass]="{'is-invalid-dropdown': check.state.invalid && (check.state.dirty || check.state.touched)}" [options]="states"
                          formControlName="state" (onChange)="onStateChange($event)"
                          [disabled]="!editingStateService.editingState.isEditing"></p-dropdown>
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">*City</label>
            </div>
            <div class="col-10 p-0">
              <p-dropdown class="d-flex w-auto col-10 p-0"  placeholder="Select a City" [ngClass]="{'is-invalid-dropdown': check.city.invalid && (check.city.dirty || check.city.touched)}" [options]="cities"
                          formControlName="city" (onChange)="onStateCity($event)"
                          [disabled]="!editingStateService.editingState.isEditing"></p-dropdown>
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">*Address line 1</label>
            </div>
            <div class="col-10 p-0">
              <input [ngClass]="{'border-0': !editingStateService.editingState.isEditing, 'is-invalid': check.addressLine1.invalid && (check.addressLine1.dirty || check.addressLine1.touched)}"  type="text" pInputText name="addressLine1" formControlName="addressLine1"  />
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">Address line 2</label>
            </div>
            <div class="col-10 p-0">
              <input type="text" pInputText name="addressLine2" formControlName="addressLine2"  />
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">*ZIP Code</label>
            </div>
            <div class="col-10 p-0">
              <p-inputMask mask="99999" [ngClass]="{'border-0': !editingStateService.editingState.isEditing, 'is-invalid-inputmask': check.zipCode.invalid && (check.zipCode.dirty || check.zipCode.touched)}" name="zipCode" formControlName="zipCode"></p-inputMask>
            </div>
          </div>
          <p-divider></p-divider>
        </div>
      </div>
    </p-fieldset>
  </form>
</ng-container>
<ng-container *ngIf="selectedCard">
    <p-fieldset class="w-100" legend="Card Information">
      <div class="row">
        <div>
         <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">Cardholder Name</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.cardholderName}}</span>
            </div>
          </div>
          <p-divider></p-divider>

            <div class="d-flex flex-row">
              <div class="col-2">
                <label class="m-0">Card Number</label>
              </div>
              <div class="col-10 p-2">
                <span>{{selectedCard?.cardNumber}}</span>
              </div>
            </div>
            <p-divider></p-divider>

          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">Card Brand</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.brand}}</span>
            </div>
          </div>
          <p-divider></p-divider>

            <div class="d-flex flex-row">
              <div class="col-2">
                <label class="m-0">Expiration Date</label>
              </div>
              <div class="col-10 p-2">
                <span>{{selectedCard?.expDate}}</span>
              </div>
            </div>
            <p-divider></p-divider>
        </div>
      </div>
    </p-fieldset>
    <p-fieldset class="w-100" legend="Billing Address">
      <div class="row">
        <div>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">Country</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.country}}</span>
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">State/Province</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.state}}</span> </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">City</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.city}}</span></div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">Address line 1</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.addressLine1}}</span>
            </div>
          </div>
          <p-divider></p-divider>
          <div class="d-flex flex-row">
            <div class="col-2">
              <label class="m-0">Address line 2</label>
            </div>
            <div class="col-10 p-2">
              <span>{{selectedCard?.addressLine2}}</span>
            </div>
          </div>
          <p-divider></p-divider>
            <div class="d-flex flex-row">
              <div class="col-2">
                <label class="m-0">ZIP Code</label>
              </div>
                      <div class="col-10 p-2">
                        <span>{{selectedCard?.zipCode}}</span>
                      </div>
            </div>
            <p-divider></p-divider>
        </div>
      </div>
    </p-fieldset>
</ng-container>
