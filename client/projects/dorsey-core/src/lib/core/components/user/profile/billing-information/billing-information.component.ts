import {
  AfterViewInit,
  Component,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { StripeCardNumberComponent, StripeService } from 'ngx-stripe';
import { FileService } from '../../../../services/file/file.service';
import { EditingStateService } from '../../../../services/editing-state.service';
import { takeUntil } from 'rxjs/operators';
import { firstValueFrom, Subject } from 'rxjs';
import { FormAction } from '../../../../models/form-action';
import { handleCancelAction } from '../../../../utils/grid-utils';
import { IUserBillingInformation } from '../../../../models/admin/users/user-billing-information.model';
import { PaymentService } from '../../../../services/payment/payment.service';
import { ToastService } from '../../../../services/toast.service';
import { IUser } from '../../../../models/admin/users/user.model';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { TitleCasePipe } from '@angular/common';
import { SelectItem } from 'primeng/api';
import { LoadingService } from '../../../../services/loading.service';

@Component({
  selector: 'dorsey-billing-information',
  templateUrl: './billing-information.component.html',
  styleUrls: ['./billing-information.component.scss'],
  providers: [TitleCasePipe],
})
export class BillingInformationComponent implements OnInit, AfterViewInit {
  @ViewChild(StripeCardNumberComponent) card: StripeCardNumberComponent;
  @ViewChild('postalCode', { static: false }) postalCodeRef;
  // @Input() billingInformation: IUserBillingInformation;
  @Input() user: IUser;
  @Input() isActive: boolean;

  isReady = false;

  private readonly destroy$ = new Subject<void>();

  postalCodeError: string;
  countries: string[];
  countriesData: any[];
  selectedCountry: string;

  stateData: any;
  states: string[];
  selectedState: string;

  cities: string[];
  selectedCity: string;

  stripe: any;
  elements: any;
  stripeOptions = {
    style: {
      base: {
        color: '#32325d',
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
        fontSize: '18px', // Increase the font size here
        lineHeight: '40px', // Set the line height to make it more readable
        padding: '10px', // Add padding for a more spacious input
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a',
      },
    },
  };
  cardDataValidation: any;
  expDateDataValidation: any;
  cvcDataValidation: any;

  billingForm = this.fb.group({
    cardholderName: ['', Validators.required],
    // cardNumber: ['', Validators.required],
    // expDate: ['', Validators.required],
    // cvc: ['', Validators.required],
    addressLine1: ['', Validators.required],
    addressLine2: [''],
    city: ['', Validators.required],
    state: ['', Validators.required],
    country: ['', Validators.required],
    zipCode: ['', Validators.required],
  });

  cards: SelectItem[];
  selectedCard: IUserBillingInformation;
  isAddingCard = false;
  cardsData: IUserBillingInformation[];

  constructor(
    private fb: FormBuilder,
    private stripeService: StripeService,
    private fileService: FileService,
    public editingStateService: EditingStateService,
    private paymentService: PaymentService,
    private toastService: ToastService,
    private dialogMessageService: DialogMessageService,
    private titleCasePipe: TitleCasePipe,
    public loadingService: LoadingService
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        if (this.isActive) {
          switch (value) {
            case FormAction.SAVE:
              if (this.isAddingCard) {
                this.createPaymentMethod();
              } else {
                this.toastService.displaySuccess(
                  'Information saved successfully.'
                );
                this.editingStateService.setValue(FormAction.SUBMIT);
              }
              break;
            case FormAction.EDIT:
              this.billingForm.enable();
              break;
            case FormAction.CANCEL:
              this.billingForm.disable();
              this.isAddingCard = false;
              break;
            case FormAction.SUBMIT:
              this.billingForm.disable();
              break;
          }
        }
      });
    firstValueFrom(handleCancelAction(this.editingStateService, this));
  }

  ngOnInit(): void {
    this.billingForm.disable();
    // this.billingAddressForm.disable();
    this.loadCards();
    this.loadCountries();
    this.billingFormMonitor();
  }

  ngAfterViewInit(): void {
    this.isReady = true;
    setTimeout(() => {
      this.loadStripeZipInput();
    });
  }

  loadStripeZipInput() {
    this.stripeService
      .elements()
      .pipe(takeUntil(this.destroy$))
      .subscribe((elements) => {
        this.stripe = this.stripeService.stripe;
        this.elements = elements;

        const postalCode = this.elements.create(
          'postalCode',
          this.stripeOptions
        );
        postalCode.mount(this.postalCodeRef?.nativeElement);

        postalCode.on('change', (event) => {
          if (event.error) {
            this.postalCodeError = event.error.message; // Handle error from Stripe
          } else {
            this.postalCodeError = ''; // Reset error if input is valid
          }
        });

        this.addPostalCodeInputRestriction(postalCode);
      });
  }

  private loadCountries() {
    this.fileService
      .loadFile('countries')
      .pipe(takeUntil(this.destroy$))
      .subscribe((resp) => {
        this.countries = resp.map((s) => s.name);
        this.countriesData = resp;
      });
  }

  billingFormMonitor() {
    this.billingForm
      .get('cardholderName')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        const letters = value.replace(/[^A-Za-z\s]/g, '');
        if (value !== letters) {
          this.billingForm
            .get('cardholderName')
            ?.setValue(letters, { emitEvent: false });
        }
      });
  }

  private addPostalCodeInputRestriction(postalCode) {
    const element = postalCode._element;

    // Prevent non-numeric input and restrict to 5 digits during keydown
    element.addEventListener('keydown', (event: KeyboardEvent) => {
      const inputValue = element.value;
      const isNumericKey = /\d/.test(event.key); // Check if the key pressed is numeric

      // Prevent non-numeric input and restrict to 5 digits
      if (inputValue.length >= 5 || !isNumericKey) {
        event.preventDefault();
      }
    });
  }

  onCountryChange(item) {
    this.selectedCountry = item.value;
    this.loadStates();
  }

  onStateChange(item) {
    this.selectedState = item.value;
    this.cities = [item.value];
    const cities = this.stateData.states.find(
      (s) => s.name === this.selectedState
    ).cities;
    if (cities.length) this.cities = cities;
    if (typeof cities === 'string') {
      this.cities = [cities];
    }

    // this.loadStates();
  }

  onStateCity(item) {
    this.selectedCity = item.value;
  }

  private loadStates() {
    this.fileService
      .loadFile(this.selectedCountry)
      .pipe(takeUntil(this.destroy$))
      .subscribe((resp) => {
        this.stateData = resp;
        this.states = resp.states.map((s) => s.name);
      });
  }

  createPaymentMethod() {
    if (
      !this?.cardDataValidation?.complete ||
      !this?.expDateDataValidation?.complete ||
      !this?.cvcDataValidation?.complete
    ) {
      this.dialogMessageService.displayError(
        'Some required fields are empty or invalid.'
      );
      this.billingForm.markAllAsTouched();

      if (!this.cardDataValidation)
        this.cardDataValidation = { complete: false };
      if (!this.expDateDataValidation)
        this.expDateDataValidation = { complete: false };
      if (!this.cvcDataValidation) this.cvcDataValidation = { complete: false };
      return;
    }
    if (this.billingForm.valid) {
      this.loadingService.setLoading(true);
      this.stripeService
        .createPaymentMethod({
          type: 'card',
          card: this.card.element, //Reference to card component
          billing_details: {
            name: this.billingForm.controls.cardholderName.value,
            email: this.user.email,
            phone: this.user.phone,
            address: {
              line1: this.billingForm.controls.addressLine1.value,
              line2: this.billingForm.controls.addressLine2.value,
              city: this.billingForm.controls.city.value,
              state: this.billingForm.controls.state.value,
              postal_code: this.billingForm.controls.zipCode.value,
              // country: this.billingForm.controls.country.value,
              country: this.countriesData.find(
                (c) => c.name === this.billingForm.controls.country.value
              ).iso2,
            },
          },
        })
        .subscribe((result) => {
          if (result.paymentMethod) {
            this.paymentService
              .createPaymentMethod(result.paymentMethod.id)
              .subscribe(
                () => {
                  this.isAddingCard = false;
                  this.toastService.displaySuccess('Card added successfully.');
                  this.editingStateService.setValue(FormAction.SUBMIT);
                  this.loadCards();
                },
                () => {
                  this.toastService.displayError(
                    'Failed adding this card, please check the data'
                  );
                }
              );
          } else if (result.error) {
            this.toastService.displayError(result.error.message);
          }
        })
        .add(() => this.loadingService.setLoading(false));
    } else {
      this.dialogMessageService.displayError(
        'Some required fields are empty or invalid.'
      );
      this.billingForm.markAllAsTouched();
    }
  }

  onCardChange(card) {}

  onAddCard() {
    this.isAddingCard = true;
    this.selectedCard = null;
  }

  onDeleteCard() {
    this.dialogMessageService.displayWarning(
      `Are you sure you wish to delete card number <b>"${this.selectedCard.cardNumber}"</b>?`,
      true,
      () => {
        this.deleteCard();
      },
      () => {}
    );
  }

  private deleteCard() {
    this.paymentService.deleteCard(this.selectedCard.id).subscribe(
      () => {
        this.toastService.displaySuccess(
          'Billing information updated successfully.'
        );

        this.editingStateService.setValue(FormAction.SUBMIT);
        this.loadCards();
      },
      (error) => {
        this.toastService.displayError('Card deletion failed.');
      }
    );
  }

  onCardNumberChange(event) {
    this.cardDataValidation = event;
  }

  onExpDateChange(event) {
    this.expDateDataValidation = event;
  }

  onCvcChange(event) {
    this.cvcDataValidation = event;
  }

  private loadCards() {
    this.paymentService
      .findCards()
      .subscribe((resp: IUserBillingInformation[]) => {
        this.cardsData = resp;

        this.cards = [];
        for (const pm of resp) {
          if (pm.isPreferred) {
            setTimeout(() => {
              this.selectedCard = pm;
            });
          }

          this.cards.push({
            label: `${pm.cardNumber}: ${this.titleCasePipe.transform(
              pm.brand
            )} ${pm.isPreferred ? '(Preferred)' : ''}  ${
              pm.isExpired ? '(Expired)' : ''
            }`,
            value: pm,
          });
        }
      });
  }

  onMakePreferred() {
    this.paymentService.setPreferredCard(this.selectedCard.id).subscribe(
      () => {
        this.toastService.displaySuccess(
          'Billing information updated successfully.'
        );

        this.editingStateService.setValue(FormAction.SUBMIT);
        this.loadCards();
      },
      (error) => {
        this.toastService.displayError('Unable to set this card as preferred.');
      }
    );
  }

  get check() {
    return this.billingForm.controls;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
