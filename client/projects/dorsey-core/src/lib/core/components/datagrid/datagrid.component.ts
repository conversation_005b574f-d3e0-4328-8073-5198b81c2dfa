import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';

import {
  AbstractColDef,
  ColDef,
  ColGroupDef,
  Column,
  ColumnApi,
  Context,
  GetDataPath,
  GridApi,
  ProcessRowGroupForExportParams,
  RowNode,
  SuppressKeyboardEventParams,
} from 'ag-grid-community';
import { DatagridMsgService } from './services/datagrid-msg.service';
import { DataGridMessage } from './models/datagrid-message.model';
import { Actions } from './models/actions.model';
import { ActionCellRendererComponent } from './customs/cell-renderers/action-cell-renderer/action-cell-renderer.component';
import { GridRow } from './models/grid-row.model';
import { ActionState } from './models/action-state';
import { DatagridActionsCta } from './models/enums/datagrid-actions-cta';
import { EditingStateService } from '../../services/editing-state.service';
import { FormAction } from '../../models/form-action';
import { Subject, Subscription } from 'rxjs';
import { DialogMessageService } from '../../services/dialog-message.service';
import { takeUntil } from 'rxjs/operators';
import { AccountService } from '../../auth/account.service';
import { RoleActions } from '../../models/enums/role-actions';

@Component({
  selector: 'datagrid[id]',
  templateUrl: './datagrid.component.html',
  styleUrls: ['./datagrid.component.scss'],
})
export class DatagridComponent implements OnInit, OnChanges, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  @Input() id: string; // Grid ID (Required)
  @Input() columns: any; // Data grid columns
  @Input() rows: GridRow<any>[] = []; // Data grid rows
  @Input() newRowData: () => any; // Data grid new row
  @Input() deleteRowData: (message: DataGridMessage) => any; // Data grid delete row
  @Input() showAddDropdownCta: boolean; // Controls visibility of add dropdown CTA
  @Input() showMoneyDropdownCta: boolean;
  @Input() showAddSingleRow: boolean;
  @Input() showActions: Actions;
  @Input() validator: (rowIndex?: number) => boolean;
  @Input() showDuplicateCta: boolean;
  @Input() showGrandTotal: boolean;
  @Input() showPagination = false; // Controls visibility and activation of pagination
  @Input() addDropdownCtaTooltip = 'Add'; // Add dropdown CTA tooltip
  @Input() tabToNextCell;
  @Input() searchValue: string;
  @Input() isExternalFilterPresent;
  @Input() doesExternalFilterPass;
  @Input() excelMessage = 'UNCLASSIFIED / FOUO';
  @Input() suppressKeyboardEvent = true;
  @Input() rowDragManaged = true;
  @Input() pinnedTopRowData = [];
  @Input() pinnedBottomRowData = [];
  @Input() overlayNoRowsTemplate: string;
  @Input() isMasterDetail = false;
  @Input() detailCellRendererParams = null;
  @Input() addRowTooltip: string;
  @Input() duplicateTooltip: string;
  @Input() gridOptions: any;
  @Input() calculateContainerWidth: boolean;
  @Input() rowClassDef: string;
  @Input() agGridClass: string;
  @Input() dynamicGrid: any;
  @Input() rowHeight: any;
  @Input() name = 'export';
  @Input() getContentBelowRow: (index: number) => [][];
  @Input() showExportCta = true;
  @Input() showVisibilityCta: boolean;
  @Input() sideBar: any;
  @Input() removableColumns: string[] = [];
  @Input() getRowStyle: any;
  @Input() suppressRowHoverHighlight: any;
  @Input() suppressMovable = true;
  @Input() suppressEditOnDoubleClick = true;
  @Input() hasAutoHeight = true;
  @Input() hasActionHandler = false;
  @Input() customDeleteWarning = false;
  @Input() isPermanentlyDeleting = false;
  @Input() suppressColumnsMenu = false;
  @Input() columnsSorting = true;
  @Input() columnHoverHighlight = false;
  @Input() rowStyle: any;
  @Input() pageSize = 25;
  @Input() treeData = false;
  @Input() groupDefaultExpanded = 0;
  @Input() getDataPath: GetDataPath;
  @Input() autoGroupColumnDef: ColDef;
  @Input() viewPath: string;

  readonly ROW_HEIGHT = 40;
  readonly ACTION_STATE = ActionState;

  @Input() extras: TemplateRef<any>;
  @Input() displayExtras = true;
  @Input() hasFilter = true;
  @Input() gridTitle: string;

  @Output() cellAction: EventEmitter<DataGridMessage> =
    new EventEmitter<DataGridMessage>();
  @Output() addCtaEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() gridIsReady: EventEmitter<GridApi> = new EventEmitter<GridApi>();
  @Output() virtualColumnsChanged: EventEmitter<GridApi> =
    new EventEmitter<GridApi>();
  @Output() rowDataChanged: EventEmitter<GridApi> = new EventEmitter<GridApi>();
  @Output() rowDragEnterEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() rowDragLeaveEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() rowDragEndEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output() columnIsReady: EventEmitter<ColumnApi> =
    new EventEmitter<ColumnApi>();
  @Output() columnResized: EventEmitter<ColumnApi> = new EventEmitter<any>();
  @Output() virtualRowRemoved: EventEmitter<[]> = new EventEmitter<[]>();
  @Output() eyeClicked: EventEmitter<void> = new EventEmitter<void>();
  @Output() pinnedRowDataChanged: EventEmitter<void> = new EventEmitter<void>();
  @Output() rowEditingStopped: EventEmitter<void> = new EventEmitter<void>();
  @Output() rowEditingStarted: EventEmitter<void> = new EventEmitter<void>();
  @Output() rowGroupOpened: EventEmitter<void> = new EventEmitter<void>();
  @Output() filterGrid: EventEmitter<any> = new EventEmitter<any>();
  @Output() clearFilter: EventEmitter<void> = new EventEmitter<void>();
  @Output() searchValueChange: EventEmitter<string> =
    new EventEmitter<string>();
  @Output() cellValueChanged: EventEmitter<any> = new EventEmitter<any>();

  editingStateServiceSub: Subscription;
  excelMessageHeader: any;
  excelMessageFooter: any;
  excelStyles: any;
  defaultColDef: any;
  api: GridApi;
  columnApi: ColumnApi;
  gridId: string;
  style: string;
  addSingleRowClicked: boolean;
  currentRowIndex: string;
  currentColId: string;
  rowDataState: IRowDataState = {};
  editingFieldIndex: number;
  rowId = 0;

  constructor(
    private datagridMsgService: DatagridMsgService,
    private dialogMessageService: DialogMessageService,
    private editingStateService: EditingStateService,
    private accountService: AccountService
  ) {
    this.editingStateServiceSub = editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        if (FormAction.SAVE != value) {
          this.showActionsByEditingState();
        }
      });
  }

  onSuppressKeyboardEvent = (params: SuppressKeyboardEventParams) => {
    if (this.suppressKeyboardEvent) {
      if (params.event.code.toLowerCase() === 'tab') {
        return !this.tabToNextCell;
      }
      return true;
    }
    return false;
  };

  private showActionsByEditingState() {
    const isEditing = this.editingStateService.getEditingState();

    if (this.showAddSingleRow !== undefined) {
      this.showAddSingleRow = isEditing;
    }

    if (this.showActions) {
      if (!this.showActions.canView) {
        (this.columns.find((c) => c.field === 'action') ?? {}).hide =
          !isEditing;
      }

      if (this.showActions.canEdit) {
        this.rowDataState.isEditing = isEditing;
        this.rows.forEach((r) => (r.isEditing = !r.isEditing));
        this.suppressEditOnDoubleClick = !isEditing;
        if (isEditing) {
          this.columnApi.resetColumnState();
        }
      }

      this.api.setColumnDefs(this.columns);
    }
  }

  handleDuplicateCta(itemId: string) {
    this.addCtaEvent.emit({ action: itemId });
  }

  onRowDragEnter(event: any): void {
    this.rowDragEnterEvent.emit(event);
  }

  onRowDragLeave(event: any): void {
    this.rowDragLeaveEvent.emit(event);
  }

  onRowDragEnd(event: any): void {
    this.rowDragEndEvent.emit(event);
  }

  onModelUpdated() {}

  onCellClicked(event: any): void {
    const message: DataGridMessage = new DataGridMessage();

    message.visibleRowIndex = event.node.rowIndex;
    message.rowIndex = event.rowIndex;
    message.columnId = event.column.colId;
    message.action = DatagridActionsCta.NONE;
    message.rowData = event.data;
    message.messageType = 'grid-cell';
    message.event = event;
    this.cellAction.emit(message);
  }

  onGridReady(params: any) {
    this.api = params.api;
    this.gridId = params.api.context.contextParams.gridId;
    this.columnApi = params.columnApi;
    this.sizeGridColumnsToFit();
    this.api.sizeColumnsToFit();
    this.gridIsReady.emit(this.api);
    this.columnIsReady.emit(this.columnApi);
    this.api.paginationSetPageSize(this.pageSize);
    this.setActionColumn();

    this.api.setColumnDefs(this.columns);
    this.setNotEditableClass();
  }

  onGridSizeChanged() {
    if (this.api.getPinnedTopRow(0)) {
      this.api.hideOverlay();
    }
    this.sizeGridColumnsToFit();
  }

  onPinnedRowDataChanged(event) {
    this.pinnedRowDataChanged.emit(event);
  }

  onRowStopEditing(event) {
    this.rowEditingStopped.emit(event);
  }

  onRowStartEditing(event) {
    if (!this.showActions.canEdit) {
      const nonSelectedGridRows = document.querySelectorAll(
        `#${this.id} div[ref="eContainer"] > div[role="row"]:not(.ag-row-editing)`
      );

      nonSelectedGridRows.forEach((row: HTMLElement) => {
        // Prevent stop editing when clicking outside current editing row.
        row.style.pointerEvents = 'none';
      });
    }
    this.rowEditingStarted.emit(event);
  }

  onRowGroupOpened(event) {
    this.rowGroupOpened.emit(event);
  }

  onVirtualRowRemoved($event) {
    this.sizeGridColumnsToFit();
    this.virtualRowRemoved.emit($event);
  }

  onFirstDataRendered($event) {
    const editingCells = this.api.getEditingCells().length;
    if (
      this.rows.length > 1 ||
      this.api?.getDisplayedRowAtIndex(0)?.rowHeight === this.ROW_HEIGHT
    ) {
      this.api.resetRowHeights();
    }
    this.sizeGridColumnsToFit();
    if (!($event.lastRow && $event.firstRow) && editingCells) {
      this.editLatestRow();
    }
  }

  ngOnInit() {
    this.datagridMsgService.message
      .pipe(takeUntil(this.destroy$))
      .subscribe((message) => {
        if (this.gridId === message.gridId) {
          if (this.showActions?.hasCellAction && !this.hasActionHandler) {
            if (message.action !== DatagridActionsCta.DELETE) {
              this.cellAction.emit(message);
            }

            switch (message.action) {
              case DatagridActionsCta.EDIT:
                this.rowDataState.currentEditingRowData = JSON.parse(
                  JSON.stringify(this.rows[message.arrayIndex])
                );
                this.onEditAction(message);
                break;
              case DatagridActionsCta.SAVE:
                this.onSaveAction(message);
                break;
              case DatagridActionsCta.DELETE:
                this.onDeleteAction(message);
                break;
              case DatagridActionsCta.CANCEL:
                this.onCancelAction();
                break;
            }
          }
        }
      });

    this.defaultColDef = this.getDefaultColDef();

    if (this.showAddSingleRow) {
      this.showAddSingleRow = this.editingStateService.getEditingState();
    } else {
      this.showAddSingleRow = undefined;
    }
  }

  private setActionColumn() {
    if (this.showActions) {
      this.setActionVisibility();

      if (this.showActions.canAdd) {
        this.showAddSingleRow = this.editingStateService.getEditingState();
      }
      if (
        this.showActions.hasCellAction &&
        !this.columns.some((c) => c.field === 'action')
      ) {
        this.columns?.push({
          headerName: 'Actions',
          field: 'action',
          hide:
            !this.editingStateService.getEditingState() &&
            !this.showActions.canView,
          editable: false,
          suppressMovable: true,
          filter: false,
          sortable: false,
          suppressMenu: true,
          minWidth: 150,
          maxWidth: 150,
          cellClass: 'not-editable',
          cellStyle: {
            display: 'flex',
            'align-items': 'center',
            'white-space': 'normal',
          },
          cellRenderer: ActionCellRendererComponent,
        });
      }
    }

    this.pushActionData();
  }

  private setNotEditableClass() {
    this.columns?.forEach((c) => {
      if (!c.editable) {
        if (c.cellClass && c.cellClass.length) {
          c.cellClass = c.cellClass + ' not-editable';
        } else {
          c.cellClass = 'not-editable';
        }
      }
    });
  }

  private pushActionData() {
    this.rowId = 0;
    this.rows?.forEach((r) => {
      if (this.showActions) {
        r.action = { ...this.showActions, ...r.action };
      }
      r.disabled ??= false;
      r.rowId = this.rowId++;
    });
    this.api?.setColumnDefs(this.columns);
    this.setNotEditableClass();
  }

  private getDefaultColDef(): any {
    return {
      sortable:
        !this.rowDataState?.isAdding &&
        !this.rowDataState?.isEditing &&
        this.columnsSorting,
      autoHeaderHeight: true,
      filter: true,
      autoHeight: true,
      wrapText: true,
      resizable: false,
      suppressMenu:
        this.rowDataState?.isAdding ||
        this.rowDataState?.isEditing ||
        this.suppressColumnsMenu,
      suppressKeyboardEvent: this.onSuppressKeyboardEvent,
      valueGetter: (params) =>
        typeof params.data[params.colDef.field] === 'string'
          ? params.data[params.colDef.field].trim()
          : params.data[params.colDef.field],
    };
  }

  onAddAction(itemId: string) {
    const row = this.newRowData();
    row.action ??= this.showActions;

    if (!this.rowDataState.isAdding && !this.rowDataState.isEditing) {
      this.rowDataState.isAdding = true;
      this.addSingleRowClicked = true;

      this.rows.push({
        ...row,
        isEditing: true,
        rowId: this.rowId++,
      });
      this.editRow({
        rowIndex: this.rows.length - 1,
        visibleRowIndex: this.rows.length - 1,
      });
    }

    if (this.showActions?.canEdit) {
      this.columnApi.resetColumnState();
      this.addSingleRowClicked = true;

      if (this.showPagination) {
        this.api.paginationGoToLastPage();
      }
      this.rows.push({
        ...row,
        isEditing: true,
        rowId: this.rowId++,
      });
      this.api.setRowData(this.rows);
      this.api.startEditingCell({
        rowIndex: this.rows.length - 1,
        colKey: this.columnApi
          .getColumns()
          .find((c) => c.getUserProvidedColDef().editable)
          .getColId(),
      });
    }

    this.addCtaEvent.emit({ action: itemId });
  }

  onEditAction(message: DataGridMessage) {
    this.rowDataState.isEditing = true;
    this.editRow(message);
  }

  onSaveAction(message: DataGridMessage) {
    if (this.validator && !this.validator(message.rowIndex)) {
      return;
    }
    this.viewMode();
  }

  onDeleteAction(message: DataGridMessage) {
    if (!this.customDeleteWarning) {
      this.dialogMessageService.displayWarning(
        !this.isPermanentlyDeleting
          ? `Are you sure you wish to delete this row?`
          : 'By deleting this row, this record will be permanently deleted, do you want to continue?',
        true,
        () =>
          this.deleteRowData
            ? this.deleteRowData(message)
            : this.deleteRow(message.rowIndex, message),
        () => null,
        'Delete'
      );
    }
  }

  private deleteRow(rowIndex: number, message?: DataGridMessage) {
    this.rows.splice(rowIndex, 1);

    this.rowDataState.isAdding = false;
    this.rowDataState.currentEditingRowIndex = undefined;
    this.api.stopEditing();
    this.rows.forEach((row) => {
      row.isDisabled = false;
    });
    this.api.setDefaultColDef(this.getDefaultColDef());
    this.api.setRowData(this.rows);
    if (message) {
      this.cellAction.emit(message);
    }
    this.rowId--;
  }

  private editRow(message: any) {
    this.rowDataState.currentEditingRowIndex = message.rowIndex;
    this.rowDataState.currentEditingArrayIndex = message.arrayIndex;

    if (this.rowDataState.isEditing) {
      this.rows.forEach((row) => {
        if (row.rowId !== message.rowIndex) {
          row.isDisabled = true;
        }
      });
      this.rows.find((r) => r.rowId === message.rowIndex).isEditing = true;
    }
    if (this.rowDataState.isAdding) {
      this.rows.forEach((row, index) => {
        if (index !== this.rows.length - 1) {
          row.isDisabled = true;
        }
      });
      this.rows[this.rows.length - 1].isEditing = true;
      this.searchValue = '';
      this.api.setRowData(this.rows);
    }
    this.api.setDefaultColDef(this.getDefaultColDef());
    this.columns.find((c, i) => {
      if (c.editable) {
        this.editingFieldIndex = i;
      }
    });
    this.api.clearFocusedCell();
    this.api.startEditingCell({
      rowIndex: message.visibleRowIndex,
      colKey: this.columns[this.editingFieldIndex].field,
    });
  }

  onCancelAction() {
    if (this.rowDataState.isAdding) {
      this.deleteRow(this.rows.length - 1);
    }
    if (this.rowDataState.isEditing) {
      this.cancelRow(this.rowDataState.currentEditingArrayIndex);
    }
  }

  private cancelRow(rowIndex: number) {
    this.rows[rowIndex] = this.rowDataState.currentEditingRowData;
    this.viewMode();
  }

  private viewMode() {
    if (this.rowDataState.isAdding) {
      this.rows[this.rowDataState.currentEditingRowIndex].isEditing = false;
    }
    if (this.rowDataState.isEditing) {
      this.rows.find(
        (r) => r.rowId === this.rowDataState.currentEditingRowIndex
      ).isEditing = false;
    }

    this.rowDataState.currentEditingRowIndex = undefined;
    this.rowDataState.isAdding = false;
    this.rowDataState.isEditing = false;
    this.api.stopEditing();
    this.rows.forEach((row) => {
      row.isDisabled = false;
    });
    this.api.setDefaultColDef(this.getDefaultColDef());
    this.api.setRowData(this.rows);
  }

  ngOnChanges(changes: SimpleChanges): void {}

  setupExcelConfig() {
    if (this.columns) {
      this.excelMessageHeader = [
        [
          {
            styleId: 'message',
            data: {
              type: 'String',
              value: this.excelMessage,
            },
            mergeAcross: this.columns.length - 1,
          },
        ],
        [],
      ];
      this.excelMessageFooter = [
        [],
        [
          {
            styleId: 'message',
            data: {
              type: 'String',
              value: this.excelMessage,
            },
            mergeAcross: this.columns.length - 1,
          },
        ],
      ];
      this.excelStyles = [
        {
          id: 'message',
          alignment: { horizontal: 'CenterAcrossSelection' },
          interior: {
            color: '#008000',
            pattern: 'Solid',
          },
          dataType: 'string',
        },
      ];
    }
  }

  handlePageSizeChanged(pageSize: any) {
    this.api.paginationSetPageSize(this.pageSize);
    this.filterGrid.emit(pageSize);
  }

  // Exports to excel
  onExport() {
    if (!this.api?.getEditingCells().length) {
      const params = this.getExportParams();
      this.api.exportDataAsExcel(params);
    }
  }

  // Exports to excel
  onExportToCSV() {
    if (!this.api?.getEditingCells().length) {
      const params = this.getExportParams('.csv');
      this.api.exportDataAsCsv(params);
    }
  }

  // click eye cta
  onEyeCtaClicked(): void {
    this.eyeClicked.emit();
  }

  // Gets parameters for the export
  getExportParams(extension = '.xlsx'): any {
    return {
      customHeader: this.excelMessageHeader,
      customFooter: this.excelMessageFooter,
      fileName: `${this.name}${extension}`,
      columnGroups: true,
      columnKeys: this.getColumnKeys(),
      getCustomContentBelowRow: this.customContentBelow,
    };
  }

  private customContentBelow(params: ProcessRowGroupForExportParams) {
    if (params.context.getContentBelowRow && !params.node.isRowPinned()) {
      return params.context.getContentBelowRow(params.node.rowIndex);
    }
  }

  private getColumnKeys(): string[] {
    const fields = (this.columns as AbstractColDef[])
      .flatMap((x) => {
        if ((x as ColGroupDef).children) {
          return (x as ColGroupDef).children;
        } else {
          return [x];
        }
      })
      .flatMap((x) => {
        if ((x as ColGroupDef).children) {
          return (x as ColGroupDef).children;
        } else {
          return [x];
        }
      })
      .flatMap((x) => {
        if ((x as ColGroupDef).children) {
          return (x as ColGroupDef).children;
        } else {
          return [x];
        }
      })
      .filter((x: ColDef) => !x.cellRenderer)
      .map((x: ColDef) => x.colId ?? x.field);
    return fields;
  }

  // provides context menu options
  getContextMenuItems(params) {
    const result = [
      {
        name: 'Export',
        action: () => {
          params.context.onExportToCSV();
        },
      },
    ];
    return result;
  }

  onColumnResized(params) {
    this.columnResized.emit(params);
    if (params.source === 'columnResized' && params.finished) {
      this.sizeGridColumnsToFit();
    }
    if (this.calculateContainerWidth) {
      this.calculateDataGridWidth();
    }
  }

  onResize() {
    this.sizeGridColumnsToFit();
  }

  onFilterOpened(event) {}

  updateAddCTAOptions() {}

  private calculateDataGridWidth() {
    setTimeout(() => {
      let total = 0;
      this.columnApi.getColumns().forEach((c) => {
        total += c.getActualWidth();
      });
      this.style = `width:${total}px`;
    }, 100);
  }

  onRowDataChanged(event: any) {
    this.pushActionData();
    this.sizeGridColumnsToFit(event);
    this.rowDataChanged.emit(event);
  }

  enableGridNavigation(params?: any) {
    // if (!params) {
    //   return;
    // }
    // const eventSelector = params.api.gridCore.eGui as HTMLDivElement;
    // const headers = eventSelector.querySelectorAll('[ref="eLabel"][role="presentation"]');
    // const cells = eventSelector.querySelectorAll(
    //   '.ag-cell-not-inline-editing[role="gridcell"]:not([ref="eCellValue"])'
    // );
    // const inputs = eventSelector.querySelectorAll('[ref="eInput"][type="text"]');
    // const saveButton = eventSelector.querySelectorAll('fa-icon[title="Save"]');
    // let cell;
    // for (let i = cells.length - 1; i >= 0; i--) {
    //   if (cells.item(i).getAttribute('aria-colindex') === '1') {
    //     cell = cells.item(i);
    //     break;
    //   }
    // }
    // headers.forEach(c => {
    //   c.setAttribute('tabindex', '0');
    //   c.setAttribute('aria-label', 'header');
    // });
    // cells.forEach(c => {
    //   c.setAttribute('tabindex', '0');
    //   c.setAttribute('aria-label', 'cell');
    // });
    // if (cell !== undefined && this.addSingleRowClicked) {
    //   cell.focus();
    //   this.addSingleRowClicked = false;
    // }
    // if (inputs.length > 0) {
    //   inputs.forEach(input => {
    //     input.setAttribute('tabindex', '0');
    //     input.setAttribute('aria-label', 'input');
    //   });
    // }
    // if (saveButton.length) {
    //   let saveRowIndex;
    //   let saveColId;
    //   let saveRowNode = saveButton.item(0).parentNode;
    //   do {
    //     saveRowIndex = saveRowNode.parentElement.getAttribute('row-index');
    //     saveRowNode = saveRowNode.parentNode;
    //     if (saveRowNode.parentElement.getAttribute('col-id')) {
    //       saveColId = saveRowNode.parentElement.getAttribute('col-id');
    //     }
    //   } while (!saveRowIndex);
    //   this.currentRowIndex = saveRowIndex;
    //   this.currentColId = saveColId;
    // } else {
    //   if (!this.dynamicGrid) {
    //     this.api?.setFocusedCell(Number(this.currentRowIndex), this.currentColId);
    //   } else {
    //     const row = eventSelector
    //       .querySelectorAll(
    //         '[row-index="' + this.dynamicGrid.rowIndex + '"] > [col-id="' + this.dynamicGrid.colKey + '"]'
    //       )
    //       .item(0) as any;
    //     row.focus();
    //   }
    // }
  }

  onColumnVisibilityChanged(event) {
    this.sizeGridColumnsToFit();
    // Keep showing fixed columns.
    if (this.removableColumns.length) {
      const columns: Column[] = event.columns;
      columns.forEach((column) => {
        const field = column.getColDef().field;
        const foundColumn = this.removableColumns.find((x) => x === field);
        if (!foundColumn) {
          this.columnApi.setColumnVisible(column.getColId(), true);
        }
      });
    }
  }

  onVirtualColumnsChanged(event) {
    this.virtualColumnsChanged.emit(event);
  }

  onToolPanelVisiblilityChanged(event) {
    this.sizeGridColumnsToFit();
  }

  private sizeGridColumnsToFit(event?) {
    // const clientWidth = (this.api as any)?.gridCore.eGridDiv.clientWidth;
    // if (clientWidth) {
    if (this.api && !this.api.getEditingCells().length) {
      this.api.sizeColumnsToFit();
      this.enableGridNavigation(event);
    }
    // }
  }

  private editLatestRow() {
    const colId = this.columnApi
      .getAllGridColumns()
      .find((c) => c.isCellEditable)
      .getId();
    this.api.startEditingCell({
      rowIndex: this.api.getDisplayedRowCount() - 1,
      colKey: colId,
    });
  }

  private setActionVisibility() {
    if (this.showActions.canDelete) {
      this.showActions.canDelete = this.accountService.hasAnyRole(
        RoleActions.DELETE,
        location.pathname
      );
    }

    if (this.showActions.canView) {
      if (this.viewPath) {
        this.showActions.canView = this.accountService.hasAnyRole(
          RoleActions.VIEW,
          this.viewPath
        );
      } else {
        this.showActions.canView = this.accountService.hasAnyRole(
          RoleActions.VIEW,
          location.pathname
        );
      }
    }
  }

  onClearFilter() {
    this.searchValue = '';
    this.clearFilter.emit();
  }

  onCellValueChanged(event) {
    this.cellValueChanged.emit(event);
  }

  onSearchValueChange(newValue) {
    this.searchValueChange.emit(newValue);
    this.api.setQuickFilter(newValue);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

interface IParams {
  value: any;
  node: RowNode;
  column: Column;
  api: GridApi;
  columnApi: ColumnApi;
  context: Context;
}

interface IRowDataState {
  isAdding?: boolean;
  isEditing?: boolean;
  currentEditingRowIndex?: number;
  currentEditingArrayIndex?: number;
  currentEditingRowData?: any;
}
