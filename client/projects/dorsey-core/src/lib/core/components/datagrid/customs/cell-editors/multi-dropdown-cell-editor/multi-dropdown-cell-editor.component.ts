import { Component, OnInit } from '@angular/core';
import { ICellEditorAngularComp } from 'ag-grid-angular';

@Component({
  selector: 'dorsey-multi-dropdown-cell-editor',
  templateUrl: './multi-dropdown-cell-editor.component.html',
  styleUrls: ['./multi-dropdown-cell-editor.component.scss'],
})
export class MultiDropdownCellEditorComponent
  implements ICellEditorAngularComp
{
  selectedValues: any[] = [];
  options: any[] = [];
  value: string;

  constructor() {}

  agInit(params: any): void {
    this.value = params.value;
    this.selectedValues = this.inputConversion();
    this.options = params.options;
  }

  getValue() {
    return this.outputConversion();
  }

  inputConversion(): any[] {
    const role = this.value
      .replace(/\n/g, '')
      .split('-')
      .filter((r) => r !== '');
    return role.map((r) => {
      return { name: r, code: r };
    });
  }

  outputConversion() {
    return this.selectedValues.map((r) => {
      return {
        name: r.name,
      };
    });
  }
}
