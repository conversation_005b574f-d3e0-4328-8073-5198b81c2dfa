import { Component, OnInit } from '@angular/core';
import { DataGridMessage } from '../../../models/datagrid-message.model';
import { DatagridMsgService } from '../../../services/datagrid-msg.service';
import { DatagridActionsCta } from '../../../models/enums/datagrid-actions-cta';

@Component({
  selector: 'dorsey-action-cell-renderer',
  templateUrl: './action-cell-renderer.component.html',
  styleUrls: ['./action-cell-renderer.component.scss'],
})
export class ActionCellRendererComponent {
  params: any;
  data: any;
  isEditing = false;
  readonly BASIC_ACTIONS_CTA = DatagridActionsCta;

  constructor(protected datagridMsgService: DatagridMsgService) {}

  onSelected(action: DatagridActionsCta) {
    if (!this.params.data.isDisabled) {
      const message: DataGridMessage = new DataGridMessage();
      message.rowIndex = this.params.rowIndex;
      message.visibleRowIndex = this.params.node.rowIndex;
      message.columnIndex = -1;
      message.action = action;
      message.rendererName = 'ActionCellRendererComponent';
      message.rowData = this.data;
      message.rawData = this.params.data;
      message.messageType = 'cell-renderer';
      message.gridId = this.params.api.context.contextParams.gridId;
      this.datagridMsgService.sendMessage(message);
    }
  }

  agInit(params) {
    this.params = params;
    this.data = params.data.action;
    this.isEditing = params.data.isEditing;
  }
}
