import { Component } from '@angular/core';
import { ICellEditorAngularComp } from 'ag-grid-angular';
import { formatDate } from '@angular/common';

@Component({
  selector: 'dorsey-calendar-cell-renderer',
  templateUrl: './calendar-cell-renderer.component.html',
  styleUrls: ['./calendar-cell-renderer.component.scss'],
})
export class CalendarCellEditorComponent implements ICellEditorAngularComp {
  value: any;

  constructor() {}

  ngOnInit(): void {}

  agInit(params: any): void {
    this.value = params.value
      ? formatDate(params.value, 'MM/dd/yyyy', 'en-US')
      : null;
  }

  refresh(params: any): boolean {
    this.value = params.value
      ? formatDate(params.value, 'MM/dd/yyyy', 'en-US')
      : null;
    return true;
  }

  getValue(): any {
    return this.value
      ? formatDate(this.value, 'yyyy-MM-ddTHH:mm:ss', 'en-US')
      : null;
  }
}
