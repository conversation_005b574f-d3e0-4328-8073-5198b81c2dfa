import { Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { catchError, fork<PERSON>oin, of, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
  b64toBlob,
  isExcel as isExcelFunc,
} from '../../../../utils/file-utils';
import { IUploadedData } from '../../../../models/admin/system/uploaded-data.model';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { UploadedDataService } from '../../../../services/admin/uploaded-data.service';
import { ToastService } from '../../../../services/toast.service';
import { OrganizationService } from '../../../../services/admin/organization.service';
import { ItemService } from '../../../../services/admin/item.service';
import { DorseyConfiguration } from '../../../../models/configuration';
import { DataSet } from '../../../../models/enums/data-set';
import { RoleService } from '../../../../services/admin/role.service';
import { SystemService } from '../../../../services/admin/system.service';
import { saveAs } from 'file-saver';

@Component({
  selector: 'dorsey-upload-detail',
  templateUrl: './upload-detail.component.html',
  styleUrls: ['./upload-detail.component.scss'],
})
export class UploadDetailComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  readonly CORE_CODES = ['OH', 'R', 'U'];

  isExcel = isExcelFunc;

  showUploadDialog = false;
  uploadedData: IUploadedData;
  uploadDisabled = true;
  uploadTooltip: string;

  readonly fileTemplateNames = {
    SMC: ['job_state_machine_template'],
    OH: ['organization_hierarchy_template'],
    W: ['workflow_template'],
    C: ['job_list_template'],
    R: ['role_list_template'],
    U: ['user_list_template'],
  };

  constructor(
    public activatedRoute: ActivatedRoute,
    private dialogMessageService: DialogMessageService,
    public uploadedDataService: UploadedDataService,
    private toastService: ToastService,
    private organizationService: OrganizationService,
    private itemService: ItemService,
    private roleService: RoleService,
    private systemService: SystemService,
    @Inject('config') private config: DorseyConfiguration
  ) {}

  ngOnInit(): void {
    this.uploadedData = this.activatedRoute.snapshot
      .queryParams as IUploadedData;

    this.uploadStatus();
  }

  private uploadStatus() {
    switch (this.uploadedData.dataSetCode) {
      case this.config.business.dataSetCodes.item:
        this.setItemUploadStatus();
        break;
      case DataSet.U:
        this.setUserUploadStatus();
        break;
      default:
        this.uploadDisabled = false;
        break;
    }
  }

  private setUserUploadStatus() {
    this.roleService.checkRolesExistence().subscribe(
      (resp: any) => {
        this.uploadDisabled = !resp;
        if (!resp) {
          this.uploadTooltip = 'No role data was found.';
        }
      },
      () =>
        this.toastService.displayError(
          'Error occurred while checking role data.'
        )
    );
  }

  private setItemUploadStatus() {
    forkJoin({
      nodeExist: this.organizationService
        .checkNodesExistence()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while checking organization nodes.'
              )
            )
          )
        ),
      transitionExist: this.itemService
        .checkTransitionExistence()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                `Error occurred while checking ${this.config.business.itemName} transitions.`
              )
            )
          )
        ),
    }).subscribe((resp: any) => {
      this.uploadDisabled = !resp.nodeExist || !resp.transitionExist;
      if (this.uploadDisabled) {
        this.uploadTooltip = !resp.nodeExist
          ? 'No Organization Hierarchy data was found.'
          : !resp.transitionExist
          ? `No state machine ${this.config.business.itemName}s data was found.`
          : null;
      }
    });
  }

  onDownload(): void {
    this.systemService
      .findSystemFile(this.uploadedData.dataSetCode)
      .subscribe((resp) => {
        const blob = b64toBlob(resp, 'application/ms-excel');
        const file = new File(
          [blob],
          `${this.fileTemplateNames[this.uploadedData.dataSetCode]}.xlsx`,
          {
            type: 'application/ms-excel',
          }
        );
        saveAs(file);
      });
  }

  onUpload(): void {
    this.showUploadDialog = false;
    this.dialogMessageService.displayWarning(
      'You are about to overwrite any existing data with the data in this file. Do you wish to continue?',
      true,
      () => (this.showUploadDialog = true),
      () => {},
      'Continue'
    );
  }

  uploadFile(file: File) {
    this.showUploadDialog = false;
    this.uploadedDataService
      .saveFile(
        file,
        {
          ...this.uploadedData,
          status: 'Uploaded',
        },
        this.CORE_CODES.some((c) => c === this.uploadedData.dataSetCode)
          ? 'coreUploadedData'
          : 'uploadedData'
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        () => {
          this.toastService.displaySuccess('File successfully loaded');
          history.back();
        },
        (error) => {
          if (error.status === 413) {
            this.dialogMessageService.displayError(
              'The file size cannot exceed 10mb.'
            );
          } else {
            this.dialogMessageService.displayError(
              error.error.split(';').join('\n'),
              false
            );
          }
        }
      );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
