import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { GridApi } from 'ag-grid-community';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { IUploadedData } from '../../../../../models/admin/system/uploaded-data.model';
import { UploadedDataService } from '../../../../../services/admin/uploaded-data.service';
import { DialogMessageService } from '../../../../../services/dialog-message.service';
import { ToastService } from '../../../../../services/toast.service';
import { blobToBinary } from '../../../../../utils/file-utils';

type AOA = any[][];

@Component({
  selector: 'cms-file-detail',
  templateUrl: './file-detail.component.html',
  styleUrls: ['./file-detail.component.scss'],
})
export class FileDetailComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  gridApi: GridApi;

  file: Blob = null;
  fileArray = null;

  uploadedData: IUploadedData;

  columnDefs = [];
  data;
  workbook: XLSX.WorkBook;

  constructor(
    public activatedRoute: ActivatedRoute,
    private dialogMessageService: DialogMessageService,
    public uploadedDataService: UploadedDataService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.uploadedData = history.state as IUploadedData;
    this.loadFile();
  }

  loadFile() {
    this.uploadedDataService
      .downloadFile(this.uploadedData.dataSetCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe((res) => {
        this.file = res;
        blobToBinary(res).then((result) => {
          this.fileArray = result;
          this.workbook = XLSX.read(this.fileArray, {
            type: 'binary',
            cellText: false,
            cellDates: true,
          });
          // this.loadGrid();
        });
      });
  }

  onDownload() {
    saveAs(this.file, this.uploadedData.fileName);
  }

  onGridIsReady(gridApi: GridApi) {
    this.gridApi = gridApi;
  }

  loadGridData(sheet: XLSX.WorkSheet) {
    const data = <AOA>XLSX.utils.sheet_to_json(sheet, {
      header: 'A',
      raw: false,
      dateNF: 'MM/dd/yyyy',
    });
    data.shift();
    return data;
  }

  loadColumnDef(sheet: XLSX.WorkSheet) {
    let columnDefs = [];

    const data = <AOA>XLSX.utils.sheet_to_json(sheet, {
      header: 'A',
      raw: false,
      dateNF: 'MM/dd/yyyy',
    });

    const header = data.shift();
    Object.keys(header).forEach((key) => {
      columnDefs.push({
        field: key,
        headerName: header[key],
      });
    });

    return columnDefs;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
