<div class='d-flex flex-column p-3 h-100'>
  <div class='text-end mb-2'>
    <div class='action-button-container' *ngIf="uploadedData?.status !== 'Locked'">
      <p-button label='Get Template' icon='pi pi-download' iconPos='left'
                [ngbTooltip]="uploadTooltip"
                [disabled]="uploadDisabled"
                [hidden]="false"
                (onClick)='onDownload()'></p-button>
      <p-button label='Upload File' icon='pi pi-upload' iconPos='left'
                [ngbTooltip]="uploadTooltip"
                [disabled]="uploadDisabled"
                [hidden]="false"
                (onClick)='onUpload()'></p-button>
    </div>
  </div>
  <p-panel header='Details' class='detail-panel'>
    <div class='card-body'>
      <div class='row'>
        <div class='col-2 text-end'>Data Source</div>
        <div class='col-6'>
            <a *ngIf='isExcel(uploadedData?.fileName)' [routerLink]="'file-detail'"
               [state]='uploadedData'>{{ uploadedData?.fileName }}</a>
            <span *ngIf='!isExcel(uploadedData?.fileName)'>{{ uploadedData?.fileName }}</span>
        </div>
      </div>
      <div class='row'>
        <div class='col-2 text-end'>Data Set</div>
        <div class='col-6'>{{ uploadedData?.dataSet }}</div>
      </div>
      <div class='row'>
        <div class='col-2 text-end'>Status</div>
        <div class='col-6'>{{ uploadedData?.status }}</div>
      </div>
      <div class='row'>
        <div class='col-2 text-end'>Status Change Date</div>
        <div class='col-6'>{{ uploadedData?.uploadedDate | date:'MM/dd/yyyy H:mm' }}</div>
      </div>
    </div>
  </p-panel>
</div>

<dorsey-upload *ngIf='showUploadDialog' (upload)='uploadFile($event)' [display]='showUploadDialog'
            [accept]="['.xlsx']"></dorsey-upload>
