import { Component } from '@angular/core';
import { Subject } from 'rxjs';
import { GridApi } from 'ag-grid-community';
import { formatDate } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { UploadSummaryEnum } from './upload-summary.enum';
import {
  StatusCellRendererBackgroundColor,
  StatusCellRendererComponent,
} from './status-cell-renderer/status-cell-renderer.component';
import { HyperlinkCellRendererComponent } from '../../datagrid/customs/cell-renderers/hyperlink-cell-renderer/hyperlink-cell-renderer.component';
import { IUploadedData } from '../../../models/admin/system/uploaded-data.model';
import { UploadedDataService } from '../../../services/admin/uploaded-data.service';

@Component({
  selector: 'dorsey-load-data',
  templateUrl: './load-data.component.html',
  styleUrls: ['./load-data.component.scss'],
})
export class LoadDataComponent {
  private readonly destroy$ = new Subject<void>();

  gridApi: GridApi;

  columnDefs = [
    {
      field: 'dataSet',
      headerName: 'Data Set Name',
      cellRenderer: HyperlinkCellRendererComponent,
      cellRendererParams: (params) => {
        const rowNode = params.api.getDisplayedRowAtIndex(params.rowIndex);

        return { route: './upload-detail', queryParams: rowNode.data };
      },
    },
    {
      field: 'status',
      cellClass: 'text-center',
      cellRenderer: StatusCellRendererComponent,
      cellRendererParams: {
        colorSelectionFunction: this.statusCellRendererColorSelection,
      },
    },
    {
      field: 'uploadedDate',
      headerName: 'Status Change Dt',
      cellClass: 'text-center',
      valueFormatter: (params) => {
        return params.value
          ? formatDate(params.value, 'MM/dd/yyyy H:mm', 'en-US')
          : null;
      },
    },
  ];

  rowData: IUploadedData[] = [];

  constructor(
    private uploadedDataService: UploadedDataService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  private loadData(): void {
    this.uploadedDataService
      .findAll()
      .pipe(takeUntil(this.destroy$))
      .subscribe((resp: IUploadedData[]) => {
        this.rowData = resp.sort((a, b) => a.displayOrder - b.displayOrder);
      });
  }

  statusCellRendererColorSelection(value: UploadSummaryEnum) {
    switch (value) {
      case UploadSummaryEnum.LOCKED:
        return StatusCellRendererBackgroundColor.GREEN;
      case UploadSummaryEnum.UPLOADED:
        return StatusCellRendererBackgroundColor.YELLOW;
      case UploadSummaryEnum.NO_DATA:
      default:
        return StatusCellRendererBackgroundColor.RED;
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
