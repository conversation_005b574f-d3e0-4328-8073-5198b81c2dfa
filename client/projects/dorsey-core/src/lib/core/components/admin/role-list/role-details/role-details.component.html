<div class="d-flex flex-column p-3 h-100">
    <div class="text-end mb-2">
        <div class="action-button-container">
            <dorsey-edition-ctas *hasAnyRole="actions.EDIT;path:path" class="col-3 p-0 text-end" [canEdit]="selectedRole.name !== 'Admin'"
                              [editionDisabled]="!rowData">
            </dorsey-edition-ctas>
        </div>
    </div>

    <p-panel>
        <ng-template pTemplate="header">
            <div class="row w-100 m-0">
                <div class="col-8">
                    <div class="row">
                        <div class="col-4">
                            <div class="row justify-content-center">
                                <div class="d-flex w-auto">
                                    <label class="align-self-center">Role name:</label>
                                </div>
                                <div class="d-flex w-auto">
                                    <span>{{selectedRole.name}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="row justify-content-center">
                                <div class="d-flex w-auto">
                                    <label for="role-dropdown" class="align-self-center">Select superior:</label>
                                </div>
                                <div class="d-flex w-auto">
                                    <p-dropdown  *ngIf="!isAdmin" id="role-dropdown" [options]="upperRoles"
                                                [(ngModel)]="selectedUpperRole" placeholder="Select Item"
                                                optionLabel="name"
                                                [disabled]="!editingStateService.editingState.isEditing"></p-dropdown>
                                    <span *ngIf="isAdmin">None</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="row justify-content-center">
                                <div class="d-flex w-auto">
                                    <label for="role-dropdown" class="align-self-center">Department:</label>
                                </div>
                                <div class="d-flex w-auto">
                                    <p-autoComplete  *ngIf="!isAdmin" [(ngModel)]="selectedDepartment"
                                                    [suggestions]="suggestedDepartments"
                                                    (completeMethod)="filterDepartment($event)" field="name"
                                                    [disabled]="!editingStateService.editingState.isEditing"></p-autoComplete>
                                    <span *ngIf="isAdmin">{{selectedDepartment.name}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-4">
                    <div class="row">
                        <div class="col-1"></div>
                        <div class="col-11">
                            <div class="row">
                                <div class="d-flex w-auto">
                                    <label for="description">Description:</label>
                                </div>
                                <div class="d-flex w-auto flex-auto">
                                    <textarea dorseyTrim [maxLength]="2047" id="description" pInputTextarea [(ngModel)]="roleDescription" [rows]="3"
                                              class="w-100" placeholder="Role description..."
                                              [disabled]="!editingStateService.editingState.isEditing"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </ng-template>

        <div class="row">
            <p-tabView styleClass="tabview-custom">
                <p-tabPanel>
                    <ng-template pTemplate="header">
                        <i class="pi pi-eye me-1"></i>
                        <span>Visibility</span>
                    </ng-template>
                    <div *ngIf="selectedRole">
                        <datagrid
                                id="role-detail-list"
                                [name]="title"
                                [columns]="columnDefs"
                                [rows]="rowData"
                                [hasFilter]="false"
                                [suppressColumnsMenu]="true"
                                [columnsSorting]="false"
                                [treeData]="true"
                                [groupDefaultExpanded]="-1"
                                [autoGroupColumnDef]="autoGroupColumnDef"
                                [getDataPath]="getDataPath"
                                (gridIsReady)="onGridIsReady($event)"
                        >
                        </datagrid>
                    </div>
                </p-tabPanel>
                <p-tabPanel>
                    <ng-template pTemplate="header">
                        <i class="pi pi-sync me-1"></i>
                        <span>Workflow</span>
                    </ng-template>
                        <div>
                            <datagrid
                                    id="hierarchy-levels"
                                    [name]="'workflow attendant'"
                                    [columns]="workflowColumnDefs"
                                    [rows]="workflowRowData"
                                    [hasFilter]="false"
                                    [columnsSorting]="false"
                                    [suppressColumnsMenu]="true"
                                    (gridIsReady)="onWorkflowGridIsReady($event)"
                            >
                            </datagrid>
                        </div>
                </p-tabPanel>
            </p-tabView>
        </div>
    </p-panel>
</div>
