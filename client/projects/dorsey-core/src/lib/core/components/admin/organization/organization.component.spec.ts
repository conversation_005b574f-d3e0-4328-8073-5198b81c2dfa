import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError, Subject } from 'rxjs';
import { <PERSON>rid<PERSON><PERSON> } from 'ag-grid-community';

import { OrganizationComponent } from './organization.component';
import { EditingStateService } from '../../../services/editing-state.service';
import { OrganizationService } from '../../../services/admin/organization.service';
import { ToastService } from '../../../services/toast.service';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { IHierarchyLevel } from '../../../models/admin/organization/hierarchy-level';
import { IHierarchyNode } from '../../../models/admin/organization/hierarchy-node';
import { RoleActions } from '../../../models/enums/role-actions';
import { FormAction } from '../../../models/form-action';
import { DataGridMessage } from '../../datagrid/models/datagrid-message.model';
import { DatagridActionsCta } from '../../datagrid/models/enums/datagrid-actions-cta';
import { ActionState } from '../../datagrid/models/action-state';
import { GridRow } from '../../datagrid/models/grid-row.model';
import * as gridUtils from '../../../utils/grid-utils';

describe('OrganizationComponent', () => {
  let component: OrganizationComponent;
  let fixture: ComponentFixture<OrganizationComponent>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockOrganizationService: jest.Mocked<OrganizationService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockRouter: jest.Mocked<Router>;

  const mockHierarchyLevels: GridRow<IHierarchyLevel>[] = [
    {
      level: 1,
      hierarchyName: 'Company',
      description: 'Company level',
      inUse: true,
      isDisabled: false,
      action: ActionState.ADD_EDIT,
      isEditing: false,
      rowId: 0
    },
    {
      level: 2,
      hierarchyName: 'Department',
      description: 'Department level',
      inUse: true,
      isDisabled: false,
      action: ActionState.ADD_EDIT,
      isEditing: false,
      rowId: 1
    },
    {
      level: 3,
      hierarchyName: 'Team',
      description: 'Team level',
      inUse: false,
      isDisabled: false,
      action: ActionState.ADD_EDIT_DELETE,
      isEditing: false,
      rowId: 2
    }
  ];

  const mockHierarchyNodes: GridRow<IHierarchyNode>[] = [
    {
      nodeId: [1],
      parentId: [],
      hierarchyValue: 'Acme Corp',
      users: 5,
      items: 10,
      canDelete: false,
      isDisabled: false,
      action: ActionState.TREE_ADD_EDIT,
      isEditing: false,
      rowId: 0
    },
    {
      nodeId: [1, 1],
      parentId: [1],
      hierarchyValue: 'Engineering',
      users: 3,
      items: 5,
      canDelete: true,
      isDisabled: false,
      action: ActionState.TREE_ADD_EDIT_DELETE,
      isEditing: false,
      rowId: 1
    },
    {
      nodeId: [1, 2],
      parentId: [1],
      hierarchyValue: 'Sales',
      users: 2,
      items: 5,
      canDelete: true,
      isDisabled: false,
      action: ActionState.TREE_ADD_EDIT_DELETE,
      isEditing: false,
      rowId: 2
    }
  ];

  beforeEach(async () => {
    const mockEditingStateSubject = new Subject<FormAction>();

    // Mock the grid utils
    jest.spyOn(gridUtils, 'getGridData').mockImplementation((gridApi: any) => {
      const data: any[] = [];
      if (gridApi && gridApi.forEachNode) {
        gridApi.forEachNode((node: any) => data.push(node.data));
      }
      return data;
    });

    mockEditingStateService = {
      getValue: jest.fn().mockReturnValue(mockEditingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: {
        isEditing: false,
        hasChanges: false
      }
    } as any;

    mockOrganizationService = {
      findLevels: jest.fn().mockReturnValue(of(mockHierarchyLevels)),
      updateLevels: jest.fn().mockReturnValue(of({})),
      findOrgHierarchy: jest.fn().mockReturnValue(of(mockHierarchyNodes)),
      updateNodes: jest.fn().mockReturnValue(of({}))
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn()
    } as any;

    mockDialogMessageService = {
      displayError: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn().mockResolvedValue(true)
    } as any;

    await TestBed.configureTestingModule({
      declarations: [OrganizationComponent],
      providers: [
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: OrganizationService, useValue: mockOrganizationService },
        { provide: ToastService, useValue: mockToastService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: Router, useValue: mockRouter }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(OrganizationComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.actions).toBe(RoleActions);
      expect(component.selectedTab).toBe(0);
      expect(component.levelErrors).toEqual([]);
      expect(component.nodeErrors).toEqual([]);
      expect(component.defRowData).toEqual([]);
      expect(component.path).toBe(location.pathname);
    });

    it('should call initialization methods on ngOnInit', () => {
      jest.spyOn(component, 'setLevelColumnDefinition');
      jest.spyOn(component as any, 'loadLevels');
      jest.spyOn(component as any, 'setColumnDefinition');

      component.ngOnInit();

      expect(component.setLevelColumnDefinition).toHaveBeenCalled();
      expect(component['loadLevels']).toHaveBeenCalled();
      expect(component['setColumnDefinition']).toHaveBeenCalled();
    });
  });

  describe('Level Column Definition', () => {
    it('should set level column definitions correctly', () => {
      component.setLevelColumnDefinition();

      expect(component.levelColumnDefs).toBeDefined();
      expect(component.levelColumnDefs.length).toBe(4);

      expect(component.levelColumnDefs[0].field).toBe('level');
      expect(component.levelColumnDefs[1].field).toBe('inUse');
      expect(component.levelColumnDefs[2].field).toBe('hierarchyName');
      expect(component.levelColumnDefs[3].field).toBe('description');
    });

    it('should configure editable fields correctly', () => {
      component.setLevelColumnDefinition();

      const hierarchyNameColumn = component.levelColumnDefs[2];
      const descriptionColumn = component.levelColumnDefs[3];

      expect(hierarchyNameColumn.editable).toBe(true);
      expect(descriptionColumn.editable).toBe(true);
      expect(hierarchyNameColumn.cellEditorParams.maxLength).toBe(25);
      expect(descriptionColumn.cellEditorParams.maxLength).toBe(255);
    });

    it('should configure checkbox renderer for inUse field', () => {
      component.setLevelColumnDefinition();

      const inUseColumn = component.levelColumnDefs[1];

      expect(inUseColumn.cellRenderer).toBeDefined();
      expect(inUseColumn.cellRendererParams().disabled).toBe(true);
    });
  });

  describe('Node Column Definition', () => {
    beforeEach(() => {
      component.levelRowData = mockHierarchyLevels;
    });

    it('should set node column definitions correctly', () => {
      component['setColumnDefinition']();

      expect(component.columnDefs).toBeDefined();
      expect(component.columnDefs.length).toBe(3);

      expect(component.columnDefs[0].field).toBe('hierarchyValue');
      expect(component.columnDefs[1].field).toBe('users');
      expect(component.columnDefs[2].field).toBe('items');
    });

    it('should configure auto group column definition', () => {
      component['setColumnDefinition']();

      expect(component.autoGroupColumnDef).toBeDefined();
      expect(component.autoGroupColumnDef.field).toBe('nodeId');
      expect(component.autoGroupColumnDef.headerName).toBe('Hierarchy Level');
      expect(component.autoGroupColumnDef.editable).toBe(false);
    });

    it('should format hierarchy level names correctly', () => {
      component['setColumnDefinition']();

      const mockParams = {
        data: { nodeId: [1, 1] },
        value: [1, 1],
        node: {} as any,
        column: {} as any,
        colDef: {} as any,
        api: {} as any,
        columnApi: {} as any,
        context: {} as any
      };

      const valueFormatter = component.autoGroupColumnDef.valueFormatter;
      if (typeof valueFormatter === 'function') {
        const formattedValue = valueFormatter(mockParams);
        expect(formattedValue).toBe('Department'); // Level 2 = Department
      }
    });

    it('should handle getDataPath correctly', () => {
      const mockData = { nodeId: [1, 2, 3] };

      const result = component.getDataPath(mockData);

      expect(result).toEqual([1, 2, 3]);
    });
  });

  describe('Data Loading', () => {
    it('should load levels successfully', () => {
      jest.spyOn(component as any, 'loadNodes');

      component['loadLevels']();

      expect(mockOrganizationService.findLevels).toHaveBeenCalled();
      expect(component.levelRowData).toBeDefined();
      expect(component.levelRowData.length).toBe(3);
      expect(component['loadNodes']).toHaveBeenCalled();
    });

    it('should handle levels load error', () => {
      mockOrganizationService.findLevels.mockReturnValue(throwError('Load error'));

      component['loadLevels']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed loading Organization Levels.');
    });

    it('should sort levels by level number', () => {
      const unsortedLevels = [
        { ...mockHierarchyLevels[2], level: 3 },
        { ...mockHierarchyLevels[0], level: 1 },
        { ...mockHierarchyLevels[1], level: 2 }
      ];
      mockOrganizationService.findLevels.mockReturnValue(of(unsortedLevels));

      component['loadLevels']();

      expect(component.levelRowData[0].level).toBe(1);
      expect(component.levelRowData[1].level).toBe(2);
      expect(component.levelRowData[2].level).toBe(3);
    });

    it('should assign action states to levels based on inUse', () => {
      component['loadLevels']();

      const inUseLevel = component.levelRowData.find(l => l.inUse);
      const notInUseLevel = component.levelRowData.find(l => !l.inUse);

      expect(inUseLevel?.action).toBe(ActionState.ADD_EDIT);
      expect(notInUseLevel?.action).toBe(ActionState.ADD_EDIT_DELETE);
    });

    it('should load nodes successfully', () => {
      component.levelRowData = mockHierarchyLevels;

      component['loadNodes']();

      expect(mockOrganizationService.findOrgHierarchy).toHaveBeenCalled();
      expect(component.rowData).toBeDefined();
      expect(component.rowData.length).toBe(3);
    });

    it('should assign correct action states to nodes', () => {
      component.levelRowData = mockHierarchyLevels;

      component['loadNodes']();

      const rootNode = component.rowData.find(n => n.nodeId.length === 1);
      const childNode = component.rowData.find(n => n.nodeId.length === 2);

      // Root node can't be deleted (canDelete: false)
      expect(rootNode?.action).toBe(ActionState.TREE_ADD_EDIT);

      // Child node can be deleted (canDelete: true) and is not at final level
      expect(childNode?.action).toBe(ActionState.TREE_ADD_EDIT_DELETE);
    });
  });

  describe('Grid Operations', () => {
    it('should handle level grid ready event', () => {
      const mockGridApi = { test: 'levelApi' } as any;

      component.onLevelGridIsReady(mockGridApi);

      expect(component.levelGridApi).toBe(mockGridApi);
    });

    it('should handle node grid ready event', () => {
      const mockGridApi = { test: 'nodeApi' } as any;

      component.onGridIsReady(mockGridApi);

      expect(component.gridApi).toBe(mockGridApi);
    });
  });

  describe('Cell Actions', () => {
    beforeEach(() => {
      component.rowData = [...mockHierarchyNodes];
      component.gridApi = {
        setRowData: jest.fn()
      } as any;
      component.hierarchyTreeGrid = {
        rowId: 100
      } as any;
      component.levelRowData = mockHierarchyLevels;
    });

    it('should handle ADD action', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.ADD,
        rawData: { nodeId: [1] }
      } as any;

      const initialRowCount = component.rowData.length;

      component.onCellAction(mockMessage);

      expect(component.rowData.length).toBe(initialRowCount + 1);
      expect(component.gridApi.setRowData).toHaveBeenCalledWith(component.rowData);
    });

    it('should handle DELETE action', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.DELETE,
        rawData: { nodeId: [1, 1] }
      } as any;

      const initialRowCount = component.rowData.length;

      component.onCellAction(mockMessage);

      expect(component.rowData.length).toBe(initialRowCount - 1);
      expect(component.gridApi.setRowData).toHaveBeenCalledWith(component.rowData);
    });

    it('should ignore unknown actions', () => {
      const mockMessage: DataGridMessage = {
        action: 'UNKNOWN_ACTION' as any,
        rawData: { nodeId: [1] }
      } as any;

      expect(() => {
        component.onCellAction(mockMessage);
      }).not.toThrow();
    });
  });

  describe('Node Management', () => {
    beforeEach(() => {
      component.rowData = [...mockHierarchyNodes];
      component.gridApi = {
        setRowData: jest.fn()
      } as any;
      component.hierarchyTreeGrid = {
        rowId: 100
      } as any;
      component.levelRowData = mockHierarchyLevels;
    });

    it('should add child node with correct structure', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.ADD,
        rawData: { nodeId: [1] }
      } as any;

      component['addChildNode'](mockMessage);

      const newNode = component.rowData[component.rowData.length - 1];
      expect(newNode.nodeId).toEqual([1, 3]); // Next available number
      expect(newNode.parentId).toEqual([1]);
      expect(newNode.hierarchyValue).toBe('');
      expect(newNode.users).toBe(0);
      expect(newNode.items).toBe(0);
      expect(newNode.canDelete).toBe(true);
      expect(newNode.isEditing).toBe(true);
    });

    it('should calculate next level correctly for new child nodes', () => {
      // Add nodes at level [1,1] and [1,2] first
      component.rowData = [
        ...mockHierarchyNodes,
        {
          nodeId: [1, 3],
          parentId: [1],
          hierarchyValue: 'Test',
          users: 0,
          items: 0,
          canDelete: true,
          isDisabled: false,
          action: ActionState.TREE_ADD_EDIT_DELETE,
          isEditing: false,
          rowId: 3
        }
      ];

      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.ADD,
        rawData: { nodeId: [1] }
      } as any;

      component['addChildNode'](mockMessage);

      const newNode = component.rowData[component.rowData.length - 1];
      expect(newNode.nodeId).toEqual([1, 4]); // Next available number after 3
    });

    it('should delete node correctly', () => {
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.DELETE,
        rawData: { nodeId: [1, 1] }
      } as any;

      const initialCount = component.rowData.length;

      component.deleteNode(mockMessage);

      expect(component.rowData.length).toBe(initialCount - 1);
      expect(component.rowData.find(n => n.nodeId[1] === 1 && n.nodeId.length === 2)).toBeUndefined();
    });

    it('should assign correct action state for new nodes at different levels', () => {
      // Test node at intermediate level (nodeId.length + 1 < levelRowData.length)
      const mockMessage: DataGridMessage = {
        action: DatagridActionsCta.ADD,
        rawData: { nodeId: [1] }
      } as any;

      component['addChildNode'](mockMessage);

      const newNode = component.rowData[component.rowData.length - 1];
      expect(newNode.action).toBe(ActionState.TREE_ADD_EDIT_DELETE);

      // Test node at final level (nodeId.length + 1 >= levelRowData.length)
      component.rowData = [];
      const finalLevelMessage: DataGridMessage = {
        action: DatagridActionsCta.ADD,
        rawData: { nodeId: [1, 1] }
      } as any;

      component['addChildNode'](finalLevelMessage);

      const finalLevelNode = component.rowData[component.rowData.length - 1];
      expect(finalLevelNode.action).toBe(ActionState.ADD_EDIT_DELETE);
    });
  });

  describe('Data Validation', () => {
    beforeEach(() => {
      component.levelGridApi = {
        forEachNode: jest.fn((callback) => {
          mockHierarchyLevels.forEach((level, index) => {
            callback({ data: { ...level, rowId: index } });
          });
        })
      } as any;
    });

    it('should validate levels successfully with valid data', () => {
      const result = component['validateLevelData']();

      expect(result).toBe(true);
      expect(component.levelErrors).toEqual([]);
    });

    it('should fail validation for empty hierarchy name', () => {
      const invalidLevels = [
        { ...mockHierarchyLevels[0], hierarchyName: '', rowId: 0 }
      ];

      component.levelGridApi = {
        forEachNode: jest.fn((callback) => {
          invalidLevels.forEach(level => callback({ data: level }));
        })
      } as any;

      const result = component['validateLevelData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });

    it('should fail validation for duplicate hierarchy names', () => {
      const duplicateLevels = [
        { ...mockHierarchyLevels[0], hierarchyName: 'Company', rowId: 0 },
        { ...mockHierarchyLevels[1], hierarchyName: 'company', rowId: 1 }
      ];

      component.levelGridApi = {
        forEachNode: jest.fn((callback) => {
          duplicateLevels.forEach(level => callback({ data: level }));
        })
      } as any;

      const result = component['validateLevelData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });

    it('should validate nodes successfully with valid data', () => {
      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          mockHierarchyNodes.forEach((node, index) => {
            callback({ data: { ...node, rowId: index } });
          });
        })
      } as any;

      const result = component['validateData']();

      expect(result).toBe(true);
      expect(component.nodeErrors).toEqual([]);
    });

    it('should fail validation for empty hierarchy value', () => {
      const invalidNodes = [
        { ...mockHierarchyNodes[0], hierarchyValue: '', rowId: 0 }
      ];

      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          invalidNodes.forEach(node => callback({ data: node }));
        })
      } as any;

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });
  });

  describe('Save Operations', () => {
    beforeEach(() => {
      component.levelGridApi = {
        forEachNode: jest.fn((callback) => {
          const validLevel = { ...mockHierarchyLevels[0], rowId: 0 };
          callback({ data: validLevel });
        })
      } as any;
      component.gridApi = {
        forEachNode: jest.fn((callback) => {
          const validNode = { ...mockHierarchyNodes[0], rowId: 0 };
          callback({ data: validNode });
        })
      } as any;
    });

    it('should save levels successfully when validation passes', () => {
      jest.spyOn(component as any, 'validateLevelData').mockReturnValue(true);

      component['saveLevels']();

      expect(mockOrganizationService.updateLevels).toHaveBeenCalled();
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Hierarchy Levels saved successfully.');
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
    });

    it('should not save levels when validation fails', () => {
      jest.spyOn(component as any, 'validateLevelData').mockReturnValue(false);

      component['saveLevels']();

      expect(mockOrganizationService.updateLevels).not.toHaveBeenCalled();
    });

    it('should handle levels save error', () => {
      jest.spyOn(component as any, 'validateLevelData').mockReturnValue(true);
      mockOrganizationService.updateLevels.mockReturnValue(throwError('Save error'));

      component['saveLevels']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed while saving Hierarchy Levels data.');
    });

    it('should save nodes successfully when validation passes', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      jest.spyOn(component as any, 'loadLevels');

      component['saveNodes']();

      expect(mockOrganizationService.updateNodes).toHaveBeenCalled();
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Hierarchy Tree saved successfully.');
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
      expect(component['loadLevels']).toHaveBeenCalled();
    });

    it('should not save nodes when validation fails', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(false);

      component['saveNodes']();

      expect(mockOrganizationService.updateNodes).not.toHaveBeenCalled();
    });

    it('should handle nodes save error', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      mockOrganizationService.updateNodes.mockReturnValue(throwError('Save error'));

      component['saveNodes']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed while saving Hierarchy Tree data.');
    });
  });

  describe('Editing State Integration', () => {
    it('should handle EDIT action', () => {
      component.levelRowData = mockHierarchyLevels;
      component.rowData = mockHierarchyNodes;
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(OrganizationComponent);
      component = fixture.componentInstance;
      component.levelRowData = mockHierarchyLevels;
      component.rowData = mockHierarchyNodes;

      editingStateSubject.next(FormAction.EDIT);

      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['levelRowData', JSON.parse(JSON.stringify(mockHierarchyLevels))]
      ]);
      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['rowData', JSON.parse(JSON.stringify(mockHierarchyNodes))]
      ]);
    });

    it('should handle SAVE action for levels tab', () => {
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(OrganizationComponent);
      component = fixture.componentInstance;
      component.selectedTab = 0;
      component.levelGridApi = { stopEditing: jest.fn() } as any;

      // Create spy after component is recreated
      const saveLevelsSpy = jest.spyOn(component as any, 'saveLevels').mockImplementation(() => {});

      editingStateSubject.next(FormAction.SAVE);

      expect(component.levelGridApi.stopEditing).toHaveBeenCalled();
      expect(saveLevelsSpy).toHaveBeenCalled();
    });

    it('should handle SAVE action for nodes tab', () => {
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(OrganizationComponent);
      component = fixture.componentInstance;
      component.selectedTab = 1;
      component.gridApi = { stopEditing: jest.fn() } as any;

      // Create spy after component is recreated
      const saveNodesSpy = jest.spyOn(component as any, 'saveNodes').mockImplementation(() => {});

      editingStateSubject.next(FormAction.SAVE);

      expect(component.gridApi.stopEditing).toHaveBeenCalled();
      expect(saveNodesSpy).toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty level data', () => {
      component.levelRowData = [];

      expect(() => {
        component['setColumnDefinition']();
      }).not.toThrow();
    });

    it('should handle nodes without parent relationships', () => {
      const orphanNodes: GridRow<IHierarchyNode>[] = [
        {
          nodeId: [1],
          parentId: [],
          hierarchyValue: 'Orphan',
          users: 0,
          items: 0,
          canDelete: true,
          isDisabled: false,
          action: ActionState.TREE_ADD_EDIT,
          isEditing: false,
          rowId: 0
        }
      ];

      component.rowData = orphanNodes;

      expect(() => {
        component.getDataPath(orphanNodes[0]);
      }).not.toThrow();
    });

    it('should handle grid API redraw when available', () => {
      const mockLevelGridApi = { redrawRows: jest.fn() };
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(OrganizationComponent);
      component = fixture.componentInstance;
      component.levelGridApi = mockLevelGridApi as any;
      component.levelRowData = mockHierarchyLevels;
      component.rowData = mockHierarchyNodes;

      editingStateSubject.next(FormAction.EDIT);

      expect(mockLevelGridApi.redrawRows).toHaveBeenCalled();
    });
  });
});
