<div class="mt-5 ms-3">
  <div class="row mb-3">
    <label for="theme" class="h-100 m-0 align-self-center appearance-label-width">Theme: &nbsp;&nbsp;&nbsp;</label>
    <p-dropdown *ngIf="themes" id="theme" class="d-flex w-auto col-10" optionLabel="label" [options]="themes"
                [(ngModel)]="selectedTheme" (onChange)="onThemeChange($event)"
                [disabled]="!editingStateService.editingState.isEditing"></p-dropdown>
  </div>
  <div class="row mb-3 custom-skin">
    <div class="row">
      <label for="custom-theme" class="appearance-label-width h-100 m-0 align-self-center d-flex align-items-center">Custom Skin:</label>
      <p-checkbox id="custom-theme" class="w-auto me-5" [binary]="true" inputId="binary" [(ngModel)]="isCustom" [disabled]="!editingStateService.editingState.isEditing" (onChange)="onSetCustom($event)"></p-checkbox>
      <div *ngIf="isCustom" class="d-flex w-auto align-items-center">
        <div class="d-flex flex-row w-auto ms-2">
          <input maxlength="7" class="color-input" type="text" pInputText [(ngModel)]="selectedColors[0]"/>
          <p-colorPicker [ngbTooltip]="'Primary Color'" class="color-input-width" [(ngModel)]="selectedColors[0]"
                         (onChange)="onColorChanger($event, 1)"
                         [disabled]="!editingStateService.editingState.isEditing"></p-colorPicker>
        </div>
        <div class="d-flex flex-row w-auto ms-2">
          <input maxlength="7" class="color-input" type="text" pInputText [(ngModel)]="selectedColors[1]"/>
          <p-colorPicker [ngbTooltip]="'Secondary Color'" class="color-input-width" [(ngModel)]="selectedColors[1]"
                         (onChange)="onColorChanger($event, 2)"
                         [disabled]="!editingStateService.editingState.isEditing"></p-colorPicker>
        </div>
        <div class="d-flex flex-row w-auto ms-2">
          <input maxlength="7" class="color-input" type="text" pInputText [(ngModel)]="selectedColors[2]"/>
          <p-colorPicker [ngbTooltip]="'Tertiary Color'" class="color-input-width" [(ngModel)]="selectedColors[2]"
                         (onChange)="onColorChanger($event, 3)"
                         [disabled]="!editingStateService.editingState.isEditing"></p-colorPicker>
        </div>
      </div>
    </div>
  </div>
  <div class="row mb-2">
    <label for="logo" class="appearance-label-width h-100 m-0 align-self-center">Logo:&nbsp;&nbsp;&nbsp;</label>
    <p-fileUpload *ngIf="files?.[0]" #logoUpload id="logo" class="d-flex w-auto" mode="basic"
                  chooseLabel="Choose" name="logo"
                  [accept]="supportedExtensions" [files]="files" (onSelect)="onFileSelect($event)"
                  (cancel)="onCancel()" (click)="onUpload()"
                  [disabled]="!editingStateService.editingState.isEditing"></p-fileUpload>
  </div>
</div>
