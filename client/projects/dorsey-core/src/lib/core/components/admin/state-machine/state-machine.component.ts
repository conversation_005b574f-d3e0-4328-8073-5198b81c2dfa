import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Edge } from '@swimlane/ngx-graph/lib/models/edge.model';
import { catchError, fork<PERSON>oin, of, Subject } from 'rxjs';

import { takeUntil } from 'rxjs/operators';
import { TitleCasePipe } from '@angular/common';
import { RoleActions } from '../../../models/enums/role-actions';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { ToastService } from '../../../services/toast.service';
import { StateMachineService } from '../../../services/admin/state-machine.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { FormAction } from '../../../models/form-action';
import { handleCancelAction } from '../../../utils/grid-utils';
import { ICaseStatus } from '../../../models/state-machine/case-status';
import { ICaseStatusTransition } from '../../../models/state-machine/case-status-transition';
import { LovService } from '../../../services/lov/lov.service';

@Component({
  selector: 'dorsey-state-machine',
  templateUrl: './state-machine.component.html',
  styleUrls: ['./state-machine.component.scss'],
  providers: [TitleCasePipe],
})
export class StateMachineComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  readonly MAX_STATES = 8;
  readonly NODE_DIAMETER = 95;
  readonly WAIT_GRAPH_MS = 1000;

  update$: Subject<any> = new Subject();
  center$: Subject<any> = new Subject();
  zoomToFit$: Subject<any> = new Subject();
  zoomOk: boolean;
  canAdjust = true;

  colors = [
    '#BDE8A5',
    '#80BFFF',
    '#9FE7FF',
    '#F8C2DA',
    '#D6CDEA',
    '#FFCF9F',
    '#FCF8C6',
    '#AEBBC2',
  ];

  nodes: any[] = [];
  links: Edge[] = [];

  fromItems: any[] = [];
  toItems: any[] = [];

  statesItems: any[] = [];
  newState: string;

  states: State[] = [];

  path = location.pathname;
  actions = RoleActions;

  constructor(
    private dialogMessageService: DialogMessageService,
    private toastService: ToastService,
    private titleCasePipe: TitleCasePipe,
    private stateMachineService: StateMachineService,
    public editingStateService: EditingStateService,
    private lovService: LovService
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        if (FormAction.SAVE === value) {
          this.saveData();
        }
        if (FormAction.EDIT === value) {
          this.editingStateService.setData([['newState', undefined]]);
          this.editingStateService.setData([
            ['colors', JSON.parse(JSON.stringify(this.colors))],
          ]);
          this.editingStateService.setData([
            ['nodes', JSON.parse(JSON.stringify(this.nodes))],
          ]);
          this.editingStateService.setData([
            ['links', JSON.parse(JSON.stringify(this.links))],
          ]);
          this.editingStateService.setData([
            ['fromItems', JSON.parse(JSON.stringify(this.fromItems))],
          ]);
          this.editingStateService.setData([
            ['toItems', JSON.parse(JSON.stringify(this.toItems))],
          ]);
          this.editingStateService.setData([
            ['statesItems', JSON.parse(JSON.stringify(this.statesItems))],
          ]);
          this.editingStateService.setData([
            ['states', JSON.parse(JSON.stringify(this.states))],
          ]);
        }
      });
    handleCancelAction(this.editingStateService, this).subscribe(() => {
      this.adjustGraph(false);
    });
  }

  ngOnInit() {
    this.loadData();
  }

  ngAfterViewInit() {
    this.setGraphHeight();
  }

  private loadData() {
    forkJoin({
      nodes: this.lovService
        .findAllCaseStatus()
        .pipe(
          catchError((error) =>
            of(this.toastService.displayError(error.error.error))
          )
        ),
      links: this.stateMachineService
        .findAllCaseStatusTransitions()
        .pipe(
          catchError((error) =>
            of(this.toastService.displayError(error.error.error))
          )
        ),
    }).subscribe((resp) => {
      const sourceStates: State[] = [];
      const states: State[] = [];

      (resp.nodes as ICaseStatus[]).forEach((n) => {
        const color = this.colors.pop();
        states.push({ id: n.code, name: n.description, color });
        this.nodes.push({
          id: n.code,
          label: n.description,
          color,
          dimension: { width: this.NODE_DIAMETER },
        });
        this.fromItems.push({ code: n.code, name: n.description });
        this.toItems.push({ code: n.code, name: n.description });
        this.statesItems.push({ code: n.code, name: n.description });
      });
      this.states = states;
      resp.links.forEach((l) => (l.id = `n${l.id}`));
      this.links = resp.links;

      this.adjustGraph(false);
    });
  }

  adjustGraph(zoomChanged: boolean) {
    if (this.canAdjust) {
      this.zoomOk = false;
      setTimeout(() => {
        this.update$.next(true);
        this.center$.next(true);
        this.zoomToFit$.next(true);
        this.zoomOk = true;
      }, this.WAIT_GRAPH_MS);
    }

    // Prevent to adjust graph when still in process.
    if (zoomChanged && this.canAdjust) {
      this.canAdjust = false;
      setTimeout(() => {
        this.canAdjust = true;
      }, this.WAIT_GRAPH_MS);
    }
  }

  createNode(value) {
    const state = {
      id: value,
      name: this.titleCasePipe.transform(value),
      color: this.colors.pop(),
    };
    const states = [...this.states];
    states.push(state);
    this.states = states;
    this.nodes.push({
      id: state.id,
      label: state.name.substring(0, 8),
      color: state.color,
      dimension: {
        width: this.NODE_DIAMETER,
      },
    });
    this.fromItems.push({ code: state.id, name: state.name });
    this.update$.next(true);

    this.newState = null;
  }

  removeNode(value) {
    this.nodes = this.nodes.filter((n) => n.id !== value);

    if (this.links.some((l) => l.source === value)) {
      this.links = this.links.filter((l) => l.source !== value);
    }
    if (this.links.some((l) => l.target === value)) {
      this.links = this.links.filter((l) => l.target !== value);
    }

    this.fromItems = this.fromItems.filter((i) => i.code !== value);

    this.update$.next(true);
  }

  createState() {
    if (this.statesItems.length >= this.MAX_STATES) {
      this.dialogMessageService.displayError(
        `States cannot exceed the maximum of ${this.MAX_STATES}.`
      );
    } else if (
      this.statesItems.some((s) => s.code === this.newState.toLowerCase())
    ) {
      this.dialogMessageService.displayError(
        `The state "${this.newState}" already exist.`
      );
    } else {
      this.statesItems.push({
        code: this.newState.toLowerCase(),
        name: this.newState,
      });
      this.createNode(this.newState);
    }
  }

  deleteState(selectedState: string) {
    this.statesItems = this.statesItems.filter((n) => n.code !== selectedState);

    const value = selectedState.toLowerCase();
    this.removeNode(value);
    this.deleteTarget(value);
  }

  setGraphHeight() {
    setTimeout(() => {
      document
        .getElementsByClassName('ngx-charts-outer')[0]
        ?.setAttribute(
          'style',
          `max-height: ${
            document.getElementById('states-container').clientHeight
          }px`
        );
    });
  }

  selectFrom(value) {
    if (value !== null) {
      this.toItems = this.fromItems.filter(
        (i) => i.code !== value.toLowerCase()
      );
    }
  }

  deleteTarget(value: string) {
    this.states = this.states.filter((n) => {
      if (n.id === value) {
        this.colors.push(n.color);
      }
      return n.id !== value;
    });
  }

  linkNodes(from: string, to: string) {
    if (this.links.some((l) => l.source === from && l.target === to)) {
      this.toastService.displayError(
        `The link "${from} -> ${to}" already exist.`
      );
    } else {
      this.links.push({
        id: `n${this.idGen().toString()}`,
        source: from,
        target: to,
      });
      this.update$.next(true);
      this.adjustGraph(false);
    }
  }

  unlinkNode(from: string, to: string) {
    if (this.links.some((l) => l.source === from && l.target === to)) {
      if (this.links.length > 1) {
        this.links = this.links.filter(
          (l) => l.source !== from || l.target !== to
        );
      } else {
        this.links = [];
      }

      this.update$.next(true);
    } else {
      this.toastService.displayError(
        `The Link "${from} -> ${to}" doesn't exist.`
      );
    }
  }

  idGen(): number {
    if (this.links.length) {
      return (
        Math.max(...this.links.map((l) => +l.id.substring(1, l.id.length))) + 1
      );
    } else {
      return 1;
    }
  }

  private saveData() {
    this.newState = null;
    const statuses: ICaseStatus[] = [];
    const transitions: ICaseStatusTransition[] = [];

    this.nodes.forEach((n) =>
      statuses.push({ code: n.id, description: n.label })
    );
    this.links.forEach((l) =>
      transitions.push({
        id: l.id.replace('n', ''),
        source: l.source,
        target: l.target,
      })
    );

    this.lovService
      .updateCaseStatus(statuses)
      .pipe(takeUntil(this.destroy$))
      .subscribe(
        () => {
          this.toastService.displaySuccess('States updated successfully.');
          this.stateMachineService
            .updateCaseStatusTransitions(transitions)
            .pipe(takeUntil(this.destroy$))
            .subscribe(
              () => {
                this.editingStateService.setValue(FormAction.SUBMIT);
                this.toastService.displaySuccess(
                  'Transitions updated successfully.'
                );
              },
              () =>
                this.toastService.displayError('Failed updating Transitions.')
            );
        },
        () => this.toastService.displayError('Failed updating States.')
      );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

interface State {
  id: string;
  name: string;
  color: string;
}
