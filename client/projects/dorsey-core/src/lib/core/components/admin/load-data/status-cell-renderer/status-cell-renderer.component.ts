import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { UploadSummaryEnum } from '../upload-summary.enum';

@Component({
  selector: 'status-cell-renderer',
  templateUrl: './status-cell-renderer.component.html',
  styleUrls: ['./status-cell-renderer.component.scss']
})
export class StatusCellRendererComponent implements ICellRendererAngularComp {
  colorSelectionFunction: () => void = null;
  backgroundColor: StatusCellRendererBackgroundColor;
  value: UploadSummaryEnum;

  agInit(params: any): void {
    this.value = params.value;
    if (params.colorSelectionFunction) {
      this.backgroundColor = params.colorSelectionFunction(this.value);
      this.updateBackground(params);
    }
  }

  refresh(params: any): boolean {
    this.value = params.value;
    return true;
  }

  updateBackground(params: any) {
    if (params.api) {
      const grid = params.api.gridBodyCtrl.eGridBody as HTMLDivElement;
      const enumColumn = grid.querySelector(
        `.ag-center-cols-container div[row-index="${params.rowIndex}"] > div[col-id="${params.column.colId}"]`
      );
      enumColumn?.classList.add('status-cell-background-color-' + this.backgroundColor);
    }
  }
}

export enum StatusCellRendererBackgroundColor {
  GREEN = 'green',
  YELLOW = 'yellow',
  ORANGE = 'orange',
  RED = 'red'
}
