import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { GridApi } from 'ag-grid-community';
import { catchError, firstValue<PERSON>rom, fork<PERSON>oin, of, Subject } from 'rxjs';
import { formatDate } from '@angular/common';
import { takeUntil } from 'rxjs/operators';
import { GridRow } from '../../datagrid/models/grid-row.model';
import { IRole } from '../../../models/admin/roles/role.model';
import { RoleActions } from '../../../models/enums/role-actions';
import { UserService } from '../../../services/admin/user.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { ToastService } from '../../../services/toast.service';
import { DialogMessageService } from '../../../services/dialog-message.service';

import { FormAction } from '../../../models/form-action';
import { getGridData, handleCancelAction } from '../../../utils/grid-utils';
import { TreeSelectCellEditorComponent } from '../../datagrid/customs/cell-editors/tree-select-cell-editor/tree-select-cell-editor.component';
import { MultiDropdownCellEditorComponent } from '../../datagrid/customs/cell-editors/multi-dropdown-cell-editor/multi-dropdown-cell-editor.component';
import { CalendarCellRendererComponent } from '../../datagrid/customs/cell-renderers/calendar-cell-renderer/calendar-cell-renderer.component';
import { DropdownCellEditorComponent } from '../../datagrid/customs/cell-editors/dropdown-cell-editor/dropdown-cell-editor.component';
import { RoleService } from '../../../services/admin/role.service';
import { OrganizationService } from '../../../services/admin/organization.service';
import {
  addHierarchyLabelProperty,
  addHierarchyLabelToNodes,
} from '../../../utils/organization-hierarchy.util';

@Component({
  selector: 'dorsey-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss'],
})
export class UserListComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  readonly title = 'User List';

  rowData: GridRow<any>[];
  gridApi: GridApi;
  roles: IRole[] = [];
  hierarchies: any[] = [];
  errors: string[] = [];
  nodes: any[];

  panelWidth: number;

  columnDefs;

  path = location.pathname;
  actions = RoleActions;

  constructor(
    private userService: UserService,
    private editingStateService: EditingStateService,
    private toastService: ToastService,
    private dialogMessageService: DialogMessageService,
    private roleService: RoleService,
    private organizationService: OrganizationService
  ) {
    this.editingStateService
      .getValue()
      .pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        if (FormAction.SAVE === value) {
          this.saveData();
        }
        if (FormAction.EDIT === value) {
          this.editingStateService.setData([
            ['rowData', JSON.parse(JSON.stringify(this.rowData))],
          ]);
        }
      });
    firstValueFrom(handleCancelAction(this.editingStateService, this));
  }

  ngOnInit(): void {
    this.loadData();
  }

  setColumnDefinition() {
    this.columnDefs = [
      {
        field: 'firstName',
        editable: true,
        minWidth: 125,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 50,
        },
      },
      {
        field: 'lastName',
        editable: true,
        minWidth: 125,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 50,
        },
      },
      {
        field: 'title',
        editable: true,
        minWidth: 125,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 15,
        },
      },
      {
        field: 'email',
        editable: true,
        minWidth: 250,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: {
          maxLength: 320,
        },
      },
      {
        field: 'hierarchyData',
        headerName: 'Hierarchy',
        editable: true,
        cellClass: 'overflow-visible',
        minWidth: 250,
        cellEditor: TreeSelectCellEditorComponent,
        cellEditorParams: (params) => {
          return {
            nodes: this.hierarchies,
            selectionMode: 'single',
            selectedNodes: params.data.hierarchyData,
          };
        },
        valueGetter: (params) => params.data?.hierarchyData?.label,
      },
      {
        field: 'roles',
        editable: true,
        minWidth: 200,
        cellEditor: MultiDropdownCellEditorComponent,
        cellEditorParams: {
          options: this.roles.map((r) => {
            return { name: r.name, code: r.name };
          }),
        },
        valueGetter: (params) => {
          return params.data.roles.map((r) => '-' + r.name + '\n').join('');
        },
      },
      {
        field: 'effectiveDate',
        headerName: 'Effective Dt',
        cellClass: 'text-center',
        editable: true,
        minWidth: 175,
        cellEditor: CalendarCellRendererComponent,
        valueFormatter: (params) => {
          return params.value
            ? formatDate(params.value, 'MM/dd/yyyy', 'en-US')
            : null;
        },
      },
      {
        field: 'terminationDate',
        headerName: 'Termination Dt',
        cellClass: 'text-center',
        editable: true,
        minWidth: 175,
        cellEditor: CalendarCellRendererComponent,
        valueFormatter: (params) => {
          return params.value
            ? formatDate(params.value, 'MM/dd/yyyy', 'en-US')
            : null;
        },
      },
      {
        field: 'isActive',
        headerName: 'Status',
        minWidth: 125,
        editable: true,
        cellEditor: DropdownCellEditorComponent,
        cellEditorParams: { options: ['Active', 'Inactive'] },
        valueSetter: (params) =>
          (params.data.isActive = params.value === 'Active'),
        valueGetter: (params) => (params.data.isActive ? 'Active' : 'Inactive'),
      },
    ];
  }

  onGridIsReady(gridApi: GridApi) {
    this.gridApi = gridApi;
  }

  onNewRow(): any {
    return {
      firstName: '',
      lastName: '',
      title: '',
      email: '',
      roles: [],
      isActive: true,
    };
  }

  private loadData() {
    forkJoin({
      users: this.userService
        .findAll()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading users.'
              )
            )
          )
        ),
      roles: this.roleService
        .findAll()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading roles.'
              )
            )
          )
        ),
      hierarchies: this.organizationService
        .findOrgTree()
        .pipe(
          catchError(() =>
            of(
              this.toastService.displayError(
                'Error occurred while loading Organization Hierarchy.'
              )
            )
          )
        ),
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe((resp) => {
        addHierarchyLabelProperty(resp.users);
        this.rowData = resp.users as any[];
        this.roles = resp.roles.filter((r) => r.name !== 'Visitor');
        this.hierarchies = resp.hierarchies;
        addHierarchyLabelToNodes(this.hierarchies);
        this.setColumnDefinition();
      });
  }

  private saveData() {
    if (this.validateData()) {
      this.rowData.forEach((r) => {
        r.roles = this.roles.filter((d) =>
          r.roles.some((r) => d.name === r.name)
        );
        r.hierarchyId = r?.hierarchyData?.id;
        //children property has circular structure (prevent JSON.stringify() converting circular structure error)
        delete r.hierarchyData;
      });
      this.userService
        .updateUsers(this.rowData)
        .pipe(takeUntil(this.destroy$))
        .subscribe(
          () => {
            this.editingStateService.setValue(FormAction.SUBMIT);
            this.toastService.displaySuccess('Users saved successfully.');
            this.loadData();
          },
          (error) => this.toastService.displayError(error.error.error)
        );
    }
  }

  private validateData(): boolean {
    const emailRegex =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    const data = getGridData(this.gridApi);
    for (const row of data) {
      if (!row.firstName.length) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: First Name is required.`
        );
      }
      if (!row.lastName.length) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: Last Name is required.`
        );
      }
      if (!row.email || !row.email.length) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: Email is required.`
        );
      }
      if (!row.roles.length) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: Role is required.`
        );
      }
      if (row.isActive == null) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: Status is required.`
        );
      }
      if (!row.effectiveDate) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: Effective Date is required.`
        );
      }

      if (!emailRegex.test(row.email)) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Users Grid: Row ${
            row.rowId + 1
          }: Email is invalid.`
        );
      }

      if (
        this.rowData.filter((r) => r.email?.length && r.email === row.email)
          .length > 1
      ) {
        this.errors.push(
          `${(this.errors.length + 1).toString()}. Cases Grid: Row ${
            row.rowId + 1
          }: Email "${row.email}" is duplicated.`
        );
      }
    }

    if (this.errors.length) {
      this.dialogMessageService.displayError(this.errors.join('\n'), false);
      this.errors = [];
      return false;
    }

    return !this.errors.length;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
