import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TitleCasePipe } from '@angular/common';
import { of, throwError, Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { StateMachineComponent } from './state-machine.component';
import { DialogMessageService } from '../../../services/dialog-message.service';
import { ToastService } from '../../../services/toast.service';
import { StateMachineService } from '../../../services/admin/state-machine.service';
import { EditingStateService } from '../../../services/editing-state.service';
import { LovService } from '../../../services/lov/lov.service';
import { FormAction } from '../../../models/form-action';
import { RoleActions } from '../../../models/enums/role-actions';
import { ICaseStatus } from '../../../models/state-machine/case-status';
import { ICaseStatusTransition } from '../../../models/state-machine/case-status-transition';

// Mock handleCancelAction
jest.mock('../../../utils/grid-utils', () => ({
  handleCancelAction: jest.fn(() => of({}))
}));

describe('StateMachineComponent', () => {
  let component: StateMachineComponent;
  let fixture: ComponentFixture<StateMachineComponent>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockTitleCasePipe: jest.Mocked<TitleCasePipe>;
  let mockStateMachineService: jest.Mocked<StateMachineService>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockLovService: jest.Mocked<LovService>;

  // For tests that don't need template rendering
  let componentInstance: StateMachineComponent;

  const mockCaseStatuses: ICaseStatus[] = [
    { code: 'NEW', description: 'New', deprecated: false },
    { code: 'PROGRESS', description: 'In Progress', deprecated: false },
    { code: 'DONE', description: 'Done', deprecated: false }
  ];

  const mockTransitions: any[] = [
    { id: '1', source: 'NEW', target: 'PROGRESS' },
    { id: '2', source: 'PROGRESS', target: 'DONE' }
  ];

  beforeEach(async () => {
    const mockEditingStateSubject = new Subject<FormAction>();

    mockDialogMessageService = {
      sendMessage: jest.fn(),
      displayWarning: jest.fn(),
      displayError: jest.fn(),
      displayCustom: jest.fn(),
      message: of()
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayInfo: jest.fn(),
      displayWarning: jest.fn(),
      displayError: jest.fn()
    } as any;

    mockTitleCasePipe = {
      transform: jest.fn((value: string) => value.charAt(0).toUpperCase() + value.slice(1).toLowerCase())
    } as any;

    mockStateMachineService = {
      findAllCaseStatusTransitions: jest.fn().mockReturnValue(of(mockTransitions)),
      updateCaseStatusTransitions: jest.fn().mockReturnValue(of({}))
    } as any;

    mockEditingStateService = {
      getValue: jest.fn().mockReturnValue(mockEditingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: {
        isEditing: false,
        hasChanges: false
      }
    } as any;

    mockLovService = {
      findAllCaseStatus: jest.fn().mockReturnValue(of(mockCaseStatuses)),
      updateCaseStatus: jest.fn().mockReturnValue(of({}))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [StateMachineComponent],
      providers: [
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: ToastService, useValue: mockToastService },
        { provide: TitleCasePipe, useValue: mockTitleCasePipe },
        { provide: StateMachineService, useValue: mockStateMachineService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: LovService, useValue: mockLovService }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    // Create component instance without template rendering for most tests
    componentInstance = new StateMachineComponent(
      mockDialogMessageService,
      mockToastService,
      mockTitleCasePipe,
      mockStateMachineService,
      mockEditingStateService,
      mockLovService
    );

    // For tests that need template rendering
    fixture = TestBed.createComponent(StateMachineComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(componentInstance).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(componentInstance.MAX_STATES).toBe(8);
      expect(componentInstance.NODE_DIAMETER).toBe(95);
      expect(componentInstance.WAIT_GRAPH_MS).toBe(1000);
      expect(componentInstance.nodes).toEqual([]);
      expect(componentInstance.links).toEqual([]);
      expect(componentInstance.fromItems).toEqual([]);
      expect(componentInstance.toItems).toEqual([]);
      expect(componentInstance.statesItems).toEqual([]);
      expect(componentInstance.states).toEqual([]);
      expect(componentInstance.path).toBe(location.pathname);
      expect(componentInstance.actions).toBe(RoleActions);
      expect(componentInstance.canAdjust).toBe(true);
    });

    it('should have correct color palette', () => {
      expect(componentInstance.colors).toEqual([
        '#BDE8A5',
        '#80BFFF',
        '#9FE7FF',
        '#F8C2DA',
        '#D6CDEA',
        '#FFCF9F',
        '#FCF8C6',
        '#AEBBC2',
      ]);
    });

    it('should have required dependencies injected', () => {
      expect(componentInstance.editingStateService).toBe(mockEditingStateService);
    });
  });

  describe('ngOnInit', () => {
    it('should call loadData on initialization', () => {
      jest.spyOn(componentInstance as any, 'loadData');

      componentInstance.ngOnInit();

      expect(componentInstance['loadData']).toHaveBeenCalled();
    });
  });

  describe('ngAfterViewInit', () => {
    it('should call setGraphHeight', () => {
      jest.spyOn(componentInstance, 'setGraphHeight');

      componentInstance.ngAfterViewInit();

      expect(componentInstance.setGraphHeight).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should load case statuses and transitions successfully', () => {
      componentInstance['loadData']();

      expect(mockLovService.findAllCaseStatus).toHaveBeenCalled();
      expect(mockStateMachineService.findAllCaseStatusTransitions).toHaveBeenCalled();
    });

    it('should process case statuses into nodes and items', () => {
      componentInstance['loadData']();

      expect(componentInstance.nodes.length).toBe(3);
      expect(componentInstance.fromItems.length).toBe(3);
      expect(componentInstance.toItems.length).toBe(3);
      expect(componentInstance.statesItems.length).toBe(3);
      expect(componentInstance.states.length).toBe(3);

      // Check first node structure
      expect(componentInstance.nodes[0]).toEqual({
        id: 'NEW',
        label: 'New',
        color: expect.any(String),
        dimension: { width: 95 }
      });
    });

    it('should process transitions with prefixed IDs', () => {
      componentInstance['loadData']();

      expect(componentInstance.links.length).toBe(2);
      // The links should have IDs that start with 'n' (prefixed from original IDs)
      expect(componentInstance.links[0].id).toMatch(/^n.*1$/);
      expect(componentInstance.links[1].id).toMatch(/^n.*2$/);
    });

    it('should handle case status service error gracefully', () => {
      mockLovService.findAllCaseStatus.mockReturnValue(throwError({ error: { error: 'Service error' } }));

      componentInstance['loadData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Service error');
    });

    it('should handle transitions service error gracefully', () => {
      mockStateMachineService.findAllCaseStatusTransitions.mockReturnValue(throwError({ error: { error: 'Transition error' } }));

      componentInstance['loadData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Transition error');
    });
  });

  describe('Graph Adjustment', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should adjust graph when canAdjust is true', () => {
      componentInstance.canAdjust = true;
      jest.spyOn(componentInstance.update$, 'next');
      jest.spyOn(componentInstance.center$, 'next');
      jest.spyOn(componentInstance.zoomToFit$, 'next');

      componentInstance.adjustGraph(false);

      expect(componentInstance.zoomOk).toBe(false);

      // Fast-forward time
      jest.advanceTimersByTime(1000);

      expect(componentInstance.update$.next).toHaveBeenCalledWith(true);
      expect(componentInstance.center$.next).toHaveBeenCalledWith(true);
      expect(componentInstance.zoomToFit$.next).toHaveBeenCalledWith(true);
      expect(componentInstance.zoomOk).toBe(true);
    });

    it('should not adjust graph when canAdjust is false', () => {
      componentInstance.canAdjust = false;
      jest.spyOn(componentInstance.update$, 'next');

      componentInstance.adjustGraph(false);

      jest.advanceTimersByTime(1000);

      expect(componentInstance.update$.next).not.toHaveBeenCalled();
    });

    it('should temporarily disable adjustment when zoomChanged is true', () => {
      componentInstance.canAdjust = true;

      componentInstance.adjustGraph(true);

      expect(componentInstance.canAdjust).toBe(false);

      // Fast-forward time
      jest.advanceTimersByTime(1000);

      expect(componentInstance.canAdjust).toBe(true);
    });
  });

  describe('State Creation', () => {
    beforeEach(() => {
      componentInstance.statesItems = [
        { code: 'new', name: 'New' },
        { code: 'progress', name: 'Progress' }
      ];
    });

    it('should create new state successfully', () => {
      componentInstance.newState = 'Review';
      // Add mock method that doesn't exist in our test component
      componentInstance['createNode'] = jest.fn();

      componentInstance.createState();

      expect(componentInstance.statesItems).toContainEqual({
        code: 'review',
        name: 'Review'
      });
      expect(componentInstance['createNode']).toHaveBeenCalledWith('Review');
    });

    it('should prevent creating state when max states reached', () => {
      componentInstance.statesItems = new Array(8).fill(0).map((_, i) => ({ code: `state${i}`, name: `State${i}` }));
      componentInstance.newState = 'NewState';

      componentInstance.createState();

      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        'States cannot exceed the maximum of 8.'
      );
    });

    it('should prevent creating duplicate state', () => {
      componentInstance.newState = 'New'; // Already exists as 'new'

      componentInstance.createState();

      expect(mockDialogMessageService.displayError).toHaveBeenCalledWith(
        'The state "New" already exist.'
      );
    });
  });

  describe('State Deletion', () => {
    beforeEach(() => {
      componentInstance.statesItems = [
        { code: 'new', name: 'New' },
        { code: 'progress', name: 'Progress' },
        { code: 'done', name: 'Done' }
      ];
      componentInstance.states = [
        { id: 'new', name: 'New', color: '#BDE8A5' },
        { id: 'progress', name: 'Progress', color: '#80BFFF' },
        { id: 'done', name: 'Done', color: '#9FE7FF' }
      ];
    });

    it('should delete state successfully', () => {
      // Add mock methods that don't exist in our test component
      componentInstance['removeNode'] = jest.fn();
      componentInstance['deleteTarget'] = jest.fn();

      componentInstance.deleteState('progress'); // Use lowercase to match the code

      expect(componentInstance.statesItems).not.toContainEqual({ code: 'progress', name: 'Progress' });
      expect(componentInstance['removeNode']).toHaveBeenCalledWith('progress');
      expect(componentInstance['deleteTarget']).toHaveBeenCalledWith('progress');
    });
  });

  describe('Node Linking', () => {
    beforeEach(() => {
      componentInstance.links = [
        { id: 'n1', source: 'new', target: 'progress' }
      ];
      jest.spyOn(componentInstance.update$, 'next');
      jest.spyOn(componentInstance, 'adjustGraph');
    });

    it('should link nodes successfully', () => {
      jest.spyOn(componentInstance, 'idGen').mockReturnValue(2);

      componentInstance.linkNodes('progress', 'done');

      expect(componentInstance.links).toContainEqual({
        id: 'n2',
        source: 'progress',
        target: 'done'
      });
      expect(componentInstance.update$.next).toHaveBeenCalledWith(true);
      expect(componentInstance.adjustGraph).toHaveBeenCalledWith(false);
    });

    it('should prevent creating duplicate link', () => {
      componentInstance.linkNodes('new', 'progress');

      expect(mockToastService.displayError).toHaveBeenCalledWith(
        'The link "new -> progress" already exist.'
      );
    });
  });

  describe('Node Unlinking', () => {
    beforeEach(() => {
      componentInstance.links = [
        { id: 'n1', source: 'new', target: 'progress' },
        { id: 'n2', source: 'progress', target: 'done' }
      ];
      jest.spyOn(componentInstance.update$, 'next');
    });

    it('should unlink nodes successfully when multiple links exist', () => {
      componentInstance.unlinkNode('new', 'progress');

      expect(componentInstance.links).not.toContainEqual({
        id: 'n1',
        source: 'new',
        target: 'progress'
      });
      expect(componentInstance.update$.next).toHaveBeenCalledWith(true);
    });

    it('should clear all links when only one link exists', () => {
      componentInstance.links = [{ id: 'n1', source: 'new', target: 'progress' }];

      componentInstance.unlinkNode('new', 'progress');

      expect(componentInstance.links).toEqual([]);
      expect(componentInstance.update$.next).toHaveBeenCalledWith(true);
    });

    it('should show error when trying to unlink non-existent link', () => {
      componentInstance.unlinkNode('done', 'new');

      expect(mockToastService.displayError).toHaveBeenCalledWith(
        'The Link "done -> new" doesn\'t exist.'
      );
    });
  });

  describe('ID Generation', () => {
    it('should generate ID 1 when no links exist', () => {
      componentInstance.links = [];

      const result = componentInstance.idGen();

      expect(result).toBe(1);
    });

    it('should generate next sequential ID when links exist', () => {
      componentInstance.links = [
        { id: 'n1', source: 'a', target: 'b' },
        { id: 'n3', source: 'b', target: 'c' },
        { id: 'n2', source: 'c', target: 'd' }
      ];

      const result = componentInstance.idGen();

      expect(result).toBe(4); // Max is 3, so next is 4
    });
  });

  describe('From Selection', () => {
    beforeEach(() => {
      componentInstance.fromItems = [
        { code: 'new', name: 'New' },
        { code: 'progress', name: 'Progress' },
        { code: 'done', name: 'Done' }
      ];
    });

    it('should filter toItems when from value is selected', () => {
      const selectedValue = 'NEW'; // selectFrom expects a string

      componentInstance.selectFrom(selectedValue);

      expect(componentInstance.toItems).toEqual([
        { code: 'progress', name: 'Progress' },
        { code: 'done', name: 'Done' }
      ]);
    });

    it('should handle null value selection', () => {
      componentInstance.selectFrom(null);

      // Should not throw error and toItems should remain unchanged
      expect(() => componentInstance.selectFrom(null)).not.toThrow();
    });
  });

  describe('Delete Target', () => {
    beforeEach(() => {
      componentInstance.states = [
        { id: 'new', name: 'New', color: '#BDE8A5' },
        { id: 'progress', name: 'Progress', color: '#80BFFF' },
        { id: 'done', name: 'Done', color: '#9FE7FF' }
      ];
      componentInstance.colors = ['#FCF8C6'];
    });

    it('should remove state and return color to palette', () => {
      componentInstance['deleteTarget']('progress');

      expect(componentInstance.states).not.toContainEqual({
        id: 'progress',
        name: 'Progress',
        color: '#80BFFF'
      });
      expect(componentInstance.colors).toContain('#80BFFF');
    });
  });

  describe('Save Data', () => {
    beforeEach(() => {
      componentInstance.nodes = [
        { id: 'new', label: 'New', color: '#BDE8A5' },
        { id: 'progress', label: 'Progress', color: '#80BFFF' }
      ];
      componentInstance.links = [
        { id: 'n1', source: 'new', target: 'progress' }
      ];
      componentInstance.newState = 'TestState';
    });

    it('should save states and transitions successfully', () => {
      componentInstance['saveData']();

      expect(componentInstance.newState).toBeNull();
      expect(mockLovService.updateCaseStatus).toHaveBeenCalledWith([
        { code: 'new', description: 'New' },
        { code: 'progress', description: 'Progress' }
      ]);
    });

    it('should save transitions after states are saved', () => {
      componentInstance['saveData']();

      expect(mockStateMachineService.updateCaseStatusTransitions).toHaveBeenCalledWith([
        { id: '1', source: 'new', target: 'progress' }
      ]);
    });

    it('should show success messages on successful save', () => {
      componentInstance['saveData']();

      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('States updated successfully.');
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Transitions updated successfully.');
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
    });

    it('should handle states update error', () => {
      mockLovService.updateCaseStatus.mockReturnValue(throwError('States error'));

      componentInstance['saveData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed updating States.');
    });

    it('should handle transitions update error', () => {
      mockStateMachineService.updateCaseStatusTransitions.mockReturnValue(throwError('Transitions error'));

      componentInstance['saveData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed updating Transitions.');
    });
  });

  describe('Editing State Integration', () => {
    it('should handle SAVE action', () => {
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      const newComponent = new StateMachineComponent(
        mockDialogMessageService,
        mockToastService,
        mockTitleCasePipe,
        mockStateMachineService,
        mockEditingStateService,
        mockLovService
      );

      // Spy on the saveData method after component creation
      const saveDataSpy = jest.spyOn(newComponent as any, 'saveData');

      editingStateSubject.next(FormAction.SAVE);

      expect(saveDataSpy).toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(componentInstance['destroy$'], 'next');
      const completeSpy = jest.spyOn(componentInstance['destroy$'], 'complete');

      componentInstance.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      const destroySpy = jest.spyOn(componentInstance['destroy$'], 'next');

      componentInstance.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Graph Height Setting', () => {
    beforeEach(() => {
      jest.useFakeTimers();

      // Mock DOM elements
      const mockElement = {
        setAttribute: jest.fn(),
        clientHeight: 500
      };

      Object.defineProperty(document, 'getElementsByClassName', {
        value: jest.fn(() => [mockElement]),
        writable: true
      });

      Object.defineProperty(document, 'getElementById', {
        value: jest.fn(() => mockElement),
        writable: true
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should set graph height based on states container', () => {
      const mockNgxChartsElement = { setAttribute: jest.fn() };
      const mockStatesContainer = { clientHeight: 400 };

      (document.getElementsByClassName as jest.Mock).mockReturnValue([mockNgxChartsElement]);
      (document.getElementById as jest.Mock).mockReturnValue(mockStatesContainer);

      componentInstance.setGraphHeight();

      jest.advanceTimersByTime(0);

      expect(mockNgxChartsElement.setAttribute).toHaveBeenCalledWith(
        'style',
        'max-height: 400px'
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty case statuses response', () => {
      mockLovService.findAllCaseStatus.mockReturnValue(of([]));

      componentInstance['loadData']();

      expect(componentInstance.nodes).toEqual([]);
      expect(componentInstance.states).toEqual([]);
    });

    it('should handle empty transitions response', () => {
      mockStateMachineService.findAllCaseStatusTransitions.mockReturnValue(of([]));

      componentInstance['loadData']();

      expect(componentInstance.links).toEqual([]);
    });

    it('should handle case status without description', () => {
      const statusWithoutDescription = [{ code: 'TEST', deprecated: false }];
      mockLovService.findAllCaseStatus.mockReturnValue(of(statusWithoutDescription));

      componentInstance['loadData']();

      expect(componentInstance.nodes[0].label).toBeUndefined();
    });
  });
});
