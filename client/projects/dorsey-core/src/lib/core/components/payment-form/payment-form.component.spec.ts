import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TitleCasePipe } from '@angular/common';
import { of, throwError } from 'rxjs';

import { PaymentFormComponent } from './payment-form.component';
import { StripeService } from 'ngx-stripe';
import { PaymentService } from '../../services/payment/payment.service';
import { DynamicDialogConfig, DynamicDialogRef, DialogService } from 'primeng/dynamicdialog';
import { ToastService } from '../../services/toast.service';
import { EditingStateService } from '../../services/editing-state.service';
import { LoadingService } from '../../services/loading.service';
import { IUserBillingInformation } from '../../models/admin/users/user-billing-information.model';
import { PaymentIntentResult, PaymentIntent } from '@stripe/stripe-js';

describe('PaymentFormComponent', () => {
  let component: PaymentFormComponent;
  let fixture: ComponentFixture<PaymentFormComponent>;
  let paymentService: jest.Mocked<PaymentService>;
  let toastService: jest.Mocked<ToastService>;
  let loadingService: jest.Mocked<LoadingService>;
  let dialogRef: jest.Mocked<DynamicDialogRef>;

  const mockCards: IUserBillingInformation[] = [
    {
      id: 'card1',
      cardholderName: 'John Doe',
      cardNumber: '**** **** **** 1234',
      brand: 'visa',
      expDate: '12/25',
      isExpired: false,
      isPreferred: true,
      country: 'US',
      state: 'CA',
      city: 'San Francisco',
      addressLine1: '123 Main St',
      zipCode: '94105'
    },
    {
      id: 'card2',
      cardholderName: 'Jane Smith',
      cardNumber: '**** **** **** 5678',
      brand: 'mastercard',
      expDate: '06/24',
      isExpired: false,
      isPreferred: false,
      country: 'US',
      state: 'NY',
      city: 'New York',
      addressLine1: '456 Oak Ave',
      zipCode: '10001'
    },
    {
      id: 'card3',
      cardholderName: 'Bob Johnson',
      cardNumber: '**** **** **** 9999',
      brand: 'amex',
      expDate: '01/23',
      isExpired: true,
      isPreferred: false,
      country: 'US',
      state: 'TX',
      city: 'Austin',
      addressLine1: '789 Pine St',
      zipCode: '73301'
    }
  ];

  const mockStripeService = {
    createPaymentMethod: jest.fn(),
    elements: jest.fn(),
    confirmCardPayment: jest.fn()
  };

  const mockPaymentService = {
    createPaymentMethod: jest.fn(),
    findCards: jest.fn(),
    createPaymentIntent: jest.fn(),
    confirmPayment: jest.fn()
  };

  const mockDynamicDialogConfig = {
    data: {
      amount: 100
    }
  };

  const mockDynamicDialogRef = {
    close: jest.fn()
  };

  const mockToastService = {
    displaySuccess: jest.fn(),
    displayError: jest.fn()
  };

  const mockEditingStateService = {
    editingState: {
      isEditing: false
    }
  };

  const mockLoadingService = {
    setLoading: jest.fn()
  };

  const mockDialogService = {
    open: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PaymentFormComponent ],
      imports: [ ReactiveFormsModule, FormsModule ],
      providers: [
        FormBuilder,
        TitleCasePipe,
        { provide: StripeService, useValue: mockStripeService },
        { provide: PaymentService, useValue: mockPaymentService },
        { provide: DynamicDialogConfig, useValue: mockDynamicDialogConfig },
        { provide: DynamicDialogRef, useValue: mockDynamicDialogRef },
        { provide: ToastService, useValue: mockToastService },
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: LoadingService, useValue: mockLoadingService },
        { provide: DialogService, useValue: mockDialogService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PaymentFormComponent);
    component = fixture.componentInstance;

    // Get service instances for testing
    paymentService = TestBed.inject(PaymentService) as jest.Mocked<PaymentService>;
    toastService = TestBed.inject(ToastService) as jest.Mocked<ToastService>;
    loadingService = TestBed.inject(LoadingService) as jest.Mocked<LoadingService>;
    dialogRef = TestBed.inject(DynamicDialogRef) as jest.Mocked<DynamicDialogRef>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with amount from config', () => {
      expect(component.amount).toBe(100);
    });

    it('should have required properties', () => {
      expect(component.amount).toBeDefined();
      expect(component.cards).toBeUndefined();
      expect(component.selectedCard).toBeUndefined();
    });

    it('should have access to required services', () => {
      expect(component.config).toBeDefined();
      expect(component.ref).toBeDefined();
      expect(component.editingStateService).toBeDefined();
      expect(component.loadingService).toBeDefined();
    });
  });

  describe('ngOnInit', () => {
    it('should call loadCards on initialization', () => {
      paymentService.findCards.mockReturnValue(of(mockCards));

      component.ngOnInit();

      expect(paymentService.findCards).toHaveBeenCalled();
    });

    it('should handle loadCards error gracefully', () => {
      paymentService.findCards.mockReturnValue(throwError('Error loading cards'));

      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('loadCards Method', () => {
    beforeEach(() => {
      paymentService.findCards.mockReturnValue(of(mockCards));
    });

    it('should load cards and populate cards array', fakeAsync(() => {
      component.ngOnInit();
      tick();

      expect(component.cards).toBeDefined();
      expect(component.cards.length).toBe(3);
    }));

    it('should set preferred card as selected', fakeAsync(() => {
      component.ngOnInit();
      tick();

      expect(component.selectedCard).toEqual(mockCards[0]);
      expect(component.selectedCard.isPreferred).toBe(true);
    }));

    it('should format card labels correctly', fakeAsync(() => {
      component.ngOnInit();
      tick();

      const preferredCard = component.cards.find(card => card.value.isPreferred);
      expect(preferredCard.label).toContain('Visa');
      expect(preferredCard.label).toContain('(Preferred)');

      const expiredCard = component.cards.find(card => card.value.isExpired);
      expect(expiredCard.label).toContain('(Expired)');
    }));

    it('should handle empty cards array', fakeAsync(() => {
      paymentService.findCards.mockReturnValue(of([]));

      component.ngOnInit();
      tick();

      expect(component.cards).toEqual([]);
      expect(component.selectedCard).toBeUndefined();
    }));

    it('should handle cards without preferred card', fakeAsync(() => {
      const cardsWithoutPreferred = mockCards.map(card => ({ ...card, isPreferred: false }));
      paymentService.findCards.mockReturnValue(of(cardsWithoutPreferred));

      component.ngOnInit();
      tick();

      expect(component.selectedCard).toBeUndefined();
    }));

    it('should handle service error', () => {
      paymentService.findCards.mockReturnValue(throwError('Service error'));

      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('onCardChange Method', () => {
    it('should exist and be callable', () => {
      expect(component.onCardChange).toBeDefined();
      expect(typeof component.onCardChange).toBe('function');
    });

    it('should handle card change event', () => {
      const mockEvent = { value: mockCards[1] };

      expect(() => component.onCardChange(mockEvent)).not.toThrow();
    });

    it('should handle undefined card change', () => {
      expect(() => component.onCardChange(undefined)).not.toThrow();
    });

    it('should handle null card change', () => {
      expect(() => component.onCardChange(null)).not.toThrow();
    });
  });

  describe('pay Method', () => {
    beforeEach(() => {
      component.selectedCard = mockCards[0];
      component.amount = 100;
    });

    it('should not process payment when no card is selected', () => {
      component.selectedCard = undefined;

      component.pay();

      expect(paymentService.createPaymentIntent).not.toHaveBeenCalled();
      expect(paymentService.confirmPayment).not.toHaveBeenCalled();
    });

    it('should process payment when card is selected', fakeAsync(() => {
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      expect(paymentService.createPaymentIntent).toHaveBeenCalledWith(10000); // 100 * 100
      expect(paymentService.confirmPayment).toHaveBeenCalledWith('pi_test_client_secret', 'card1');
    }));

    it('should show success message and close dialog on successful payment', fakeAsync(() => {
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      expect(toastService.displaySuccess).toHaveBeenCalledWith('Payment succeeded!');
      expect(dialogRef.close).toHaveBeenCalled();
      expect(loadingService.setLoading).toHaveBeenCalledWith(false);
    }));

    it('should show error message on payment failure', fakeAsync(() => {
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: undefined,
        error: {
          message: 'Payment failed',
          type: 'card_error',
          code: 'card_declined'
        }
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      expect(toastService.displayError).toHaveBeenCalledWith('Payment failed!');
      expect(dialogRef.close).not.toHaveBeenCalled();
      expect(loadingService.setLoading).toHaveBeenCalledWith(false);
    }));

    it('should set loading state during payment process', fakeAsync(() => {
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      expect(loadingService.setLoading).toHaveBeenCalledWith(true);
      expect(loadingService.setLoading).toHaveBeenCalledWith(false);
    }));

    it('should handle createPaymentIntent error', fakeAsync(() => {
      paymentService.createPaymentIntent.mockReturnValue(throwError('Payment intent error'));

      expect(() => {
        component.pay();
        tick();
      }).toThrow('Payment intent error');

      expect(paymentService.confirmPayment).not.toHaveBeenCalled();
    }));

    it('should handle confirmPayment error', fakeAsync(() => {
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(throwError('Confirm payment error'));

      expect(() => {
        component.pay();
        tick();
      }).toThrow('Confirm payment error');

      expect(loadingService.setLoading).toHaveBeenCalledWith(true);
    }));

    it('should handle payment with different status', fakeAsync(() => {
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'requires_action',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      expect(toastService.displaySuccess).not.toHaveBeenCalled();
      expect(toastService.displayError).not.toHaveBeenCalled();
      expect(dialogRef.close).not.toHaveBeenCalled();
      expect(loadingService.setLoading).toHaveBeenCalledWith(false);
    }));

    it('should convert amount to cents correctly', fakeAsync(() => {
      component.amount = 50.75;
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 5075,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      expect(paymentService.createPaymentIntent).toHaveBeenCalledWith(5075); // 50.75 * 100
    }));
  });

  describe('Component Integration', () => {
    it('should initialize component with proper service dependencies', () => {
      expect(component.config).toBeDefined();
      expect(component.ref).toBeDefined();
      expect(component.editingStateService).toBeDefined();
      expect(component.loadingService).toBeDefined();
    });

    it('should handle complete payment flow without template rendering', fakeAsync(() => {
      // Setup
      paymentService.findCards.mockReturnValue(of(mockCards));
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };
      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      // Initialize component manually
      component.ngOnInit();
      tick();

      // Verify card is selected
      expect(component.selectedCard).toEqual(mockCards[0]);

      // Process payment
      component.pay();
      tick();

      // Verify payment flow
      expect(paymentService.createPaymentIntent).toHaveBeenCalledWith(10000);
      expect(paymentService.confirmPayment).toHaveBeenCalledWith('pi_test_client_secret', 'card1');
      expect(toastService.displaySuccess).toHaveBeenCalledWith('Payment succeeded!');
      expect(dialogRef.close).toHaveBeenCalled();
    }));

    it('should handle payment flow with manual card selection', fakeAsync(() => {
      paymentService.findCards.mockReturnValue(of(mockCards));
      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };
      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.ngOnInit();
      tick();

      // Manually select a different card
      component.selectedCard = mockCards[1];

      component.pay();
      tick();

      expect(paymentService.confirmPayment).toHaveBeenCalledWith('pi_test_client_secret', 'card2');
    }));
  });

  describe('Edge Cases', () => {
    it('should handle zero amount', () => {
      component.amount = 0;
      component.selectedCard = mockCards[0];

      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));

      component.pay();

      expect(paymentService.createPaymentIntent).toHaveBeenCalledWith(0);
    });

    it('should handle negative amount', () => {
      component.amount = -50;
      component.selectedCard = mockCards[0];

      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));

      component.pay();

      expect(paymentService.createPaymentIntent).toHaveBeenCalledWith(-5000);
    });

    it('should handle very large amount', () => {
      component.amount = 999999.99;
      component.selectedCard = mockCards[0];

      const mockPaymentIntentResponse = { client_secret: 'pi_test_client_secret' };
      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));

      component.pay();

      expect(paymentService.createPaymentIntent).toHaveBeenCalledWith(99999999);
    });

    it('should handle card with missing properties', fakeAsync(() => {
      const incompleteCard: IUserBillingInformation = {
        id: 'incomplete',
        cardNumber: undefined,
        brand: undefined,
        isPreferred: false,
        isExpired: false,
        cardholderName: undefined,
        expDate: undefined,
        country: undefined,
        state: undefined,
        city: undefined,
        addressLine1: undefined,
        zipCode: undefined
      };

      paymentService.findCards.mockReturnValue(of([incompleteCard]));

      // Test without template rendering
      component.ngOnInit();
      tick();

      expect(component.cards.length).toBe(1);
      // The label will be "undefined: null   " due to missing brand and cardNumber
      expect(component.cards[0].label).toContain('undefined');
    }));

    it('should handle payment intent response with different structure', fakeAsync(() => {
      component.selectedCard = mockCards[0];
      const mockPaymentIntentResponse = {
        different_key: 'pi_test_client_secret',
        another_key: 'other_value'
      };
      const mockConfirmPaymentResponse: PaymentIntentResult = {
        paymentIntent: {
          status: 'succeeded',
          id: 'pi_test_123',
          object: 'payment_intent',
          amount: 10000,
          currency: 'usd',
          created: Date.now(),
          livemode: false
        } as PaymentIntent,
        error: undefined
      };

      paymentService.createPaymentIntent.mockReturnValue(of(mockPaymentIntentResponse));
      paymentService.confirmPayment.mockReturnValue(of(mockConfirmPaymentResponse));

      component.pay();
      tick();

      // Should use the first key's value
      expect(paymentService.confirmPayment).toHaveBeenCalledWith('pi_test_client_secret', 'card1');
    }));

    it('should handle null payment intent response', () => {
      component.selectedCard = mockCards[0];
      paymentService.createPaymentIntent.mockReturnValue(of(null));

      // The component doesn't handle null responses gracefully
      // It will try to access Object.keys(null) which causes an error
      expect(() => {
        component.pay();
      }).not.toThrow(); // The error happens in the subscribe, not immediately

      expect(paymentService.confirmPayment).not.toHaveBeenCalled();
    });

    it('should handle undefined payment intent response', () => {
      component.selectedCard = mockCards[0];
      paymentService.createPaymentIntent.mockReturnValue(of(undefined));

      // Similar to null, undefined will cause Object.keys(undefined) to throw
      expect(() => {
        component.pay();
      }).not.toThrow(); // The error happens in the subscribe, not immediately

      expect(paymentService.confirmPayment).not.toHaveBeenCalled();
    });
  });

  describe('Template Integration', () => {
    it('should have component properties accessible', () => {
      expect(component.amount).toBeDefined();
      // cards and selectedCard are undefined initially until ngOnInit is called
      expect(component.cards).toBeUndefined();
      expect(component.selectedCard).toBeUndefined();
    });

    it('should handle amount property changes', () => {
      component.amount = 250;
      expect(component.amount).toBe(250);
    });

    it('should handle selectedCard property changes', () => {
      component.selectedCard = mockCards[0];
      expect(component.selectedCard).toEqual(mockCards[0]);
    });

    it('should handle cards property changes', () => {
      const testCards = [{ label: 'Test Card', value: mockCards[0] }];
      component.cards = testCards;
      expect(component.cards).toEqual(testCards);
    });

    it('should have pay method available', () => {
      expect(component.pay).toBeDefined();
      expect(typeof component.pay).toBe('function');
    });

    it('should have onCardChange method available', () => {
      expect(component.onCardChange).toBeDefined();
      expect(typeof component.onCardChange).toBe('function');
    });
  });

  describe('Service Dependencies', () => {
    it('should have access to all required services', () => {
      expect(component.config).toBeDefined();
      expect(component.ref).toBeDefined();
      expect(component.editingStateService).toBeDefined();
      expect(component.loadingService).toBeDefined();
    });

    it('should use TitleCasePipe for card brand formatting', fakeAsync(() => {
      paymentService.findCards.mockReturnValue(of(mockCards));

      component.ngOnInit();
      tick();

      const visaCard = component.cards.find(card => card.value.brand === 'visa');
      expect(visaCard.label).toContain('Visa'); // Should be title case
    }));

    it('should handle editing state service', () => {
      expect(component.editingStateService.editingState.isEditing).toBe(false);
    });

    it('should handle loading service calls', () => {
      expect(component.loadingService.setLoading).toBeDefined();
    });
  });

  describe('Memory Management', () => {
    it('should not have memory leaks with multiple card loads', fakeAsync(() => {
      // Load cards multiple times
      for (let i = 0; i < 5; i++) {
        paymentService.findCards.mockReturnValue(of(mockCards));
        component.ngOnInit();
        tick();
      }

      expect(component.cards.length).toBe(3);
      expect(paymentService.findCards).toHaveBeenCalledTimes(5);
    }));

    it('should handle component destruction gracefully', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });

  describe('Configuration Handling', () => {
    it('should handle different amount configurations', () => {
      const newConfig = { data: { amount: 250.50 } };
      const newComponent = new PaymentFormComponent(
        TestBed.inject(FormBuilder),
        TestBed.inject(StripeService),
        TestBed.inject(PaymentService),
        newConfig as any,
        TestBed.inject(ToastService),
        TestBed.inject(DialogService),
        TestBed.inject(DynamicDialogRef),
        TestBed.inject(EditingStateService),
        TestBed.inject(TitleCasePipe),
        TestBed.inject(LoadingService)
      );

      expect(newComponent.amount).toBe(250.50);
    });

    it('should handle missing amount in config', () => {
      const newConfig = { data: {} };
      const newComponent = new PaymentFormComponent(
        TestBed.inject(FormBuilder),
        TestBed.inject(StripeService),
        TestBed.inject(PaymentService),
        newConfig as any,
        TestBed.inject(ToastService),
        TestBed.inject(DialogService),
        TestBed.inject(DynamicDialogRef),
        TestBed.inject(EditingStateService),
        TestBed.inject(TitleCasePipe),
        TestBed.inject(LoadingService)
      );

      expect(newComponent.amount).toBeUndefined();
    });

    it('should handle null config data', () => {
      const newConfig = { data: null };

      expect(() => {
        new PaymentFormComponent(
          TestBed.inject(FormBuilder),
          TestBed.inject(StripeService),
          TestBed.inject(PaymentService),
          newConfig as any,
          TestBed.inject(ToastService),
          TestBed.inject(DialogService),
          TestBed.inject(DynamicDialogRef),
          TestBed.inject(EditingStateService),
          TestBed.inject(TitleCasePipe),
          TestBed.inject(LoadingService)
        );
      }).toThrow();
    });
  });
});
