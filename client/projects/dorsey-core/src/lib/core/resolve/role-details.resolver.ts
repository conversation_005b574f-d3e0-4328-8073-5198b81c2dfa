import { Injectable } from '@angular/core';
import { Router, RouterStateSnapshot, ActivatedRouteSnapshot } from '@angular/router';
import { catchError, EMPTY, Observable, of, switchMap } from 'rxjs';
import { RoleService } from '../services/admin/role.service';
import { ToastService } from '../services/toast.service';

@Injectable()
export class RoleDetailsResolver  {
  readonly ERROR_MSG = 'Role Id was not found.';

  constructor(
    private roleService: RoleService,
    private toastService: ToastService
  ) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<any> {
    return this.roleService.findRole(route.queryParamMap.get('roleId')).pipe(
      switchMap((resp: any) => {
        if (!resp) {
          this.toastService.displayError(this.ERROR_MSG);
          return EMPTY;
        }
        return of(resp);
      }),
      catchError((error) => {
        this.toastService.displayError(this.ERROR_MSG);
        return EMPTY;
      })
    );
  }
}
