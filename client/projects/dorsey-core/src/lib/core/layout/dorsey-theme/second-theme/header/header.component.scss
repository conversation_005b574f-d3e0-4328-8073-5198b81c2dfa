@import 'variables';

$t1-logo-container-width: 150px;
$t1-header-separation: 4px;

$t2-logo-container-width: 160px;
$t2-header-separation: 1px;

h2 {
  align-self: center;
  color: #0c5394;
  margin-left: 20px;
  font-size: xx-large;
}

:host
{
  ::ng-deep {

    .p-breadcrumb {
      background: transparent !important;
      border: none !important;
      border-radius: 0 !important;
      padding: 6px 0 0 0 !important;
    }

    #template {
      fa-icon {
        font-size: 24px;
        color: $secondary-color;
      }

      p-breadcrumb > div.p-breadcrumb {
        background: $primary-color;
        border: none;
        border-radius: 3px;
        padding: 3px;
      }

      button.p-button {
        color: $secondary-color !important;
        background-color: #F4F4F4 !important;
        border: #F4F4F4 solid 1px !important;
      }

      span.fa-user {
        font-size: 30px !important;
      }
    }

    fa-icon:hover {
      cursor: pointer;
    }

    p-breadcrumb {
      padding-left: 10px;
    }

    p-breadcrumb span {
      color: $secondary-font-color !important;
    }

    p-breadcrumb span.p-menuitem-icon {
      margin: 0 4px 0 4px;
    }

    button > span.pi-user {
      font-size: 18px !important;
    }

    button[icon="pi pi-chevron-down"] {
      width: 20px !important;
      padding: 0 !important;
      > span {
        font-size: 13px !important;
      }
    }

    button:not([icon]) {
      width: 24px !important;
    }
    button:not([icon]):focus {
      box-shadow: none !important;
    }

    p-tieredmenu > .p-tieredmenu {
      width: fit-content !important;
      left: -58px !important;
    }
  }
}

#template {
  .logo-container-back {
    width: $t2-logo-container-width;
    height: calc(#{$t2-header-height} - #{$t2-header-separation});
    background-color: $quaternary-color;
    border-bottom: calc(#{$t2-header-height} * 0.24) solid $secondary-color;
  }

  .logo-container-front {
    background-color: $primary-color;
    width: $t2-logo-container-width;
    height: $t2-header-height;
    border-top-left-radius: 25px;
    border-right: round($t2-header-separation) solid $quaternary-color;
    border-bottom: round($t2-header-separation) solid $quaternary-color;
  }

  .logo {
    height: 100%;
    width: 100%;
    object-fit: contain;
    padding: 10px 6px 10px 6px
  }

  .header-title {
    display: flex;
    width: 100%;
    height: $t2-header-height;
    border-bottom: $t2-header-separation solid $quaternary-color;
    border-top-right-radius: 25px;
    background-color: #F4F4F4;
    justify-content: space-between;
  }

  .header-title-separator {
    position: absolute;
    display: flex;
    width: 100%;
    background-color: $secondary-color;
    height: calc(#{$t2-header-height} * 0.25);
  }

  .header-breadcrumb {
    position: absolute;
    display: flex;
    width: calc(100% - 120px) ;
    height: calc(#{$t2-header-height} * 0.26);
    background-color: $primary-color;
    margin: 72px 0 1px 20px;
    border-radius: 8px;
  }

  h2 {
    color: $secondary-color;
  }

  .letter-container {
    margin: -5px 42px 28px 0;
  }
}

.v-center {
  display: flex;
  align-items: center;
}

.header-title-container {
  position: relative;
  display: flex;
  min-width: fit-content;
  width: 100%;
  background-color: $quaternary-color;
  flex-direction: column;
}

.letter {
  position: absolute;
  font-size: 26px;
}

.library {
  position: absolute;
  font-size: 26px;
  right: 145px;
}

.notification-container {
  font-size: 12px;
  position: absolute;
  margin: 20px 1px 1px 17px;
  background-color: $background-red;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 8px;
  color: #ffffff;
}
