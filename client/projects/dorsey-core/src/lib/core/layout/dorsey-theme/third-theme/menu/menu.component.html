<div
  [@openClose]="isOpen ? 'open' : 'closed'"
  (@openClose.start)="onAnimationStart($event)"
  (@openClose.done)="onAnimationEnd($event)"
  [class.opened]="isOpen" class="menu-container">
  <div class="sticky">
    <ng-template #menuBranch let-menuItems="items" let-isRoot="isRoot">
      <ngb-accordion [activeIds]="['UNIQ_ID']" #acc="ngbAccordion" [ngClass]="{'closed': !isOpen, 'inner-accordion': !isRoot}" (panelChange)="onPanelChange($event)">
        <ng-container *ngFor="let rootItem of menuItems">
          <ngb-panel id="UNIQ_ID" *hasAnyRole="actions.VIEW;path:rootItem.path">
            <ng-template ngbPanelTitle>
              <div [ngClass]="{'option-closed': !isRoot && !isOpen }">
<!--                <div class="icon-container"><i [class]="rootItem.icon"></i></div>-->
                <span class="main-title" [ngClass]="{'font-weight-bold': !isRoot}">{{rootItem.name}}:</span>
              </div>
            </ng-template>
            <ng-template ngbPanelContent>
              <ng-container *ngIf="rootItem.children?.length">
                <ng-container *ngFor="let menuItem of rootItem.children">
                  <ng-container *ngIf="!menuItem.children?.length">
                    <div class="option-container">
                      <div
                        *hasAnyRole="actions.VIEW;path:menuItem.path"
                        [ngClass]="{'option-closed': !isOpen }"
                        class="option"
                        (click)="onSelectOption($event)"
                        [routerLink]="menuItem.path"
                        routerLinkActive="active-link"
                      >
                        <div class="icon-container" [ngClass]="{ selected: headerService.selectedMenuOption?.includes(menuItem.path)}"><i [class]="menuItem.icon"></i></div>
                        <span class="font-weight-bold">{{menuItem.name}}</span>
                      </div>
                    </div>
                  </ng-container>
                  <ng-container *ngIf="menuItem.children?.length">
                    <ng-container *ngTemplateOutlet="menuBranch; context:{items: [menuItem], isRoot: false}"></ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>
            </ng-template>
          </ngb-panel>
        </ng-container>
      </ngb-accordion>
    </ng-template>
    <ng-container *ngTemplateOutlet="menuBranch; context:{items: menuItems, isRoot: true}"></ng-container>
  </div>
</div>



