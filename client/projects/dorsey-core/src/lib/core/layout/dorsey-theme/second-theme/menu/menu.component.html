<div
  [@openClose]="isOpen ? 'open' : 'closed'"
  (@openClose.start)="onAnimationStart($event)"
  (@openClose.done)="onAnimationEnd($event)"
  [class.opened]="isOpen"
  class="menu-container"
>
  <div class="sticky">
    <ng-template #menuBranch2 let-menuItems="items" let-isRoot="isRoot">
      <div
        ngbAccordion
        [ngClass]="{ closed: !isOpen, 'inner-accordion': !isRoot }"
      >
        <ng-container *ngFor="let rootItem of menuItems">
          <div ngbAccordionItem *hasAnyRole="actions.VIEW; path: rootItem.path">
            <div ngbAccordionHeader>
              <button ngbAccordionButton>
                <div
                  [ngClass]="{
                    option: !isRoot && !isOpen,
                    'option-closed': !isRoot && !isOpen
                  }"
                >
                  <i [class]="rootItem.icon"></i>
                  <span [ngClass]="{ 'font-weight-bold': !isRoot }">{{
                    rootItem.name
                  }}</span>
                </div>
              </button>
            </div>
            <div ngbAccordionCollapse>
              <div ngbAccordionBody>
                <ng-template>
                  <ng-container *ngIf="rootItem.children?.length">
                    <ng-container *ngFor="let menuItem of rootItem.children">
                      <ng-container *ngIf="!menuItem.children?.length">
                        <div
                          *hasAnyRole="actions.VIEW; path: menuItem.path"
                          [ngClass]="{
                            selected:
                              headerService.selectedMenuOption?.includes(
                                menuItem.path
                              ),
                            'option-closed': !isOpen
                          }"
                          class="option"
                          (click)="onSelectOption($event)"
                          [routerLink]="menuItem.path"
                          routerLinkActive="active-link"
                        >
                          <i [class]="menuItem.icon"></i>
                          <span class="font-weight-bold">{{
                            menuItem.name
                          }}</span>
                        </div>
                      </ng-container>
                      <ng-container *ngIf="menuItem.children?.length">
                        <ng-container
                          *ngTemplateOutlet="
                            menuBranch2;
                            context: {
                              items: [menuItem],
                              isRoot: false
                            }
                          "
                        ></ng-container>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-template>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-template>
    <ng-container
      *ngTemplateOutlet="
        menuBranch2;
        context: { items: menuItems, isRoot: true }
      "
    ></ng-container>
  </div>
  <div class="menu-separator"></div>
</div>
