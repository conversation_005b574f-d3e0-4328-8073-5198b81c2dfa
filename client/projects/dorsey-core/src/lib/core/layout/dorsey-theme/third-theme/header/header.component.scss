@import 'variables';
@import '../third-theme-variables';

//$header-height: 100px;
$t1-logo-container-width: 150px;
$t1-header-separation: 4px;

$t2-logo-container-width: 160px;
$t2-header-separation: 1px;

h3 {
  align-self: center;
  color: #0c5394;
  margin-left: 20px;
  //font-size: xx-large;
}

:host
{
  #header {
    //border-bottom: $t1-header-separation solid $quaternary-color;
  }

  ::ng-deep {

    .p-breadcrumb {
      background: transparent !important;
      border: none !important;
      border-radius: 0 !important;
      padding: 6px 0 0 0 !important;
    }

    #header {
      fa-icon {
        font-size: 24px;
      }

      p-breadcrumb > div.p-breadcrumb {
        background: #eeeeee;
        border: none;
        border-radius: 3px;
        padding: 3px;
      }

      button.p-button {
        color: #495057 !important;
      }

      span.fa-user {
        font-size: 24px !important;
      }
    }

    fa-icon:hover {
      cursor: pointer;
    }

    p-breadcrumb {
      padding-left: 25px;
    }

    p-breadcrumb span {
      color: #6A9DCD !important;
    }

    p-breadcrumb span.p-menuitem-icon {
      margin: 0 4px 0 4px;
    }

    button > span.pi-user {
      font-size: 18px !important;
    }

    button[icon="pi pi-chevron-down"] {
      width: 20px !important;
      padding: 0 !important;
      > span {
        font-size: 13px !important;
      }
    }

    button:not([icon]):not(.p-speeddial-button) {
      width: 24px !important;
    }
    button:not([icon]):focus {
      box-shadow: none !important;
    }

    p-tieredmenu > .p-tieredmenu {
      width: fit-content !important;
      left: -58px !important;
    }

    .p-speeddial-button {
      width: 2em !important;
      height: 2em !important;
      border: none;
      color: #495057 !important;
    }

    p-speeddial .p-speeddial {
      position: relative !important;
    }

    p-divider {
      z-index: 1002;
    }
    .p-divider.p-divider-horizontal {
      margin: 0;
      padding: 0;
    }
  }
}

#header {
  .logo-container-back {
    width: $menu-logo-containers-width;
    min-height: $header-height;
    background-color: $tertiary-color;
    box-shadow: 6px 5px 5px #d6d6d6;
    z-index: 1001;
    display: flex;
    align-items: center;
  }

  .logo-container-front {
    background-color: $tertiary-color;
    width: $menu-logo-containers-width;
    height: calc(#{$header-height} - 20px);
    //border-right: round($t1-header-separation) solid $quaternary-color;
    //border-bottom: round($t1-header-separation) solid $quaternary-color;
  }

  .logo {
    height: 100%;
    width: 100%;
    object-fit: contain;
    padding: 10px 6px 10px 6px
  }

  .header-title {
    display: flex;
    width: 100%;
    height: calc(#{$header-height} * 0.60);
    border-top-right-radius: 25px;
    justify-content: space-between;
  }

  .header-title-separator {
    display: flex;
    width: 100%;
    height: calc(#{$header-height} * 0.40);
  }

  .header-breadcrumb {
    position: absolute;
    width: calc(100% - #{$t1-logo-container-width}) ;
    height: 32px;
    background-color: $secondary-color;
    margin: 75px 60px 1px 60px;
    border-radius: 2px;
  }

  h3 {
    color: #756377;
  }

  .letter-container {
    margin: -12px 36px 28px 0;
  }
}

.v-center {
  display: flex;
  align-items: center;
}

.header-title-container {
  position: relative;
  display: flex;
  min-width: fit-content;
  width: 100%;
  background-color: #EEEEEE;
  flex-direction: column;
}

.letter {
  position: absolute;
  font-size: 26px;
}

.library {
  position: absolute;
  font-size: 26px;
  right: 145px;
}

.notification-container {
  font-size: 12px;
  position: absolute;
  margin: 20px 1px 1px 17px;
  background-color: $background-red;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 8px;
  color: #ffffff;
}

p-speedDial {
  z-index: 1001;
}

i {
  font-size: 24px;
  margin-top: 7px;
  color: #495057;
}

i:not(.fa-user-circle-o) {
  cursor: pointer;
}

input[pinputtext] {
  border-top: none;
  border-left: none;
  border-right: none;
  background-color: #EEEEEE;
}

input[pinputtext]:focus {
  box-shadow: 0 4px 6px #a6d5fa;
}


