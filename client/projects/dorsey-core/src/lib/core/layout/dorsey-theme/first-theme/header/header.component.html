<div id="template" class="d-flex w-100 h-100">
  <div class="d-flex">
    <div class="logo-container-back">
      <div class="logo-container-front">
        <img
          class='logo'
          [src]="headerService.logoUrl"
          alt="cms-logo"
        />
      </div>
    </div>
  </div>
  <div class="header-title-container">
    <div class="header-title">
      <h2>{{headerService.title}}</h2>
      <div class="pull-right text-end v-center w-auto me-5">
        <div class="letter-container">
          <fa-icon (click)="router.navigate(['home', 'dashboard'])" [icon]="'envelope'" class="letter" ></fa-icon>
          <div class="notification-container">
            <span>{{ notificationsCount ? notificationsCount : ''}}</span>
          </div>
        </div>
        <fa-icon (click)="router.navigate(['library'])" [icon]="'book'" class="library" ></fa-icon>
        <p-splitButton icon="fa fa-user" [model]="profileItems" styleClass="p-button-text"></p-splitButton>
      </div>
    </div>
    <div class="header-title-separator">
      <p-breadcrumb [model]="headerService.breadCrumbItems"></p-breadcrumb>
    </div>
  </div>
</div>
