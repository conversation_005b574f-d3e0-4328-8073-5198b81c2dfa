<div id="header" class="d-flex w-100">
  <div class="d-flex flex-column">
    <div class="logo-container-back">
      <div class="logo-container-front">
        <img
          class='logo'
          [src]="headerService.logoUrl"
          alt="cms-logo"
        />
      </div>
    </div>
    <p-divider></p-divider>
  </div>
  <div class="header-title-container">
    <div class="header-title">
      <h3>{{headerService.title}}</h3>
      <div class="pull-right text-end v-center w-auto me-5 align-items-start mt-3">
        <input pInputText id="search" [(ngModel)]="search" class="me-2" />
        <i class="fa fa-search me-5" aria-hidden="true"></i>
<!--        <div class="letter-container">-->
<!--          <fa-icon (click)="router.navigate(['home', 'dashboard'])" [icon]="'envelope'" class="letter" ></fa-icon>-->
<!--          <div class="notification-container">-->
<!--            <span>{{ notificationsCount ? notificationsCount : ''}}</span>-->
<!--          </div>-->
<!--        </div>-->
<!--        <p-splitButton icon="fa fa-bars" [model]="profileItems" styleClass="p-button-text"></p-splitButton>-->
        <i class="fa fa-user-circle-o me-1" aria-hidden="true"></i>
        <span class="mt-2 me-3">{{userDataService?.userData?.firstName}} {{userDataService?.userData?.lastName}}</span>
        <p-speedDial
          class="mt-1 me-2"
          [model]="profileItems"
          direction="down"
          [transitionDelay]="80"
          showIcon="pi pi-bars"
          hideIcon="pi pi-times"
          buttonClassName="p-button-outlined" />
<!--        <fa-icon (click)="router.navigate(['library'])" [icon]="'book'" class="library" ></fa-icon>-->
        <fa-icon (click)="logout()" [icon]="'sign-out'" [ngbTooltip]="'Sign out'" [placement]="'bottom'"></fa-icon>
      </div>
    </div>
    <div class="header-title-separator">
      <p-breadcrumb [model]="headerService.breadCrumbItems"></p-breadcrumb>
    </div>
  </div>
</div>
