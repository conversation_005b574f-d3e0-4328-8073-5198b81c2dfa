@import 'variables';

#main-container {
  margin: auto;
  max-width: 1920px;
  min-width: calc(1366px - (#{$main-div-border-width} * 2));
  background: $main-content-background;
  border-bottom-right-radius: 25px;
}

dorsey-header {
  background-color: transparent;
  display: flex;
}

.t2-header {
  height: $t2-header-height;
}


#main-content {
  display: flex;
  height: 100%;
  border-bottom-right-radius: 25px;
}

.t2-main-content {
  min-height: calc(100vh - #{$t2-header-height});
}


#menu-container {
  width: 57px; // prevent pushing content
  background-color: $quaternary-color;
}

#content {
  display: flex;
  flex-direction: column;
  flex-grow: 0.99;
  padding: 15px;
  z-index: 1000;
  border-bottom-right-radius: 25px;

}

app-footer {
  height: 50px;
  display: flex;
  background-color: red;
}

.overlay {
  position: fixed; /* follow the viewport position */
  width: 100%; /* Full width (cover the whole page) */
  height: 100%; /* Full height (cover the whole page) */
  left: 50%;
  top: 50%;
  right: 0;
  bottom: 0;
  background-color: #efefef; /* Black background with opacity */
  -webkit-transform: translate(-50%, -50%);
  opacity: 0.5;
  z-index: 10000; /* Specify a stack order in case you're using a different order for other elements */
  filter: alpha(opacity=50);
}

.body-root {
  border: solid $quaternary-color $main-div-border-width;
  background-color: $quaternary-color;
}

:host::ng-deep {
  .p-dialog-content {
    overflow: visible;
  }
}
