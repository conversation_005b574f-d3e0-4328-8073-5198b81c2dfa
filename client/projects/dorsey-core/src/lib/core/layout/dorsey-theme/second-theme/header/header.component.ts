import { Component, Inject, Input, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, Scroll } from '@angular/router';
import { AccountService } from '../../../../auth/account.service';
import { <PERSON><PERSON>anitizer, Title } from '@angular/platform-browser';
import { MenuItem } from 'primeng/api';
import { WebSocketShareService } from '../../../../services/notifications/web-socket.service';
import { NotificationService } from '../../../../services/notifications/notification.service';
import { DorseyConfiguration } from '../../../../models/configuration';
import { UploadedDataService } from '../../../../services/admin/uploaded-data.service';
import { HeaderService } from '../../../../services/admin/header.service';
import { AbstractHeader } from '../../common/abstract-header';

@Component({
  selector: 'dorsey-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements AbstractHeader, OnInit {
  @Input() skin: number;

  title: string;
  userInfo: any;
  items: MenuItem[];
  profileItems: MenuItem[];
  notificationsCount = 0;
  logoUrl: string;

  constructor(
    public router: Router,
    private accountService: AccountService,
    private titleService: Title,
    private websocketService: WebSocketShareService,
    private notificationService: NotificationService,
    private uploadedDataService: UploadedDataService,
    @Inject('config') private config: DorseyConfiguration,
    public route: ActivatedRoute,
    public headerService: HeaderService
  ) {
    this.profileItems = [
      {
        label: 'Profile',
        icon: 'pi pi-user-edit',
        command: () => this.goProfile(),
      },
      {
        label: 'logout',
        icon: 'pi pi-power-off',
        command: () =>
          (window.location.href = `${config.environment.backendUrl}/api/logout`),
      },
    ];
    this.websocketService.getNewValue().subscribe(() => {
      this.loadNotificationCount();
    });
  }

  ngOnInit(): void {
    this.loadLogo();
  }

  loadNotificationCount() {
    this.notificationService
      .findUserNotifications()
      .subscribe((resp) => (this.notificationsCount = resp.length));
  }

  loadLogo() {
    this.uploadedDataService.downloadFile('LOGO').subscribe((resp) => {
      this.headerService.setLogo(resp);
    });
  }

  loadBreadcrumb(event: any) {
    this.headerService.title = this.titleService.getTitle();
    const decodedUrl = decodeURIComponent(event.url);
    const paths = decodedUrl
      .substring(
        0,
        decodedUrl.indexOf('?') !== -1
          ? decodedUrl.indexOf('?')
          : decodedUrl.length
      )
      .split('/');
    paths.shift();

    const rootPath = paths.shift();
    let pathParent = this.router.config.find(
      (r) => r?.data && r.path === rootPath
    );

    if (pathParent) {
      this.headerService.breadCrumbItems = [pathParent.data?.['breadcrumb']];
      return;
    }

    let pathChildren = this.router.config.find(
      (r) => r?.data?.['parentPath'] === rootPath
    )?.children;
    let prevPath = rootPath;
    let path: any = {};

    const items: MenuItem[] = [];
    let prevItems: MenuItem[];

    if (this.headerService.breadCrumbItems?.length) {
      prevItems = JSON.parse(
        JSON.stringify(this.headerService.breadCrumbItems)
      );
    }

    paths.forEach((p) => {
      if (pathChildren?.length) {
        if (pathChildren.some((pc) => [...pc.path].some((l) => l === ':'))) {
          path = JSON.parse(
            JSON.stringify(
              pathChildren.find((pc) => [...pc.path].some((l) => l === ':'))
            )
          );
          path.data['breadcrumb'].label += decodeURI(p);
        } else {
          path = JSON.parse(
            JSON.stringify(
              pathChildren.find(
                (pc) => pc.path.replace(':', '') === p.split('?')[0]
              ) ?? {}
            )
          );
        }

        prevPath = prevPath.concat(`/${p}`);

        if (path?.data?.['breadcrumb']) {
          path.data['breadcrumb'].routerLink = prevPath;
          items.push(path.data['breadcrumb']);
        }

        pathChildren = path.children;
      }
    });

    if (Object.keys(this.route.snapshot.queryParams).length) {
      items[items.length - 1].queryParams = {
        ...this.route.snapshot.queryParams,
      };
    }

    if (prevItems?.length) {
      items.forEach((v) => {
        const curr = prevItems.find((pv) => pv.label === v.label);

        if (curr && Object.keys(curr).some((v) => v === 'queryParams')) {
          v.queryParams = curr.queryParams;
        }
      });
    }

    this.headerService.breadCrumbItems = items;
  }

  goProfile() {
    this.router.navigate(['user', 'profile']);
  }
}
