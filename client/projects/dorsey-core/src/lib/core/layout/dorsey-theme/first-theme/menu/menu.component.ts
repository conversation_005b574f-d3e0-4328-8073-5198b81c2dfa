import {
  animate,
  AnimationEvent,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  AfterViewInit,
  Component,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MenuItem } from '../../../../models/menu-item';
import { RoleActions } from '../../../../models/enums/role-actions';
import { AbstractMenu } from '../../common/abstract-menu';
import { HeaderService } from '../../../../services/admin/header.service';

@Component({
  selector: 'dorsey-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss'],
  animations: [
    trigger('openClose', [
      state('open', style({})),
      state(
        'closed',
        style({
          width: '57px', // Should match with $menu-width var of variables.css
        })
      ),
      transition('open <=> closed', [animate('0.3s')]),
    ]),
  ],
})
export class Menu1Component implements AbstractMenu, OnInit, AfterViewInit {
  @Input() menuItems: MenuItem[];

  isOpen = false;
  selectedOption: string;
  actions = RoleActions;

  constructor(public headerService: HeaderService) {}

  ngOnInit(): void {
    this.onPathChange(location.pathname);
  }

  ngAfterViewInit() {}

  onAnimationStart(event: AnimationEvent) {}

  onAnimationEnd(event: AnimationEvent) {}

  onSelectOption(event) {}

  onPathChange(path: string) {
    this.headerService.selectedMenuOption = path;
  }
}
