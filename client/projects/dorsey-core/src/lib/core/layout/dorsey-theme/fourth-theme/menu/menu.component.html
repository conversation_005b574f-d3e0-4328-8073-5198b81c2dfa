<div class="menu-container">
  <div class="sticky">
    <div class="flex justify-content-center search-container mt-2">
      <span class="p-input-icon-left">
        <i class="pi pi-search"></i>
        <input type="text" pInputText [(ngModel)]="search" />
      </span>
    </div>
    <p-divider></p-divider>
    <ng-template #menuBranch4 let-menuItems="items" let-isRoot="isRoot">
      <div
        ngbAccordion
        [ngClass]="{ 'inner-accordion': !isRoot }"
        [closeOthers]="false"
      >
        <ng-container *ngFor="let rootItem of menuItems; let i = index">
          <div ngbAccordionItem *hasAnyRole="actions.VIEW; path: rootItem.path">
            <div ngbAccordionHeader>
              <button ngbAccordionButton>
                <div>
                  <i [class]="rootItem.icon"></i>
                  <span [ngClass]="{ 'font-weight-bold': !isRoot }">{{
                    rootItem.name
                  }}</span>
                </div>
              </button>
            </div>
            <div ngbAccordionCollapse>
              <div ngbAccordionBody>
                <ng-container *ngIf="rootItem.children?.length">
                  <ng-container *ngFor="let menuItem of rootItem.children">
                    <ng-container *ngIf="!menuItem.children?.length">
                      <div
                        *hasAnyRole="actions.VIEW; path: menuItem.path"
                        [ngClass]="{
                          selected: headerService.selectedMenuOption?.includes(
                            menuItem.path
                          )
                        }"
                        class="option"
                        (click)="onSelectOption($event)"
                        [routerLink]="menuItem.path"
                        routerLinkActive="active-link"
                      >
                        <i [class]="menuItem.icon"></i>
                        <span class="font-weight-bold">{{
                          menuItem.name
                        }}</span>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="menuItem.children?.length">
                      <ng-container
                        *ngTemplateOutlet="
                          menuBranch4;
                          context: { items: [menuItem], isRoot: false }
                        "
                      ></ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-template>
    <ng-container
      *ngTemplateOutlet="
        menuBranch4;
        context: { items: menuItems, isRoot: true }
      "
    ></ng-container>
  </div>
</div>
