import { AfterViewChecked, Component, Input, ViewChild } from '@angular/core';
import { MenuItem } from '../../../models/menu-item';
import { HeaderComponent } from './header/header.component';
import { Menu1Component } from './menu/menu.component';
import { HeaderService } from '../../../services/admin/header.service';
import { AbstractTheme } from '../common/abstract-theme';

@Component({
  selector: 'dorsey-first-theme',
  templateUrl: './first-theme.component.html',
  styleUrls: ['./first-theme.component.scss'],
})
export class FirstThemeComponent implements AbstractTheme, AfterViewChecked {
  @Input() menuItems: MenuItem[];
  @ViewChild('header') header: HeaderComponent;
  @ViewChild('menu') menu: Menu1Component;

  display = false;
  initialRoute;

  constructor(public headerService: HeaderService) {}

  ngOnInit(): void {}

  ngAfterViewChecked(): void {
    if (this.display && this.initialRoute) {
      this.header.loadBreadcrumb(this.initialRoute);
      this.initialRoute = null;
    }
  }
}
