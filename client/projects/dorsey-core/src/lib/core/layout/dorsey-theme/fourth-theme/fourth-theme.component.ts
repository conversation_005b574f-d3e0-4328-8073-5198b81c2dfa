import { Component, Input, ViewChild } from '@angular/core';
import { MenuItem } from '../../../models/menu-item';
import { HeaderComponent } from './header/header.component';
import { Menu4Component } from './menu/menu.component';
import { HeaderService } from '../../../services/admin/header.service';

@Component({
  selector: 'dorsey-fourth-theme',
  templateUrl: './fourth-theme.component.html',
  styleUrls: ['./fourth-theme.component.scss'],
})
export class FourthThemeComponent {
  @Input() menuItems: MenuItem[];
  @ViewChild('header') header: HeaderComponent;
  @ViewChild('menu') menu: Menu4Component;

  display = false;
  initialRoute;

  constructor(public headerService: HeaderService) {}

  ngOnInit(): void {}

  ngAfterViewChecked(): void {
    if (this.display && this.initialRoute) {
      this.header.loadBreadcrumb(this.initialRoute);
      this.initialRoute = null;
    }
  }
}
