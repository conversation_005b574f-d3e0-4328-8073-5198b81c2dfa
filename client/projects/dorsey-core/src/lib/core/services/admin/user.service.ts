import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseRestService } from '../base-rest.service';
import { IUser, User } from '../../models/admin/users/user.model';
import { IUploadedData } from '../../models/admin/system/uploaded-data.model';
import { HttpHeaders } from '@angular/common/http';

@Injectable()
export class UserService extends BaseRestService {
  findLoggedUserInfo(): Observable<IUser> {
    return this.get(`users/me`);
  }
  findAll(): Observable<any> {
    return this.get(`users`);
  }
  findUser(): Observable<any> {
    return this.get(`users/profile`);
  }
  updateUsers(users: User[]): Observable<any> {
    return this.put(`users`, users);
  }
  updateUser(user: User, image: File): Observable<any> {
    const fd = new FormData();
    fd.append('data', JSON.stringify(user));
    fd.append('file', image);
    return this.httpClient.put(this.baseURL + `/users/single`, fd, {
      headers: new HttpHeaders(),
    });
  }
}
