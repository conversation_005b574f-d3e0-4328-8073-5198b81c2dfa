import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseRestService } from '../base-rest.service';
import { ISLA } from '../../models/admin/system/sla.model';

@Injectable()
export class SystemService extends BaseRestService {
  findConfig(): Observable<any> {
    return this.get(`system`);
  }

  findSystemFile(fileName: string): Observable<any> {
    return this.get(`system/files/${fileName}`);
  }

  updateConfig(config: any): Observable<any> {
    return this.put(`system`, config);
  }

  findSla(): Observable<any> {
    return this.get(`admin/sla`);
  }

  updateSla(slas: ISLA[]): Observable<any> {
    return this.put(`admin/sla`, slas);
  }
}
