import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpHeaders } from '@angular/common/http';
import { BaseRestService } from '../base-rest.service';
import { IUploadedData } from '../../models/admin/system/uploaded-data.model';

@Injectable()
export class UploadedDataService extends BaseRestService {
  findAll(): Observable<any> {
    return this.get(`coreUploadedData`);
  }

  saveFile(
    file: File,
    uploadedData: IUploadedData,
    path: string
  ): Observable<any> {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('data', JSON.stringify(uploadedData));
    return this.httpClient.post(`${this.baseURL}/${path}`, fd, {
      headers: new HttpHeaders(),
    });
  }

  downloadFile(id: string): Observable<Blob> {
    return this.httpClient.get(
      this.baseURL + `/coreUploadedData/download/${id}`,
      {
        responseType: 'blob',
      }
    );
  }
}
