import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { BaseRestService } from '../base-rest.service';
import { AuditLogFilter } from '../../models/admin/audit-log/audit-log-filter.model';
import { AuditLog } from '../../models/admin/audit-log/audit-log.model';

@Injectable()
export class AuditLogService extends BaseRestService {
  findFilter(): Observable<AuditLogFilter> {
    return this.get(`auditlog/filter`);
  }

  findAuditLogs(
    userEmail: string,
    action: string,
    entityType: string,
    startDate: string,
    endDate: string
  ): Observable<AuditLog[]> {
    let params = new HttpParams();
    if (startDate) {
      params = params.append('startDate', startDate);
    }
    if (endDate) {
      params = params.append('endDate', endDate);
    }
    return this.get(
      `auditlog/${userEmail}/${action}/${entityType}`,
      params
    ) as Observable<AuditLog[]>;
  }
}
