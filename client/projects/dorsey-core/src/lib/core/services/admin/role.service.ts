import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseRestService } from '../base-rest.service';
import { IRole } from '../../models/admin/roles/role.model';

@Injectable()
export class RoleService extends BaseRestService {
  findAll(): Observable<any> {
    return this.get(`roles`);
  }

  findRole(roleId: string): Observable<any> {
    return this.get(`roles/${roleId}`);
  }

  updateRole(roles: IRole[]): Observable<any> {
    return this.put(`roles`, roles);
  }

  updateCapabilities(role: IRole): Observable<any> {
    return this.put(`roles/capabilities`, role);
  }

  checkRolesExistence(): Observable<any> {
    return this.get(`roles/checkRoles`);
  }

  checkRolesExistenceByNames(names: string[]): Observable<any> {
    return this.post(`roles/checkRoles`, names);
  }
}
