import Stomp from 'stompjs';
import SockJ<PERSON> from 'sockjs-client';
import { WebSocketShareService } from './web-socket.service';
import {Inject, Injectable} from '@angular/core';
import {DorseyConfiguration} from "../../models/configuration";

@Injectable()
export class WebSocketAPI {
  private readonly serverUrl: string;
  private stompClient;

  constructor(@Inject('config') private config: DorseyConfiguration, private websocketShare: WebSocketShareService) {
    this.serverUrl = `${config.environment.apiUrl}/socket`;
}
  connect() {
    let ws = new SockJS(this.serverUrl);
    this.stompClient = Stomp.over(ws);
    let that = this;
    this.stompClient.connect(
      {},
      frame => {
        that.stompClient.subscribe('/notification', message => {
          that.onMessageReceived(message);
        });
      },
      this.errorCallBack
    );
  }

  disconnect() {
    if (this.stompClient !== null) {
      this.stompClient.disconnect();
    }
    console.log('Disconnected');
  }

  errorCallBack(error) {
    console.log('errorCallBack -> ' + error);
  }
  onMessageReceived(message) {
    this.websocketShare.onNewValueReceive(message.body);
  }

  sendMessage(message) {
    this.stompClient.send('/api/send/message', {}, message);
  }
}
