import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BaseRestService } from '../base-rest.service';
import { IUserNotificationConfig } from '../../models/admin/users/user-notification-config.model';

@Injectable()
export class NotificationService extends BaseRestService {
  findUserNotifications(): Observable<any> {
    return this.get(`notification/user`);
  }

  deleteUserNotification(userId: string, id: number): Observable<any> {
    return this.delete(`notification/user/${userId}/${id}`);
  }

  findUserNotificationsConfig(): Observable<any> {
    return this.get(`notification/user/config`);
  }

  updateUserNotificationsConfig(
    notificationConfig: IUserNotificationConfig
  ): Observable<any> {
    return this.put(`notification/user/config`, notificationConfig);
  }
}
