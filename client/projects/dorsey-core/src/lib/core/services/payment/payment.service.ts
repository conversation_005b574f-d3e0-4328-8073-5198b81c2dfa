import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { IWorkflowTask } from '../../models/workflow/workflow-task.model';
import { BaseRestService } from '../base-rest.service';
import { IWorkflow } from '../../models/workflow/workflow.model';
import { IWorkflowData } from '../../models/workflow/workflow-data.model';
import { IWorkflowTaskType } from '../../models/workflow/workflow-task-type.model';
import { IWorkflowTaskTypeData } from '../../models/workflow/workflow-task-type-data.model';
import { DorseyConfiguration } from '../../models/configuration';
import { HttpClient } from '@angular/common/http';
import { StripeService } from 'ngx-stripe';

@Injectable()
export class PaymentService extends BaseRestService {
  constructor(
    @Inject('config') protected override config: DorseyConfiguration,
    protected override httpClient: HttpClient,
    private stripeService: StripeService
  ) {
    super(config, httpClient);
  }

  createPaymentIntent(amount: number): Observable<any> {
    return this.post(`payment/payment-intent`, { amount });
  }

  confirmPayment(clientSecret: string, cardId: string) {
    return this.stripeService.confirmCardPayment(clientSecret, {
      payment_method: cardId,
    });
  }

  createPaymentMethod(cardId: string): Observable<any> {
    return this.post(`payment/create-card`, cardId);
  }

  setPreferredCard(cardId: string): Observable<any> {
    return this.put(`payment/update-card`, cardId);
  }

  deleteCard(cardId: string): Observable<any> {
    return this.delete(`payment/delete-card/${cardId}`);
  }

  findCards(): Observable<any> {
    return this.get('payment/cards');
  }

  hasCard(): Observable<boolean> {
    return this.get('payment/has-card');
  }
}
