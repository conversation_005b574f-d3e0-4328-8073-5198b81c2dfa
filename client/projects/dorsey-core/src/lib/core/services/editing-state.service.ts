import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { FormAction } from '../models/form-action';

@Injectable({
  providedIn: 'root'
})
export class EditingStateService {
  private isEditing: Subject<FormAction>;

  public editingState: {
    data?: any[];
    isEditing?: boolean;
    formState?: FormAction;
    url?: { prev?: string; curr?: string };
  } = { data: [], isEditing: false, url: {} };

  constructor() {
    this.isEditing = new Subject<FormAction>();
  }

  getValue(): Observable<FormAction> {
    return this.isEditing.asObservable();
  }

  setValue(value: FormAction): void {
    this.editingState.isEditing = FormAction.EDIT === value || FormAction.SAVE === value;
    this.editingState.formState = value;
    this.isEditing.next(value);
  }

  getEditingState(): boolean {
    return this.editingState.isEditing;
  }

  getFormActionState(): FormAction {
    return this.editingState.formState;
  }

  setData(datasets: any[]) {
    this.editingState.data.push(...datasets);
  }

  getData() {
    return this.editingState.data;
  }

  clearData() {
    this.editingState.data = [];
  }

  setPrevUrl(url: string) {
    this.editingState.url.prev = url;
  }

  getPrevUrl() {
    return this.editingState.url.prev;
  }

  setCurrUrl(url: string) {
    this.editingState.url.curr = url;
  }

  getCurrUrl() {
    return this.editingState.url.curr;
  }
}
