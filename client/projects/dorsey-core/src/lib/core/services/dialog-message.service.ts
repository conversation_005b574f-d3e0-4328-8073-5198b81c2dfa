import { Injectable, TemplateRef } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { DialogMessage } from '../models/dialog-message.model';
import { SafeHtml } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root',
})
export class DialogMessageService {
  private messageSource = new Subject<DialogMessage>();
  message: Observable<DialogMessage> = this.messageSource.asObservable();

  sendMessage(message: DialogMessage) {
    this.messageSource.next(message);
  }

  displayWarning(
    body: string,
    hasCancel = true,
    okCallBack = null,
    cancelCallBack = null,
    ctaLabel = 'Continue'
  ): void {
    const message: DialogMessage = {
      title: 'Warning',
      body,
      ctaLabel,
      okCallBack,
      cancelCallBack,
      hasCancel: hasCancel,
    };
    this.sendMessage(message);
  }

  displayError(
    body: string | SafeHtml,
    hasCancel = true,
    okCallBack = null,
    cancelCallBack = null,
    ctaLabel = 'OK'
  ): void {
    const message: DialogMessage = {
      title: 'Error',
      body,
      ctaLabel,
      okCallBack,
      cancelCallBack,
      hasCancel: hasCancel,
    };
    this.sendMessage(message);
  }

  displayCustom(
    title: string,
    hasCancel: boolean,
    okCallBack: any,
    cancelCallBack: any,
    body: TemplateRef<any>,
    ctaLabel: string
  ): void {
    const message: DialogMessage = {
      title,
      body,
      okCallBack,
      cancelCallBack,
      ctaLabel,
      hasCancel,
    };
    this.sendMessage(message);
  }
}
