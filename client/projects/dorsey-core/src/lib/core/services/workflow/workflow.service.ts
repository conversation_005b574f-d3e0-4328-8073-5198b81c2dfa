import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { IWorkflowTask } from '../../models/workflow/workflow-task.model';
import { BaseRestService } from '../base-rest.service';
import { IWorkflow } from '../../models/workflow/workflow.model';
import { IWorkflowData } from '../../models/workflow/workflow-data.model';
import { IWorkflowTaskType } from '../../models/workflow/workflow-task-type.model';
import { IWorkflowTaskTypeData } from '../../models/workflow/workflow-task-type-data.model';

@Injectable()
export class WorkflowService extends BaseRestService {
  findById(id: string): Observable<any> {
    return this.get(`workflow/${id}`);
  }
  findWorkflowByName(name: string): Observable<any> {
    return this.get(`workflow/name/${name}`);
  }
  findWorkflowByVersionAndType(
    version: number,
    typeId: string
  ): Observable<any> {
    return this.get(`workflow/${version}/${typeId}`);
  }
  findWorkflows(): Observable<any> {
    return this.get(`workflow`);
  }
  findManagedWorkflows(): Observable<any> {
    return this.get(`workflow/managed`);
  }
  findWorkflowEntities(): Observable<any> {
    return this.get(`workflow/entities`);
  }
  findWorkflowsByLovType(id: string): Observable<any> {
    return this.get(`workflow/lovType/${id}`);
  }
  findWorkflowsByType(id: string): Observable<any> {
    return this.get(`workflow/type/${id}`);
  }
  findCompletedWorkflows(): Observable<any> {
    return this.get(`workflow/completed`);
  }
  updateWorkflows(wf: IWorkflow[]): Observable<any> {
    return this.put(`workflow`, wf);
  }
  findWorkflowData(wfId: string): Observable<any> {
    return this.get(`workflow/data/${wfId}`);
  }
  updateWorkflowData(wfData: IWorkflowData): Observable<any> {
    return this.put(`workflow/data`, wfData);
  }
  findWorkflowTask(id: string): Observable<any> {
    return this.get(`workflow/task/${id}`);
  }
  findWorkflowTaskType(id: string): Observable<any> {
    return this.get(`workflow/taskType/${id}`);
  }
  findWorkflowTaskTypes(): Observable<any> {
    return this.get(`workflow/taskTypes`);
  }
  updateWorkflowTaskType(data: IWorkflowTaskType): Observable<any> {
    return this.put(`workflow/taskType`, data);
  }
  updateWorkflowTaskTypeData(data: IWorkflowTaskTypeData): Observable<any> {
    return this.put(`workflow/taskTypeData`, data);
  }
  findWorkflowTaskTypeByName(
    workflow: string,
    taskType: string
  ): Observable<any> {
    return this.get(`workflow/taskType/${workflow}/${taskType}`);
  }
  findWorkflowTaskTypeByNameAndVersion(
    workflow: string,
    taskType: string,
    version: number
  ): Observable<any> {
    return this.get(`workflow/taskType/${workflow}/${taskType}/${version}`);
  }
  findRelationsByTaskTypeInput(
    workflowId: string,
    inputId: string
  ): Observable<any> {
    return this.get(`workflow/taskType/relations/${workflowId}/${inputId}`);
  }
  findTaskById(id: string): Observable<any> {
    return this.get(`workflow/task/${id}`);
  }
  createTask(task: IWorkflowTask): Observable<any> {
    return this.post(`workflow`, task);
  }
  findAssignedToMe(): Observable<any> {
    return this.get(`workflow/task/assigned`);
  }
  findInitiatedByMe(): Observable<any> {
    return this.get(`workflow/task/pending`);
  }
  findCompleted(): Observable<any> {
    return this.get(`workflow/task/completed`);
  }
  approve(task: IWorkflowTask): Observable<any> {
    return this.put(`workflow/task/approve`, task);
  }
  reject(task: IWorkflowTask): Observable<any> {
    return this.put(`workflow/task/reject`, task);
  }
  next(task: IWorkflowTask): Observable<any> {
    return this.put(`workflow/task/next`, task);
  }
  deleteById(id: string): Observable<any> {
    return this.delete(`workflow/${id}`);
  }
  softDeleteById(id: string): Observable<any> {
    return this.delete(`workflow/softDelete/${id}`);
  }
  checkTaskIsApproval(id: string): Observable<any> {
    return this.get(`workflow/task/isApproval/${id}`);
  }
  findJifWorkflowTaskHistoryForItem(id: string): Observable<any> {
    return this.get(`workflow/task/jif/history/${id}`);
  }
  findPendingWorkflowsTaskHistories(): Observable<any> {
    return this.get(`workflow/task/history/pending`);
  }

  findCompletedWorkflowsTaskHistories(
    startDate: string,
    endDate: string
  ): Observable<any> {
    return this.get(`workflow/task/history/completed/${startDate}/${endDate}`);
  }

  findJifPendingSequencedWorkflowsTaskHistoriesForItem(
    id: string
  ): Observable<any> {
    return this.get(`workflow/task/jif/history/pending/sequenced/${id}`);
  }

  jifSendBackTask(
    justification: string,
    taskId: string,
    taskTypeId: string
  ): Observable<any> {
    return this.put(
      `workflow/task/jif/sendBack/${taskId}/${taskTypeId}`,
      justification
    );
  }

  findWorkflowTaskCurrentStepByEntityName(name: string): Observable<any> {
    return this.get(`workflow/workflowTask/${name}`);
  }
}
