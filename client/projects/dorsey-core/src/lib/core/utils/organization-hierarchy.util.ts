export function addHierarchyLabelToNodes(Tree: any[]) {
  for (const n of Tree) {
    n.label = `${n.levelData.hierarchyName}: ${n.hierarchyValue}`;

    if (n.children.length) {
      addHierarchyLabelToNodes(n.children);
    }
  }
}

export function addHierarchyLabelProperty(data: any[]) {
  data.forEach(u => {
    if (u.hierarchyData) {
      u.hierarchyData.label = `${u.hierarchyData.levelData.hierarchyName}: ${u.hierarchyData.hierarchyValue}`;
    }
  });
}
