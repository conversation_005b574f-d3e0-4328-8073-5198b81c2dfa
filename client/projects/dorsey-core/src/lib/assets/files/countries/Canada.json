{"name": "Canada", "states": [{"name": "Alberta", "cities": ["Airdrie", "Athabasca", "<PERSON><PERSON>", "Barrhead", "Bass<PERSON>", "Beaumont", "Beaverlodge", "Black Diamond", "Blackfalds", "<PERSON> Accord", "Bonnyville", "Bow Island", "<PERSON>", "Calgary", "Calmar", "<PERSON><PERSON>", "Canmore", "<PERSON><PERSON>", "Carstairs", "Chestermere", "<PERSON><PERSON><PERSON>", "Coaldale", "Coalhurst", "<PERSON>", "Cold Lake", "Crossfield", "Devon", "Didsbury", "Drayton Valley", "Edmonton", "<PERSON><PERSON>", "Elk Point", "Fairview", "<PERSON><PERSON><PERSON>", "Fort Macleod", "Fort McMurray", "Fort Saskatchewan", "Fox Creek", "<PERSON>", "Grand Centre", "Grande Cache", "Grande Prairie", "<PERSON><PERSON><PERSON>", "<PERSON>", "Heritage Pointe", "High Level", "High Prairie", "High River", "<PERSON><PERSON>", "Irricana", "Jasper Park Lodge", "<PERSON><PERSON>", "Lac La Biche", "<PERSON><PERSON><PERSON>", "Lamont", "Larkspur", "<PERSON>", "<PERSON><PERSON>", "Lethbridge", "Lloydminster", "<PERSON><PERSON><PERSON>", "<PERSON>", "Mannville", "Maple Ridge", "<PERSON><PERSON>", "Medicine Hat", "Mill Woods Town Centre", "Millet", "Morinville", "<PERSON><PERSON>", "Okotoks", "Olds", "Peace River", "Penhold", "Picture Butte", "Pincher Creek", "Ponoka", "<PERSON>", "<PERSON>", "Red Deer", "Rideau Park", "<PERSON><PERSON><PERSON>", "Rocky Mountain House", "Sexsmith", "Sherwood Park", "Silver Berry", "Slave Lake", "Smoky Lake", "Spirit River", "Springbrook", "Spruce Grove", "St. Albert", "<PERSON><PERSON><PERSON>", "Stony Plain", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Swan Hills", "Sylvan Lake", "<PERSON><PERSON>", "<PERSON><PERSON>", "Three Hills", "Tofield", "Two Hills", "Valleyview", "Vegreville", "Vermilion", "Viking", "Vulcan", "<PERSON><PERSON><PERSON>", "Wembley", "<PERSON><PERSON>", "<PERSON><PERSON>", "Wetaskiwin", "Whitecourt", "<PERSON>"]}, {"name": "British Columbia", "cities": ["Abbotsford", "<PERSON><PERSON><PERSON><PERSON>", "Aldergrove", "Aldergrove East", "Anmore", "Arbutus Ridge", "<PERSON>", "Ashcroft", "Barrière", "Bowen Island", "<PERSON><PERSON>", "Burns Lake", "Cache Creek", "Campbell River", "Castlegar", "Cedar", "Central Coast Regional District", "Chase", "<PERSON><PERSON><PERSON>", "Chetwynd", "Chilliwack", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Coquitlam", "<PERSON><PERSON><PERSON>", "Cowichan Bay", "Cranbrook", "Creston", "Cumberland", "Dawson Creek", "Delta", "Denman Island", "Denman Island Trust Area", "Duck Lake", "<PERSON>", "East Wellington", "Elkford", "<PERSON>", "<PERSON><PERSON><PERSON>", "Fairwinds", "<PERSON><PERSON><PERSON>", "Fort Nelson", "Fort St. John", "Fraser Valley Regional District", "French Creek", "Fruitvale", "Gibsons", "Golden", "Grand Forks", "Hanceville", "Hope", "Hornby Island", "Houston", "Invermere", "Kamloops", "<PERSON><PERSON><PERSON>", "Kimberley", "Kitimat", "<PERSON><PERSON><PERSON>", "Ladysmith", "Lake Cowichan", "Langford", "Langley", "Lillooet", "Lions Bay", "Logan Lake", "Lumby", "<PERSON>", "Maple Ridge", "<PERSON><PERSON><PERSON>", "Met<PERSON>sin", "Metro Vancouver Regional District", "Mission", "Nakusp", "<PERSON><PERSON>", "<PERSON>", "New Westminster", "North Cowichan", "North Oyster/Yellow Point", "North Saanich", "North Vancouver", "Oak Bay", "Okanagan", "Okanagan Falls", "<PERSON>", "Osoyoos", "Parksville", "Peace River Regional District", "Peachland", "Pemberton", "Pen<PERSON><PERSON>", "Pitt Meadows", "Port Alberni", "Port Coquitlam", "Port McNeill", "Port Moody", "Powell River", "Prince <PERSON>", "<PERSON>", "Princeton", "<PERSON><PERSON><PERSON>", "Quesnel", "Regional District of Alberni-Clayoquot", "Regional District of Central Okanagan", "Revelstoke", "Richmond", "Rossland", "<PERSON><PERSON>", "Salmo", "Salmon Arm", "Salt Spring Island", "Saltair", "<PERSON><PERSON><PERSON>", "Sicamous", "Six Mile", "<PERSON><PERSON>", "<PERSON><PERSON>", "South Pender Harbour", "Sparwood", "Summerland", "Surrey", "Terrace", "Tofino", "Trail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tumbler Ridge", "Ucluelet", "Vancouver", "Vanderhoof", "<PERSON>", "Victoria", "Walnut Grove", "Welcome Beach", "West End", "West Kelowna", "West Vancouver", "<PERSON><PERSON><PERSON>", "White Rock", "Williams <PERSON>"]}, {"name": "Manitoba", "cities": ["Altona", "Beausejour", "Boisse<PERSON>in", "<PERSON>", "Carberry", "<PERSON><PERSON>", "Cross Lake 19A", "<PERSON><PERSON><PERSON>", "<PERSON>", "Deloraine", "<PERSON>lin Flon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Headingley", "Ile des Chênes", "Killarney", "La Broquerie", "Lac du Bonnet", "Landmark", "Lorette", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Moose Lake", "Morden", "<PERSON>", "Neepawa", "Niverville", "Portage la Prairie", "Rivers", "<PERSON><PERSON>", "Selkirk", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Steinbach", "Stonewall", "Swan River", "The Pas", "<PERSON>", "Virden", "West St. Paul", "<PERSON>", "Winnipeg"]}, {"name": "New Brunswick", "cities": ["Baie Ste. Anne", "Bathurst", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Florenceville-Bristol", "Fredericton", "Fundy Bay", "Grande-Digue", "Greater Lakeburn", "<PERSON>", "<PERSON>", "Keswick Ridge", "Lincoln", "Lutes Mountain", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Moncton", "<PERSON><PERSON><PERSON><PERSON>", "New Maryland", "<PERSON><PERSON><PERSON>", "Oromocto", "Richibucto", "Sackville", "<PERSON>", "Saint <PERSON>", "<PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>", "Salisbury", "<PERSON><PERSON><PERSON>", "Shediac Bridge-Shediac River", "Shippagan", "Starlight Village", "Sussex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"]}, {"name": "Newfoundland and Labrador", "cities": ["<PERSON>", "Bay St. George South", "<PERSON><PERSON><PERSON>", "Botwood", "<PERSON><PERSON><PERSON>", "Carbonear", "Catalina", "Channel-Port aux Basques", "Clarenville-Shoal Harbour", "Conception Bay South", "Corner Brook", "Deer Lake", "Fogo Island", "Gambo", "<PERSON><PERSON>", "Grand Bank", "Grand Falls-Windsor", "Happy Valley-Goose Bay", "Harbour Breton", "Labrador City", "Lewisporte", "Marystown", "Mount Pearl", "Pasadena", "Springdale", "St. Anthony", "St. John's", "Stephenville", "Stephenville Crossing", "Torbay", "Upper Island Cove", "<PERSON><PERSON><PERSON>"]}, {"name": "Northwest Territories", "cities": ["Behchokǫ̀", "Fort McPherson", "Fort Smith", "Hay River", "Inuvik", "<PERSON>", "Yellowknife"]}, {"name": "Nova Scotia", "cities": ["Amherst", "Annapolis County", "Antigonish", "Berwick", "Bridgewater", "Cape Breton County", "Chester", "Colchester", "Cole Harbour", "Cow Bay", "Dartmouth", "<PERSON><PERSON><PERSON>", "Digby County", "English Corner", "Eskasoni 3", "Fall River", "Glace Bay", "<PERSON>", "Halifax", "Hantsport", "Hayes Subdivision", "Kentville", "Lake Echo", "<PERSON><PERSON><PERSON>", "Lower Sackville", "Lunenburg", "Middleton", "New Glasgow", "Oxford", "Parr<PERSON>", "Pictou", "Pictou County", "Port Hawkesbury", "Port Williams", "Princeville", "<PERSON><PERSON><PERSON>", "Springhill", "Sydney", "Sydney Mines", "Truro", "Windsor", "Wolfville", "Yarmouth"]}, {"name": "Nunavut", "cities": ["Clyde River", "Gjoa Haven", "Iqaluit", "Kugluktuk", "Pangnirtung", "<PERSON><PERSON>"]}, {"name": "Ontario", "cities": ["Ajax", "Algoma", "<PERSON><PERSON><PERSON>", "Amherstburg", "Amigo Beach", "Ancaster", "Angus", "Arnprior", "Atikokan", "Attawapiskat", "Aurora", "<PERSON><PERSON><PERSON>", "Azilda", "Ballantrae", "Bancroft", "<PERSON><PERSON>", "Bath", "Belleville", "Bells Corners", "Belmont", "Binbrook", "Bluewater", "<PERSON><PERSON><PERSON>", "Bracebridge", "Brampton", "<PERSON><PERSON>", "Brantford", "Brockville", "Brussels", "<PERSON><PERSON><PERSON>", "Burlington", "Cambridge", "Camlachie", "Capreol", "Carleton Place", "<PERSON><PERSON><PERSON>", "Chatham", "Chatham-Kent", "Clarence-<PERSON><PERSON>", "Cobourg", "Cochrane District", "Collingwood", "Concord", "Constance Bay", "Cookstown", "Cornwall", "<PERSON><PERSON><PERSON>", "Deep River", "Delaware", "Deseron<PERSON>", "Dorchester", "Dowling", "Dryden", "Durham", "Ear Falls", "East Gwillimbury", "East York", "<PERSON>", "Elmvale", "<PERSON><PERSON><PERSON>", "Espanola", "Essex", "Etobicoke", "Exeter", "Fort Erie", "Fort Frances", "Gananoque", "Glencoe", "<PERSON><PERSON><PERSON>", "Golden", "<PERSON><PERSON><PERSON><PERSON>", "Greater Napanee", "Greater Sudbury", "Greenstone", "Guelph", "Haldimand County", "Haliburton Village", "Halton", "<PERSON>", "Hanover", "<PERSON><PERSON>", "Hawkesbury", "Hearst", "Hornepayne", "Huntsville", "Huron East", "Ingersoll", "Innisfil", "Iroquois Falls", "<PERSON>", "<PERSON><PERSON><PERSON>", "Kapuskasing", "Kawartha Lakes", "<PERSON><PERSON>", "Keswick", "Kincardine", "King", "Kingston", "Kirkland Lake", "<PERSON>er", "L'Orignal", "Lakefield", "Lambton Shores", "<PERSON><PERSON>", "Leamington", "<PERSON><PERSON><PERSON>", "<PERSON>", "Listowel", "Little Current", "Lively", "London", "Lucan", "Mad<PERSON>", "Manitoulin District", "Manitouwadge", "Marathon", "Markdale", "Markham", "<PERSON><PERSON>", "Meaford", "Metcalfe", "Midland", "<PERSON><PERSON><PERSON><PERSON>", "Millbrook", "<PERSON>", "Mississauga", "Mississauga Beach", "Moose Factory", "Moosonee", "Morrisburg", "Mount Albert", "Mount Brydges", "Napanee", "Napanee Downtown", "<PERSON><PERSON><PERSON>", "Nepean", "New Hamburg", "Newmarket", "Niagara Falls", "Nipissing District", "Norfolk County", "North Bay", "North Perth", "North York", "Norwood", "Oakville", "<PERSON><PERSON><PERSON>", "Orangeville", "<PERSON><PERSON><PERSON>", "Osgoode", "Oshawa", "Ottawa", "Owen Sound", "Paisley", "Paris", "Parkhill", "Parry Sound", "Parry Sound District", "Peel", "Pembroke", "Perth", "Petawawa", "Peterborough", "Petrolia", "Pickering", "Picton", "Plantagenet", "Plattsville", "Port Colborne", "Port Hope", "Port Rowan", "Port Stanley", "<PERSON><PERSON><PERSON>", "<PERSON>", "Prince <PERSON>", "Queenswood Heights", "Quinte West", "Rainy River District", "Rayside-<PERSON><PERSON><PERSON>", "Red Lake", "Regional Municipality of Waterloo", "<PERSON><PERSON><PERSON>", "Richmond", "Richmond Hill", "Ridgetown", "Rockwood", "<PERSON>", "Sarnia", "Sault Ste. Marie", "Scarborough", "Seaforth", "<PERSON><PERSON><PERSON>", "Simcoe", "Sioux Lookout", "Skatepark", "Smiths Falls", "South Huron", "South River", "St. Catharines", "St. George", "St<PERSON>", "<PERSON>", "Stoney Point", "Stratford", "Sudbury", "Tavistock", "Temiskaming Shores", "Thessalon", "Thornhill", "<PERSON><PERSON>", "Thunder Bay", "Thunder Bay District", "Timiskaming District", "<PERSON><PERSON>", "Tobermory", "Toronto", "Toronto county", "Tottenham", "Tweed", "Uxbridge", "Valley East", "<PERSON><PERSON>", "<PERSON>", "Vineland", "<PERSON>", "Walpole Island", "Wasaga Beach", "Waterford", "Waterloo", "Watford", "<PERSON><PERSON>", "Welland", "<PERSON><PERSON>", "Wendover", "West Lorne", "Willowdale", "Winchester", "Windsor", "<PERSON><PERSON>", "Woodstock", "York"]}, {"name": "Prince Edward Island", "cities": ["Alberton", "Belfast", "Charlottetown", "Cornwall", "Fallingbrook", "Kensington", "Montague", "<PERSON><PERSON><PERSON>", "Summerside"]}, {"name": "Quebec", "cities": ["Abitibi-Témiscamingue", "Acton Vale", "Adstock", "Albanel", "Alma", "<PERSON>", "Amqui", "Ange-Gardien", "Asbestos", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Baie-<PERSON>", "Baie-Saint-Paul", "Barr<PERSON><PERSON>", "Bas-Saint-Laurent", "Beaconsfield", "Beauceville", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bécancour", "Bedford", "Beloeil", "Bert<PERSON>erville", "Blainville", "Bois-des-Filion", "Boisbriand", "Bonaventure", "Boucherville", "Breakeyville", "<PERSON><PERSON><PERSON>", "Brossard", "Brownsburg-Chatham", "Buckingham", "Cabano", "Cacouna", "Candiac", "Cantley", "Cap-Chat", "Cap-Santé", "Capitale-Nationale", "<PERSON><PERSON><PERSON>", "Carleton", "Carleton-sur-Mer", "Centre-du-Québec", "Cha<PERSON>ly", "Chambord", "<PERSON>", "Cha<PERSON><PERSON>", "Charlemagne", "Château-<PERSON>er", "Châteauguay", "Chaudière-Appalaches", "<PERSON><PERSON><PERSON>", "Chibougamau", "Chute-aux-Outardes", "Coaticook", "Contrecoeur", "Cookshire", "Cookshire-Eaton", "Côte-Nord", "Côte-Saint-Luc", "Coteau-du-Lac", "Cowansville", "<PERSON><PERSON><PERSON>", "Danville", "<PERSON>luyville", "<PERSON><PERSON>", "Deux-Montagnes", "<PERSON><PERSON><PERSON><PERSON>", "Dolbeau<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dollard-<PERSON>", "Donnacona", "Dorval", "Drummondville", "<PERSON><PERSON>", "East Angus", "East Broughton", "Farnham", "Ferme-Neuve", "Fermont", "Forestville", "Fort-Coulonge", "Fossambault-sur-le-Lac", "<PERSON>", "Gas<PERSON>é", "Gaspésie-Îles-de-la-Madeleine", "Gatineau", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Hampstead", "Hauterive", "Havre-Saint-Pierre", "Hérouxville", "<PERSON>", "Huntingdon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kingsey Falls", "Kirkland", "L'Ancienne-Lorette", "L'Ange-Gardien", "L'Ascension-de-Notre-Seigneur", "L'Assomption", "L'Épiphanie", "L'Île-Perrot", "La Conception", "La Haute-Saint-Charles", "La Malbaie", "La Minerve", "La Pocatière", "La Prairie", "La Sarre", "La Tuque", "<PERSON>le", "Lac-Alouette", "Lac-Brome", "Lac<PERSON><PERSON><PERSON>", "Lac<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "le Plateau", "Lebel-sur-Quévillon", "<PERSON><PERSON><PERSON>", "Les Cèdres", "Les <PERSON>x", "Les Escoumins", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lorraine", "<PERSON><PERSON>", "Luceville", "Macamic", "Magog", "Malartic", "Maliotenam", "Manawan", "Mandeville", "Maniwaki", "<PERSON>", "Marieville", "Mascouche", "Maskinongé", "Matagami", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Melocheville", "<PERSON><PERSON><PERSON>", "Métabet<PERSON>uan", "Metabetchouan-Lac-a-la-Croix", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Mont-Joli", "Mont-Laurier", "Mont-Royal", "Mont-Saint-Grégoire", "Mont-Saint-Hilaire", "Mont-Tremblant", "<PERSON><PERSON><PERSON>", "Montréal", "Montréal-Est", "Montréal-Ouest", "Morin-Heights", "Napierville", "Neuville", "New Carlisle", "New-Richmond", "<PERSON><PERSON>", "Nord-du-Québec", "<PERSON><PERSON>", "Notre-Dame-de-Grâce", "Notre-Dame-de-l'Île-Perrot", "Notre-Dame-des-Prairies", "Notre-Dame-du-Lac", "Notre-Dame-du-Mont-Carmel", "<PERSON><PERSON>", "Ormstown", "Otterburn Park", "<PERSON><PERSON><PERSON><PERSON>", "Papineauville", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Piedmont", "Pierreville", "<PERSON><PERSON>urt", "Plessisville", "Pohénégamook", "Pointe-Calumet", "Pointe<PERSON><PERSON>", "Pointe-du-Lac", "Pont Rouge", "Pont-Rouge", "Port-Cartier", "Portneuf", "Prévost", "Princeville", "Québec", "<PERSON><PERSON>", "Repentigny", "<PERSON><PERSON><PERSON>", "Richmond", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Rivière-du-Loup", "Rivière-Rouge", "<PERSON><PERSON><PERSON>", "Rock Forest", "<PERSON><PERSON><PERSON>", "Rougemont", "Rouyn-Noranda", "Sacré-Coeur", "Saguenay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-André-Avellin", "Saint-<PERSON><PERSON><PERSON>", "Saint-Antoine-de-<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-Barnabé-Sud", "Saint-Basile-le-Grand", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-Bruno", "Saint-Bruno-<PERSON>", "Saint-Bruno-de-Montarville", "Saint-<PERSON>ut", "Saint-Césaire", "Saint<PERSON><PERSON>", "Saint-Côme-Linière", "<PERSON><PERSON><PERSON><PERSON>", "Saint-Cyrille-de-Wendover", "Saint-Damase", "Saint-Denis-sur-Richelieu", "Saint-Donat-de-Montcalm", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>-de-<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-F<PERSON><PERSON><PERSON>", "Saint-Félix-de-Valois", "<PERSON><PERSON><PERSON>", "Saint-G<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-Germain-de-Grantham", "Saint-<PERSON>", "Saint-Hippolyte", "<PERSON><PERSON><PERSON><PERSON>", "Saint<PERSON><PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>Mineur", "<PERSON><PERSON><PERSON><PERSON>", "Saint-Jean-sur-Richelieu", "Saint-<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-<PERSON>-de-Coleraine", "Saint-<PERSON>", "Saint-Lambert-de-Lauzon", "Saint<PERSON><PERSON>", "Saint-<PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>", "Saint-Léonard-d'Aston", "Saint-Libo<PERSON>", "Saint-<PERSON><PERSON><PERSON><PERSON>", "Saint-Marc-des-Carrières", "<PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON>", "Saint-Michel-des-Saints", "Saint-Na<PERSON>re", "<PERSON><PERSON><PERSON><PERSON>", "Saint-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saint-Philippe-de-La Prairie", "Saint-<PERSON>", "Saint-Pierre-les-Becquets", "Saint-<PERSON>", "Saint-<PERSON><PERSON><PERSON>", "Saint<PERSON><PERSON>", "Saint<PERSON><PERSON><PERSON><PERSON>", "Saint-Rémi-de-Tingwick", "Saint<PERSON><PERSON><PERSON><PERSON>", "Saint-Sauveur-des-Monts", "Saint-Siméon", "Saint<PERSON><PERSON>", "Saint-Tite", "Saint<PERSON><PERSON>", "Saint-Zotique", "Sainte Catherine de <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sainte-Agathe-des-Monts", "Sainte-Anne-de-Bellevue", "Sainte-Anne-des-Monts", "Sainte-Anne-des-Plaines", "Sainte-Béatrix", "Sainte<PERSON><PERSON>", "Sainte-Croix", "Sainte<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sainte-<PERSON><PERSON>", "Sainte-Madeleine", "Sainte-Marie", "Sainte-Marthe-sur-le-Lac", "Sainte-Martine", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sainte-Thérèse", "Salaberry-de-Valleyfield", "Salluit", "Senneterre", "Sept-Îles", "<PERSON>", "Shawinigan", "Shawville", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "St-Jean-Port-Joli", "<PERSON>", "Témiscaming", "Terrasse-des-Pins", "Terrebonne", "Thetford-Mines", "<PERSON><PERSON><PERSON>", "Trois-Rivières", "Val-d'Or", "Val<PERSON>David", "Val-des-Monts", "Val-Morin", "Valcourt", "Vallée-Jonction", "Varennes", "Vaudreuil-Dorion", "Venise-en-Québec", "Verchères", "Victoriaville", "Ville-Marie", "Wakefield", "Warwick", "Waskaganish", "<PERSON><PERSON><PERSON><PERSON>", "Waterloo", "Weedon Centre", "Westmount", "Weymontachie", "Windsor", "Yamachiche"]}, {"name": "Saskatchewan", "cities": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Biggar", "Canora", "<PERSON><PERSON>", "Dalmeny", "Esterhazy", "Estevan", "Foam Lake", "Gravelbourg", "Hudson Bay", "Humboldt", "Indian Head", "<PERSON><PERSON><PERSON>", "Kerr<PERSON><PERSON>", "Kindersley", "La Ronge", "Langenburg", "Lang<PERSON>", "<PERSON><PERSON><PERSON>", "Lumsden", "<PERSON><PERSON>", "Maple Creek", "Martensville", "Meadow Lake", "Mel<PERSON>", "<PERSON>", "<PERSON>", "Moosomin", "<PERSON><PERSON><PERSON>", "North Battleford", "Outlook", "Oxbow", "Pelican Narrows", "Pilot <PERSON><PERSON>", "Preeceville", "<PERSON>", "Regina", "Regina Beach", "Rosetown", "Rost<PERSON>n", "Saskatoon", "<PERSON><PERSON><PERSON>", "Shellbrook", "Swift Current", "Tisdale", "Unity", "<PERSON><PERSON>", "<PERSON><PERSON>", "Watrous", "<PERSON><PERSON><PERSON>", "White City", "<PERSON><PERSON><PERSON>", "Wynyard", "<PERSON><PERSON>"]}, {"name": "Yukon", "cities": ["Dawson City", "Haines Junction", "Watson Lake", "Whitehorse"]}]}