import { ModuleWithProviders, NgModule } from '@angular/core';
import { DorseyCoreComponent } from './dorsey-core.component';
import { DorseyCoreService } from './dorsey-core.service';
import { DorseyConfiguration } from './core/models/configuration';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons';
import { CoreModule } from './core/core.module';
import { AccountService } from './core/auth/account.service';
import { SystemService } from './core/services/admin/system.service';

@NgModule({
  declarations: [DorseyCoreComponent],
  imports: [CoreModule],
  providers: [AccountService, SystemService],
  exports: [DorseyCoreComponent],
})
export class DorseyCoreModule {
  constructor(library: FaIconLibrary) {
    library.addIconPacks(fas, far);
  }

  static forRoot(
    configuration: DorseyConfiguration
  ): ModuleWithProviders<DorseyCoreModule> {
    return {
      ngModule: DorseyCoreModule,
      providers: [
        DorseyCoreService,
        { provide: 'config', useValue: configuration },
      ],
    };
  }
}
