{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"dorsey-core": {"projectType": "library", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/dorsey-core", "sourceRoot": "projects/dorsey-core/src", "prefix": "dorsey", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/dorsey-core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/dorsey-core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/dorsey-core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-builders/jest:run", "options": {"tsConfig": "./projects/dorsey-core/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}}}