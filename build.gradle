plugins {
	id 'org.springframework.boot' version '3.1.4'
	id 'io.spring.dependency-management' version '1.0.11.RELEASE'
	id 'java'
	id "org.sonarqube" version "3.4.0.2513"
	id 'maven-publish'
}

group = ARTIFACT_PACKAGE
version = ARTIFACT_VERSION

java {
	sourceCompatibility = JavaVersion.VERSION_17
}

jar {
	enabled = true
	archiveClassifier.set('')
}

publishing {
	publications {
		gpr(MavenPublication) {
			groupId = ARTIFACT_PACKAGE
			artifactId = ARTIFACT_ID
			version = ARTIFACT_VERSION

			from components.java
			pom {
				url = "https://gitlab.dorseyplus.com/dorsey-plus/core.git"
			}
		}
	}
	repositories {
		maven {
			name = REPO_NAME
			url = REPO_URL
			credentials(PasswordCredentials) {
				username = REPO_USER
				password = REPO_PASS
			}
			authentication {
				basic(BasicAuthentication)
			}
		}
	}
}

repositories {
	mavenCentral()
	maven {
		url "https://build.shibboleth.net/nexus/content/repositories/releases/"
	}
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-websocket'
	testImplementation 'junit:junit:4.13.1'
	testImplementation 'org.junit.jupiter:junit-jupiter:5.8.1'
	annotationProcessor 'org.projectlombok:lombok:1.18.24'
	implementation 'org.mapstruct:mapstruct:1.5.5.Final'
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
	implementation 'org.springframework.security:spring-security-saml2-service-provider:6.1.4'
//	implementation group: 'org.springframework.security.extensions', name: 'spring-security-saml2-core', version: '1.0.10.RELEASE'
	implementation 'org.springframework.boot:spring-boot-starter-web'
//	implementation 'org.springframework.boot:spring-boot-starter-tomcat'
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
	implementation 'org.springframework.boot:spring-boot-devtools'
	implementation 'org.springframework.boot:spring-boot-starter-logging'
	implementation 'org.springframework.boot:spring-boot-starter-aop'
	implementation 'org.springframework:spring-aspects'
	implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
	implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.3'
	implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.3'
	implementation 'com.fasterxml.jackson.module:jackson-module-parameter-names:2.13.3'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.3'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-hibernate6:2.15.0-rc3'
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.3'
	implementation 'com.stripe:stripe-java:27.0.0'
	implementation 'io.hypersistence:hypersistence-utils-hibernate-60:3.5.3'
	implementation 'com.vladmihalcea:hibernate-types-60:2.21.1'
	implementation group: 'com.google.code.gson', name: 'gson', version: '2.10.1'
	implementation 'org.jxls:jxls-reader:2.0.6'
	implementation group: 'org.hibernate.orm', name: 'hibernate-core', version: '6.3.0.CR1'
	implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
	implementation 'org.apache.commons:commons-text:1.11.0'
	implementation group: 'org.modelmapper', name: 'modelmapper', version: '3.0.0'
	implementation group: 'com.googlecode.jmapper-framework', name: 'jmapper-core', version: '1.6.1.CR2'
	compileOnly group: 'org.projectlombok', name: 'lombok', version: '1.18.24'
	compileOnly 'jakarta.platform:jakarta.jakartaee-web-api:10.0.0'
	runtimeOnly 'org.postgresql:postgresql:42.3.7'
	runtimeOnly 'mysql:mysql-connector-java:8.0.33'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.2'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.2'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testImplementation 'org.junit.jupiter:junit-jupiter'
	testImplementation 'com.h2database:h2'
	testImplementation 'org.flywaydb:flyway-core'
}

tasks.named('test') {
	useJUnitPlatform()
}
