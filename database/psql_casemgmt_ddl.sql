-- Officer, Detective, Prosecutor, etc.
CREATE TABLE roles_lov (
    code VARCHAR(15) NOT NULL PRIMARY KEY,
    description VARCHAR(2047) NOT NULL,
    deprecated BOOLEAN NOT NULL DEFAULT false
);

-- A case can have only one status at a time.
CREATE TABLE case_status_lov (
 code VARCHAR(8) NOT NULL PRIMARY KEY,
 description VARCHAR(2047) NOT NULL,
 deprecated BOOLEAN NOT NULL DEFAULT false
);

-- Robbery, murder, assault, etc.
CREATE TABLE case_type_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE audit_action_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE audit_entity_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE notification_type_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE users (
  user_id UUID NOT NULL PRIMARY KEY,
  email VARCHAR(320) NULL UNIQUE,
  is_active BOOLEAN NOT NULL DEFAULT false,
  first_name VARCHAR(255) NOT NULL,
  last_name VARCHAR(255) NOT NULL,
  title VARCHAR(255) NULL,
  effective_date TIMESTAMP NOT NULL,
  termination_date TIMESTAMP NULL,
  CONSTRAINT ck_users_termination_date
     CHECK (termination_date IS NULL OR termination_date >= effective_date)
);

-- Need table(s) for user email, phone, etc. plus xref.
-- CREATE TABLE user_contact_xref (
--   user_id UUID NOT NULL,
--   email VARCHAR(320) NULL UNIQUE,
--   phone VARCHAR(15) NULL UNIQUE,
--   address
--   CONSTRAINT fk_users
--     FOREIGN KEY (user_id) REFERENCES users (user_id)
-- );

-- This table allows a user to have more than one role.
CREATE TABLE user_roles_xref (
  user_id UUID NOT NULL,
  user_role VARCHAR(15) NOT NULL,
  CONSTRAINT pk_user_roles_xref PRIMARY KEY (user_id, user_role),
  CONSTRAINT fk_users
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_roles
    FOREIGN KEY (user_role) REFERENCES roles_lov (code)
);

CREATE SEQUENCE notification_id_seq;
CREATE TABLE user_notifications_xref (
 id INTEGER DEFAULT NEXTVAL('notification_id_seq') NOT NULL,
 user_id UUID NOT NULL,
 type VARCHAR(15) NOT NULL,
 body TEXT,
 url VARCHAR(2047),
 created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
 CONSTRAINT pk_user_notification_xref PRIMARY KEY (user_id, id),
 CONSTRAINT fk_notification_type_lov
     FOREIGN KEY (type) REFERENCES notification_type_lov (code)
);

CREATE TABLE user_notifications_config_xref (
 user_id UUID NOT NULL,
 type VARCHAR(15) NOT NULL,
 object_id UUID NOT NULL,
 notify BOOLEAN NOT NULL,
 CONSTRAINT pk_user_notification_config_xref PRIMARY KEY (user_id, type),
 CONSTRAINT fk_users
     FOREIGN KEY (user_id) REFERENCES users (user_id),
 CONSTRAINT fk_notification_type_lov
     FOREIGN KEY (type) REFERENCES notification_type_lov (code)
);

-- Bare bones case data.
CREATE TABLE case_data (
  case_id UUID NOT NULL PRIMARY KEY,
  case_name VARCHAR(2047) NOT NULL,
  opened_by UUID NOT NULL,
  opened_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  case_description VARCHAR(8191),
  location VARCHAR(2047),
  current_status VARCHAR(15) NOT NULL DEFAULT 'Open',
  status_modified_by UUID NOT NULL,
  status_update_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_case_data_opened
    FOREIGN KEY (opened_by) REFERENCES users (user_id),
  CONSTRAINT fk_case_data_modified
    FOREIGN KEY (status_modified_by) REFERENCES users (user_id),
  CONSTRAINT fk_case_status_lov
    FOREIGN KEY (current_status) REFERENCES case_status_lov (code)
);

-- State machine transitions.
CREATE TABLE case_status_transition (
    id VARCHAR(3) NOT NULL PRIMARY KEY,
    source VARCHAR(8) NOT NULL,
    target VARCHAR(8) NOT NULL,
    CONSTRAINT fk_case_status_source
        FOREIGN KEY (source) REFERENCES case_status_lov (code),
    CONSTRAINT fk_case_status_target
        FOREIGN KEY (target) REFERENCES case_status_lov (code)
);

-- This table allows a case to have more than one type (e.g. robbery and assault).
CREATE TABLE case_type_xref (
  case_id UUID NOT NULL,
  case_type VARCHAR(15) NOT NULL,
  CONSTRAINT pk_case_type_xref PRIMARY KEY (case_id, case_type),
  CONSTRAINT fk_case_data
    FOREIGN KEY (case_id) REFERENCES case_data (case_id),
  CONSTRAINT fk_case_type
    FOREIGN KEY (case_type) REFERENCES case_type_lov (code)
);	   

-- Need a table for witnesses.

-- Need a table for document attachments.

-- Need history tables to track changes to primary tables.


-- Log table

CREATE TABLE service_call_log (
  id UUID NOT NULL PRIMARY KEY,
  accept_type text,
  class_name text,
  client_id integer,
  content_type text,
  elapsed_time_ms bigint,
  end_time timestamp without time zone,
  error_msg text,
  host_name text,
  http_action text,
  log_response boolean NOT NULL,
  method_name text,
  path_parms json,
  query_parms json,
  remote_ip text,
  req_header_parms json,
  resource text,
  resp_header_parms json,
  response_code integer NOT NULL,
  request_body text,
  response_body text,
  stack_trace text,
  start_time timestamp without time zone,
  submit_time timestamp without time zone,
  user_name text
);

CREATE TABLE audit_log (
   audit_id UUID NOT NULL PRIMARY KEY,
   user_id UUID NOT NULL,
   action_code VARCHAR(15) NOT NULL,
   entity_code TEXT NOT NULL,
   entity_key TEXT NOT NULL,
   audit_date TIMESTAMP NOT NULL,
   entity_value JSON NULL
);


