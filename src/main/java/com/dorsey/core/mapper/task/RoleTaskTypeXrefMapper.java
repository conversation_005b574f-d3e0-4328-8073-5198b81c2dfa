package com.dorsey.core.mapper.task;



import com.dorsey.core.dto.task.RoleWorkflowTaskXrefDTO;
import com.dorsey.core.model.workflow.RoleWorkflowTaskXref;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface RoleTaskTypeXrefMapper {
  RoleTaskTypeXrefMapper INSTANCE = Mappers.getMapper( RoleTaskTypeXrefMapper.class );

  @Mapping(target = "roleId", source = "id.roleId")
  @Mapping(target = "taskId", source = "id.taskId")
  RoleWorkflowTaskXrefDTO roleTaskTypeXrefDTO(RoleWorkflowTaskXref entity);
}
