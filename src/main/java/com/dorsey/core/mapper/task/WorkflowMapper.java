package com.dorsey.core.mapper.task;



import com.dorsey.core.dto.task.ManageWorkflowDTO;
import com.dorsey.core.dto.task.WorkflowDTO;
import com.dorsey.core.model.workflow.Workflow;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface WorkflowMapper {
  WorkflowMapper INSTANCE = Mappers.getMapper( WorkflowMapper.class );

  WorkflowDTO workflowDTO(Workflow entity);
}
