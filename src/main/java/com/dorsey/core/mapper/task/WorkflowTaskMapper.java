package com.dorsey.core.mapper.task;


import com.dorsey.core.dto.task.RoleWorkflowTaskXrefDTO;
import com.dorsey.core.dto.task.WorkflowTaskDTO;
import com.dorsey.core.dto.task.WorkflowTaskTypeDTO;
import com.dorsey.core.mapper.user.UserMapper;
import com.dorsey.core.model.workflow.WorkflowTaskType;
import com.dorsey.core.model.workflow.WorkflowTask;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Set;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", uses = UserMapper.class)
public interface WorkflowTaskMapper {

  @Mapping(target = "assignedTo", source = "taskType", qualifiedByName = "AssignedToMapper")
  @Mapping(target = "taskType", source = "taskType", qualifiedByName = "WorkflowTaskTypeMapper")
  WorkflowTaskDTO taskDTO(WorkflowTask entity);

  @Named("AssignedToMapper")
  default Set<RoleWorkflowTaskXrefDTO> assignedToMapper(WorkflowTaskType source) {
    return source.getAssignedTo().stream().map(RoleTaskTypeXrefMapper.INSTANCE::roleTaskTypeXrefDTO).collect(Collectors.toSet());
  }

  @Named("WorkflowTaskTypeMapper")
  default WorkflowTaskTypeDTO workflowTaskTypeMapper(WorkflowTaskType source) {
    return WorkflowTaskTypeMapper.INSTANCE.workflowTaskTypeMapper(source);
  }
}
