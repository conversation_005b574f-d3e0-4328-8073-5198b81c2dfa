package com.dorsey.core.mapper.organization;

import com.dorsey.core.dto.organization.HierarchyLevelDTO;
import com.dorsey.core.model.organization.HierarchyLevel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface HierarchyLevelMapper {
  HierarchyLevelMapper INSTANCE = Mappers.getMapper( HierarchyLevelMapper.class );

  @Mapping(target = "inUse", ignore = true)
  HierarchyLevelDTO hierarchyLevelDTO(HierarchyLevel entity);
  HierarchyLevel hierarchyLevelEntity(HierarchyLevelDTO dto);
}
