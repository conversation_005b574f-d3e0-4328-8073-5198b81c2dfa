package com.dorsey.core.config;

import com.dorsey.core.session.CustomAuthenticationSuccessHandler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.firewall.StrictHttpFirewall;

@Configuration
@ComponentScan(basePackages = {"com.dorsey.core.session"})
@EnableWebSecurity
public class SecurityConfig {

  @Value("${spring.security.oauth2.client.registration.google.client-id}")
  private String googleClientId;
  @Value("${spring.security.oauth2.client.registration.google.client-secret}")
  private String googleSecret;

  @Bean
  public SecurityFilterChain filterChain(HttpSecurity http, CustomAuthenticationSuccessHandler customAuthenticationSuccessHandler) throws Exception {
//    http.csrf(AbstractHttpConfigurer::disable).authorizeHttpRequests(authorize -> authorize
//            .requestMatchers(HttpMethod.OPTIONS, "/").permitAll()//allow CORS option calls
//            .requestMatchers("/").permitAll().anyRequest().authenticated()
//        )
//        .saml2Login(saml -> saml.successHandler(customAuthenticationSuccessHandler));

//    http.csrf(AbstractHttpConfigurer::disable)
//        .authorizeHttpRequests(authorize -> authorize
//            .requestMatchers(HttpMethod.OPTIONS, "/").permitAll()
//            .requestMatchers("/payment/webhook", "/login", "/logout", "/saml/sso", "/login/saml", "/logout/saml", "/saml/**", "/login/oauth", "/oauth2/**").permitAll() // Permit access to all SAML and login-related URLs
//            .anyRequest().authenticated()
//        )
    http.csrf(AbstractHttpConfigurer::disable).authorizeHttpRequests(authorize -> authorize
            .requestMatchers(HttpMethod.OPTIONS, "/").permitAll()//allow CORS option calls
            .requestMatchers("/").permitAll().anyRequest().authenticated()
        )
        .formLogin(form -> form
            .loginPage("/login")
            .permitAll()
        )
        .logout(logout -> logout
            .logoutUrl("/logout")
            .logoutSuccessUrl("/login?logout")
            .logoutSuccessHandler((request, response, authentication) -> {
              response.sendRedirect("/api/login");
            })
            .invalidateHttpSession(true)
            .deleteCookies("JSESSIONID")
        )
        .saml2Login(saml -> saml
            .loginPage("/saml2/authenticate/okta-saml")
            .successHandler(customAuthenticationSuccessHandler))
        .oauth2Login(oauth2 -> oauth2
//            .loginPage("/oauth2/authorization/google") // Default OAuth2 login page
//            .loginPage("/login/oauth2/code/microsoft")
            .successHandler(customAuthenticationSuccessHandler)
            .failureHandler((request, response, exception) -> {
              System.out.println("Authentication failed: " + exception.getMessage());
              response.sendRedirect("/api/login?error=true");
            })
//            .failureUrl("/login?error=true")
//            .clientRegistrationRepository(clientRegistrationRepository())
        )
        .exceptionHandling(exceptions ->
            exceptions
                .authenticationEntryPoint((request, response, authException) -> {
                  // Redirect to login page if authentication fails
                  response.sendRedirect("/login");
                })
//                .accessDeniedHandler((request, response, accessDeniedException) -> {
//                  // Handle access denied errors
//                  response.sendRedirect("/login"); // Custom access denied page
//                })
        );
    return http.getOrBuild();
  }

//  @Bean
//  public ClientRegistrationRepository clientRegistrationRepository() {
//    return new InMemoryClientRegistrationRepository(
//        googleClientRegistration()
////        ,
////        facebookClientRegistration()
//    );
//  }

  private ClientRegistration googleClientRegistration() {
    return ClientRegistration.withRegistrationId("google")
        .clientId(googleClientId)
        .clientSecret(googleSecret)
        .authorizationUri("https://accounts.google.com/o/oauth2/auth")
        .tokenUri("https://oauth2.googleapis.com/token")
        .userInfoUri("https://www.googleapis.com/oauth2/v3/userinfo")
        .redirectUri("{baseUrl}/login/oauth2/code/google")
        .scope("profile", "email")
        .build();
  }

  private ClientRegistration facebookClientRegistration() {
    return ClientRegistration.withRegistrationId("facebook")
        .clientId("CLIENT_ID")
        .clientSecret("CLIENT_SECRET")
        .authorizationUri("https://www.facebook.com/v12.0/dialog/oauth")
        .tokenUri("https://graph.facebook.com/v12.0/oauth/access_token")
        .userInfoUri("https://graph.facebook.com/me?fields=id,name,email")
        .redirectUri("{baseUrl}/login/oauth2/code/facebook")
        .scope("public_profile", "email")
        .build();
  }

  @Bean
  public JwtDecoder jwtDecoder() {
    return NimbusJwtDecoder
        .withJwkSetUri("https://login.microsoftonline.com/626f456d-190f-4ea5-a52a-a2fb3746426b/discovery/v2.0/keys")
        .jwsAlgorithm(SignatureAlgorithm.RS256).build();
  }


  @Bean
  public HttpFirewall allowUrlEncodedSlashHttpFirewall() {
    StrictHttpFirewall firewall = new StrictHttpFirewall();
    firewall.setAllowUrlEncodedSlash(true);
    return firewall;
  }

//  @Bean
//  public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
//    return factory -> factory.addConnectorCustomizers(connector -> connector.setEncodedSolidusHandling(
//        EncodedSolidusHandling.DECODE.getValue()));
//  }
}
