package com.dorsey.core.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ModelMapperConfig {
  @Bean
  protected ModelMapper modelMapper() {
    ModelMapper ret = new ModelMapper();
    ret.getConfiguration().setMatchingStrategy(MatchingStrategies.LOOSE);

    return ret;
  }

  @Bean
  public ObjectMapper objectMapper() {
    ObjectMapper objMapper = new ObjectMapper();
    objMapper.enable(SerializationFeature.INDENT_OUTPUT);
    objMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    objMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    objMapper.registerModule(new JavaTimeModule());
    return objMapper;
  }
}
