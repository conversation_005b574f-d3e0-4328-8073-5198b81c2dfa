package com.dorsey.core.enums;


import com.dorsey.core.model.lov.DataSetLov;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum DataSet {
  LOGO,
  OH,
  R,
  U;

  public static DataSet fromLov(DataSetLov dataSetLov) {
    return Stream.of(values()).filter(x -> x.name().equals(dataSetLov.getCode())).findFirst().orElse(null);
  }
}
