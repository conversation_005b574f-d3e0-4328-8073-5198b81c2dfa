package com.dorsey.core.dto.users;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class UserBillingInformationDTO {
  private String id;
  private String cardholderName;
  private String cardNumber;
  private String brand;
  private String expDate;
  private Boolean isExpired;
  private Boolean isPreferred;
  private String country;
  private String state;
  private String city;
  private String addressLine1;
  private String addressLine2;
  private String zipCode;
}
