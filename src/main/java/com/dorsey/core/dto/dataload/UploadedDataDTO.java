package com.dorsey.core.dto.dataload;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class UploadedDataDTO {
  private Integer displayOrder;
  private String dataSetCode;
  private String dataSet;
  private String fileName;
  private String status;
  private String statusCode;
  private LocalDateTime uploadedDate;
}
