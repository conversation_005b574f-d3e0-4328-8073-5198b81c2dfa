package com.dorsey.core.dto.organization;

import com.dorsey.core.model.organization.HierarchyNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class HierarchyNodeDTO {
  private Integer id;
  private List<Integer> nodeId;
  private List<Integer> parentId;
  private String hierarchyValue;
  private HierarchyLevelDTO levelData;
  private Integer users;
  private Long items;
  private Boolean canDelete;

  public HierarchyNodeDTO(HierarchyNode mod) {
    if (mod != null) {
      id = mod.getNodeId();
      levelData = HierarchyLevelDTO.builder()
          .level(mod.getLevelData().getLevel())
          .hierarchyName(mod.getLevelData().getHierarchyName())
          .description(mod.getLevelData().getDescription())
          .build();
      hierarchyValue = mod.getHierarchyValue();
    }
  }
}
