package com.dorsey.core.dto.organization;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class HierarchyTreeDTO {
  private List<HierarchyTreeDTO> children;
  private Integer id;
  private String hierarchyValue;
  private HierarchyLevelDTO levelData;
}
