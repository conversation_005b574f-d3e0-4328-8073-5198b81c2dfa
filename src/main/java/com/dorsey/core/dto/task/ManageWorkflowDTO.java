package com.dorsey.core.dto.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ManageWorkflowDTO {
  private UUID id;
  private String workflow;
  private String description;
  private Integer version;
  private String entity;
  private Boolean completed;
  private Boolean canDelete;
}
