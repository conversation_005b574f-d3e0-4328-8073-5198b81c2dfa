package com.dorsey.core.dto.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class WorkflowTaskTypeRelationDTO {
  private Integer id;
  private UUID workflowId;
  private String input;
  private String output;
  private Integer priority;
  private WorkflowTaskTypeDTO inputData;
  private WorkflowTaskTypeDTO outputData;
  private String relevantDataField;
  private String relevantDataValue;
  private String comparisonOperator;
}
