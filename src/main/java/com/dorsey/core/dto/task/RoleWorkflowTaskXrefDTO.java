package com.dorsey.core.dto.task;

import com.dorsey.core.dto.lov.BaseLovDTO;
import com.dorsey.core.model.workflow.WorkflowTaskType;
import com.dorsey.core.model.roles.Role;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class RoleWorkflowTaskXrefDTO extends BaseLovDTO {
  private UUID roleId;
  private UUID taskId;
  private Role role;
  private WorkflowTaskType workflowTaskInfo;
}
