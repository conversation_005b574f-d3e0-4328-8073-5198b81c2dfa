package com.dorsey.core.dto.workflow;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class TaskCustomValue {
  private String field;
  private String label;
  private Boolean visible;
  private Boolean editable;
}
