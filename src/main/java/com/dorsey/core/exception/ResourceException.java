package com.dorsey.core.exception;

import org.springframework.http.HttpStatus;

public class ResourceException extends RuntimeException {
  private final HttpStatus status;

  public ResourceException(final String message) {
    super(message);
    status = HttpStatus.INTERNAL_SERVER_ERROR;
  }

  public ResourceException(final Exception exception) {
    super(exception);
    status = determineStatus(exception);
  }

  public ResourceException(final String message, final Exception exception) {
    super(message, exception);
    status = determineStatus(exception);
  }

  public ResourceException(final String message, final HttpStatus status, final Exception exception) {
    super(message, exception);
    this.status = status;
  }

  public ResourceException(final Throwable ex, final HttpStatus status) {
    super(ex);
    this.status = status;
  }

  private static HttpStatus determineStatus(Exception ex) {
    if (ex instanceof ServiceException) {
      return HttpStatus.BAD_REQUEST;
    } else if (ex instanceof NotFoundException) {
      return HttpStatus.NOT_FOUND;
    } else {
      return HttpStatus.INTERNAL_SERVER_ERROR;
    }
  }

  public HttpStatus getStatus() {
    return status;
  }
}
