package com.dorsey.core.util;

import com.dorsey.core.model.organization.HierarchyNode;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
@AllArgsConstructor
public class OrganizationHierarchyUtil {
  public static void getUserNodeIds(Integer hierarchyId, Set<Integer> userNodeIds, List<HierarchyNode> nodes) {
    userNodeIds.add(hierarchyId);
    nodeDigger(hierarchyId, userNodeIds, nodes);
  }

  private static void nodeDigger(Integer hierarchyId, Set<Integer> userNodeIds, List<HierarchyNode> nodes) {
    var children = nodes.stream().filter(n -> n.getParentId() != null && n.getParentId().equals(hierarchyId)).map(HierarchyNode::getNodeId).toList();

    if (!children.isEmpty()) {
      userNodeIds.addAll(children);
      for (Integer child: children) {
        nodeDigger(child, userNodeIds, nodes);
      }
    }
  }
}
