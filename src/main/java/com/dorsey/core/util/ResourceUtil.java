package com.dorsey.core.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

public final class ResourceUtil {
  private ResourceUtil() {
  }

  public static String getResource(String fileName) throws IOException {
    StringBuilder ret = new StringBuilder();

    try (Reader reader = new BufferedReader(new InputStreamReader
        (Objects.requireNonNull(ResourceUtil.class.getClassLoader().getResourceAsStream(fileName)), StandardCharsets.UTF_8))) {
      int c;
      while ((c = reader.read()) != -1) {
        ret.append((char) c);
      }
    }

    return ret.toString();
  }
}
