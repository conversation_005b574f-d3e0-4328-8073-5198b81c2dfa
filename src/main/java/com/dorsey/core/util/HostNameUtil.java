package com.dorsey.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;

public final class HostNameUtil {
  private static final Logger LOGGER = LoggerFactory.getLogger(HostNameUtil.class);
  private static String hostName;

  private HostNameUtil() {
  }

  public static String getHostName() {
    if (hostName == null) {
      try {
        hostName = InetAddress.getLocalHost().getHostName();
      } catch (UnknownHostException uhe) {
        LOGGER.error("Unable to get hostname!", uhe);
      }
    }

    return hostName;
  }
}
