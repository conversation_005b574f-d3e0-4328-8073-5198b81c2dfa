package com.dorsey.core.util;

import com.dorsey.core.enums.roles.SystemRoles;
import com.dorsey.core.exception.NotFoundException;
import com.dorsey.core.model.roles.Role;
import com.dorsey.core.model.roles.RoleCapabilitiesXref;
import com.dorsey.core.model.roles.RoleCapabilitiesXrefId;
import com.dorsey.core.model.user.UserNotificationsXref;
import com.dorsey.core.model.user.UserRolesXref;
import com.dorsey.core.model.user.UserRolesXrefId;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.role.RoleRepo;
import com.dorsey.core.repository.user.UserRepo;
import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Component
@AllArgsConstructor
public class UserUtil {
  private static final String UNKNOWN = "Unknown";
  private static final UUID ID = UUID.fromString("00000000-0000-0000-0000-0000ffffffff");
  private final UserRepo userRepo;
  private final RoleRepo roleRepo;
  public static final Users VISITOR = Users.builder()
      .userId(ID)
      .email(UNKNOWN)
      .firstName(UNKNOWN)
      .lastName(UNKNOWN)
      .isActive(true)
      .roles(
          Set.of(new UserRolesXref(
              new UserRolesXrefId(
                  ID,
                  ID
              ),
              Role.builder()
                  .roleId(ID)
                  .name(UNKNOWN)
                  .description(UNKNOWN)
                  .department(UNKNOWN)
                  .superior(null)
                  .superiorId(null)
                  .capabilities(List.of(new RoleCapabilitiesXref(new RoleCapabilitiesXrefId(ID, UNKNOWN), false, false, false, false)))
                  .build()))).build();

  public Users getCurrentUser() {
    Authentication auth = SecurityContextHolder.getContext().getAuthentication();
    Users ret = VISITOR;
    if (null != auth && !"anonymousUser".equals(auth.getPrincipal())) {
      ret = userRepo.findByEmailIgnoreCase(auth.getName()).orElseGet(() -> this.getVisitor(auth));
    }
    return ret;
  }

  public Users getVisitor(Authentication auth) {
    OAuth2AuthenticationToken authToken = (OAuth2AuthenticationToken) auth;
    var visitorRole = roleRepo.findByName(SystemRoles.VISITOR.getLabel()).orElseThrow(() -> new NotFoundException("Role not found."));

    String firstName = null;
    String lastName = null;
    String email = null;

    switch (authToken.getAuthorizedClientRegistrationId()) {
      case "google" -> {
        firstName = (String) authToken.getPrincipal().getAttributes().get("given_name");
        lastName = (String) authToken.getPrincipal().getAttributes().get("family_name");
        email = (String) authToken.getPrincipal().getAttributes().get("email");
      }
      case "microsoft", "facebook" -> {
        firstName = (String) authToken.getPrincipal().getAttributes().get("name");
        email = (String) authToken.getPrincipal().getAttributes().get("email");
      }
      default -> throw new NotFoundException("oAuth 2.0 registration Client not found.");
    }

    return Users.builder()
        .userId(UUID.fromString("00000000-0000-0000-0000-000000000000"))
        .roles(Set.of(UserRolesXref.builder().role(visitorRole).build()))
        .firstName(firstName)
        .lastName(lastName)
        .email(email)
        .notificationConfig(new HashSet<>())
        .build();
  }

  public UserNotificationsXref buildNotification(String type, String notificationsBody, String urlPath, UUID userId, String objectId) {
    return UserNotificationsXref.builder()
        .userId(userId)
        .type(type)
        .body(notificationsBody)
        .url(String.format("%s/%s", urlPath, objectId))
        .createdDate(LocalDateTime.now())
        .build();
  }
}
