package com.dorsey.core.util;

import org.apache.commons.lang3.StringUtils;

public class StringUtil {

  public static String toCamelCase(String value) {
    if (value == null || value.isEmpty()) {
      return value;
    }

    String[] words = StringUtils.split(value, ' ');
    words[0] = StringUtils.uncapitalize(words[0]);

    for (int i = 1; i < words.length; i++) {
      words[i] = StringUtils.capitalize(words[i]);
    }

    return StringUtils.join(words);
  }
}
