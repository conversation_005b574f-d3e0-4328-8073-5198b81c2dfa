package com.dorsey.core.util.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.function.IntPredicate;

public enum Comparator {
  EQ("=", x -> x == 0),
  NE("!=", x -> x != 0),
  GTE(">=", x -> x >= 0),
  GT(">", x -> x > 0),
  LT("<", x -> x < 0),
  LTE("<=", x -> x <= 0);

  public final String display;
  public final IntPredicate predicate;
  private static final Map<String, Comparator> lookup = new HashMap<String, Comparator>();

  static {
    for (Comparator d : Comparator.values()) {
      lookup.put(d.getDisplay(), d);
    }
  }

  private Comparator(String display, IntPredicate predicate) {
    this.display = display;
    this.predicate = predicate;
  }

  private boolean test(double left, double right) {
    return predicate.test(Double.compare(left, right));
  }

  public static boolean evaluate(double var1, double var2, Comparator comp) {
    return comp.test(var1, var2);
  }

  public String getDisplay() {
    return display;
  }

  public static Comparator get(String abbreviation) {
    return lookup.get(abbreviation);
  }
}