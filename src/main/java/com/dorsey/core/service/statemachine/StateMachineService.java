package com.dorsey.core.service.statemachine;

import com.dorsey.core.dto.statemachine.CaseStatusTransitionDTO;
import com.dorsey.core.model.statemachine.CaseStatusTransition;
import com.dorsey.core.repository.statemachine.CaseStatusTransitionRepo;
import com.dorsey.core.service.AbstractService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class StateMachineService extends AbstractService {
  private static final Logger log = LoggerFactory.getLogger(StateMachineService.class);

  private final CaseStatusTransitionRepo caseStatusTransitionRepo;


  public List<CaseStatusTransitionDTO> retrieveAllCaseStatusTransitions() {
    return caseStatusTransitionRepo.findAll().stream().map(entity -> modelMapper.map(entity, CaseStatusTransitionDTO.class)).toList();
  }

  @Transactional
  public List<CaseStatusTransitionDTO> updateAllCaseStatusTransitions(List<CaseStatusTransitionDTO> dtos) {
    Assert.notNull(dtos, "Transitions cannot be null.");

    var entities = caseStatusTransitionRepo.findAll();
    var deletedIds = entities.stream().map(CaseStatusTransition::getId).filter(id -> !dtos.stream().map(CaseStatusTransitionDTO::getId).toList().contains(id.toString())).toList();
    var addIds = entities.stream().map(CaseStatusTransition::getId).toList();
    var newEntities = dtos.stream().filter(d -> !addIds.contains(Integer.parseInt(d.getId()))).toList();
    List<CaseStatusTransition> updatedEntities = new ArrayList<>();

    for (CaseStatusTransition entity : entities) {
      dtos.stream().filter(d -> entity.getId().equals(Integer.parseInt(d.getId()))).findFirst().ifPresent(entity::merge);
      if (entity.isDirty()) {
        updatedEntities.add(entity);
      }
    }

    for (CaseStatusTransitionDTO dto : newEntities) {
      updatedEntities.add(CaseStatusTransition.builder()
          .id(Integer.parseInt(dto.getId()))
          .source(dto.getSource())
          .target(dto.getTarget())
          .build()
      );
    }

    caseStatusTransitionRepo.saveAll(updatedEntities);
    caseStatusTransitionRepo.deleteAllById(deletedIds);

    return retrieveAllCaseStatusTransitions();
  }
}
