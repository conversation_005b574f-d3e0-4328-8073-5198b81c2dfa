package com.dorsey.core.service.dataload.processors;

import lombok.AllArgsConstructor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.stereotype.Component;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;

@Component
@AllArgsConstructor
public class LogoProcessor {

  public void process(InputStream image) throws IOException, SAXException, InvalidFormatException {

  }
}
