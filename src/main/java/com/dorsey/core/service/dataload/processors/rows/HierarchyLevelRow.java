package com.dorsey.core.service.dataload.processors.rows;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;


@Data
public class HierarchyLevelRow {
  private String level;
  private String hierarchyName;
  private String description;


  public void setLevel(String level) {
    this.level = StringUtils.trimToNull(level);
  }

  public void setHierarchyName(String hierarchyName) {
    this.hierarchyName = StringUtils.trimToNull(hierarchyName);
  }

  public void setDescription(String description) {
    this.description = StringUtils.trimToNull(description);
  }
}
