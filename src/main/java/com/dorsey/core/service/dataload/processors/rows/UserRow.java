package com.dorsey.core.service.dataload.processors.rows;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;


@Data
public class UserRow {
  private String email;
  private String status;
  private String firstName;
  private String lastName;
  private String title;
  private String hierarchyName;
  private String roles;
  private String effectiveDate;
  private String terminationDate;

  public void setEmail(String email) {
    this.email = StringUtils.trimToNull(email);
  }

  public void setIsActive(String status) {
    this.status = StringUtils.trimToNull(status);
  }

  public void setFirstName(String firstName) {
    this.firstName = StringUtils.trimToNull(firstName);
  }

  public void setLastName(String lastName) {
    this.lastName = StringUtils.trimToNull(lastName);
  }

  public void setTitle(String title) {
    this.title = StringUtils.trimToNull(title);
  }

  public void setHierarchyName(String hierarchyName) {
    this.hierarchyName = StringUtils.trimToNull(hierarchyName);
  }

  public void setRoles(String roles) {
    this.roles = StringUtils.trimToNull(roles);
  }

  public void setEffectiveDate(String effectiveDate) {
    this.effectiveDate = StringUtils.trimToNull(effectiveDate);
  }

  public void setTerminationDate(String terminationDate) {
    this.terminationDate = StringUtils.trimToNull(terminationDate);
  }
}
