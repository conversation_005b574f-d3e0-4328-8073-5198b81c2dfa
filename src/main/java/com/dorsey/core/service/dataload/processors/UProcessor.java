package com.dorsey.core.service.dataload.processors;

import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.organization.HierarchyNode;
import com.dorsey.core.model.roles.Role;
import com.dorsey.core.model.user.UserRolesXref;
import com.dorsey.core.model.user.UserRolesXrefId;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.organization.HierarchyNodeRepo;
import com.dorsey.core.repository.role.RoleRepo;
import com.dorsey.core.repository.user.UserRepo;
import com.dorsey.core.repository.user.UserRolesXrefRepo;
import com.dorsey.core.service.dataload.processors.headers.UserHeader;
import com.dorsey.core.service.dataload.processors.rows.UserRow;
import com.dorsey.core.util.ExcelUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.jxls.reader.ReaderBuilder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class UProcessor {
  private static final String FILE_NAME = "users.xml";

  private static final String EMAIL_PATTERN = "^(?=.{1,64}@)[A-Za-z0-9_-]+(\\.[A-Za-z0-9_-]+)*@[^-][A-Za-z0-9-]+(\\.[A-Za-z0-9-]+)*(\\.[A-Za-z]{2,})$";
  private UserRepo userRepo;
  private HierarchyNodeRepo hierarchyNodeRepo;
  private UserRolesXrefRepo userRolesXrefRepo;
  private RoleRepo roleRepo;

  @Transactional
  public void process(InputStream inputXLS) throws IOException, SAXException, InvalidFormatException {
    var currRoles = userRolesXrefRepo.findAll();
    var dbRows = userRepo.findAll();

    var rows = this.parse(inputXLS);
    List<UserRolesXref> userRoles = new ArrayList<>();
    HashMap<String, Integer> nodesMap = new HashMap<>();
    HashMap<String, UUID> rolesMap = new HashMap<>();
    HashMap<String, UUID> usersMap = new HashMap<>();

    for (HierarchyNode node : hierarchyNodeRepo.findAll()) {
      nodesMap.put(node.getHierarchyValue(), node.getNodeId());
    }

    for (Role role : roleRepo.findAll()) {
      rolesMap.put(role.getName(), role.getRoleId());
    }

    for (UserRow row : rows) {
      var uuid = UUID.randomUUID();
      var data = Users.builder()
          .userId(uuid)
          .email(row.getEmail())
          .isActive(row.getStatus().trim().equals("Active".trim()))
          .firstName(row.getFirstName())
          .lastName(row.getLastName())
          .title(row.getTitle())
          .hierarchyId(nodesMap.get(row.getHierarchyName()))
          .effectiveDate(LocalDate.parse(ExcelUtil.StringToLocalDate(row.getEffectiveDate())).atStartOfDay())
          .terminationDate(row.getTerminationDate() != null ? LocalDate.parse(ExcelUtil.StringToLocalDate(row.getTerminationDate())).atStartOfDay() : null)
          .build();

      if (dbRows.stream().anyMatch(dbr -> row.getEmail().equals(dbr.getEmail()))) {
        userRepo.updateUsers(data.getUserId() ,data.getIsActive(), data.getFirstName(), data.getLastName(), data.getTitle(), data.getHierarchyId(), data.getEffectiveDate(), data.getTerminationDate());
      } else {
        userRepo.save(data);
      }
    }

    var users = userRepo.findByEmailIn(rows.stream().map(UserRow::getEmail).toList());

    for (Users user : users) {
      usersMap.put(user.getEmail(), user.getUserId());
    }

    for (UserRow row : rows) {
      var roles = Arrays.stream(row.getRoles().split(",")).toList();
      for (String role : roles) {
        userRoles.add(
            UserRolesXref.builder()
                .id(
                    UserRolesXrefId.builder()
                        .userId(usersMap.get(row.getEmail()))
                        .roleId(rolesMap.get(StringUtils.trim(role)))
                        .build())
                .build());
      }
    }

    var rolesToDelete = currRoles.stream().filter(cr -> userRoles.stream().anyMatch(ur -> ur.getId().getUserId().equals(cr.getId().getUserId()))).filter(cr -> userRoles.stream().noneMatch(ur -> ur.getId().equals(cr.getId()))).toList();
    var rolesToAdd = userRoles.stream().filter(ur -> currRoles.stream().noneMatch(cr -> cr.getId().equals(ur.getId()))).toList();
    userRolesXrefRepo.deleteAllInBatch(rolesToDelete);
    userRolesXrefRepo.saveAll(rolesToAdd);
  }

  public List<UserRow> parse(InputStream inputXLS) throws IOException, SAXException, InvalidFormatException {
    List<UserRow> rows = new ArrayList<>();
    var header = new UserHeader();

    try (inputXLS; InputStream config = getClass().getResourceAsStream("/templates/mapper/" + FILE_NAME)) {
      var mainReader = ReaderBuilder.buildFromXML(config);
      Map<String, Object> beans = new HashMap<>();
      beans.put("header", header);
      beans.put("rows", rows);
      mainReader.read(inputXLS, beans);
    }

    List<String> results = validate(header, rows);
    if (!results.isEmpty()) {
      throw new ServiceException(String.join(";", results));
    }
    return rows;
  }

  private List<String> validate(UserHeader header, List<UserRow> rows) {
    List<String> errors = new ArrayList<>();

    errors.addAll(validateHeader(header));
    errors.addAll(validateRows(rows));
    return errors;
  }

  private List<String> validateHeader(UserHeader header) {
    List<String> errors = new ArrayList<>();
    if (header.getFirstname() == null || !header.getFirstname().trim().equals("Firstname")) {
      errors.add("Header <b>'Firstname'</b> is missing in column A.");
    }
    if (header.getLastname() == null || !header.getLastname().trim().equals("Lastname")) {
      errors.add("Header <b>'Lastname'</b> is missing in column B.");
    }
    if (header.getTitle() == null || !header.getTitle().trim().equals("Title")) {
      errors.add("Header <b>'Title'</b> is missing in column C.");
    }
    if (header.getEmail() == null || !header.getEmail().trim().equals("Email")) {
      errors.add("Header <b>'Email'</b> is missing in column D.");
    }
    if (header.getHierarchyName() == null || !header.getHierarchyName().trim().equals("Hierarchy Name")) {
      errors.add("Header <b>'Hierarchy Name'</b> is missing in column E.");
    }
    if (header.getRoles() == null || !header.getRoles().trim().equals("Roles")) {
      errors.add("Header <b>'Roles'</b> is missing in column F.");
    }
    if (header.getEffectiveDate() == null || !header.getEffectiveDate().trim().equals("Effective Date")) {
      errors.add("Header <b>'Effective Date'</b> is missing in column G.");
    }
    if (header.getTerminationDate() == null || !header.getTerminationDate().trim().equals("Termination Date")) {
      errors.add("Header <b>'Termination Date'</b> is missing in column H.");
    }
    if (header.getStatus() == null || !header.getStatus().trim().equals("Status")) {
      errors.add("Header <b>'Status'</b> is missing in column I.");
    }
    return errors;
  }

  private List<String> validateRows(List<UserRow> rows) {
    List<String> errors = new ArrayList<>();
    LocalDate effectiveDate = null;
    LocalDate terminationDate = null;

    var hierarchyNodes = hierarchyNodeRepo.findAll().stream().map(HierarchyNode::getHierarchyValue).toList();
    var roles = roleRepo.findAll().stream().map(Role::getName).toList();

    for (int i = 0; i < rows.size(); i++) {
      try {
        var row = rows.get(i);
        var fileEffectiveDate = ExcelUtil.StringToLocalDate(row.getEffectiveDate());
        var fileTerminationDate = ExcelUtil.StringToLocalDate(row.getTerminationDate());

        boolean validEffectiveDate = true;
        boolean validTerminationDate = true;

        var fileRoles = Arrays.stream(row.getRoles().split(",")).map(String::trim).toList();

        if (StringUtils.isEmpty(row.getStatus())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Status cannot be null.", i + 2));
        } else if (row.getStatus().trim().equals("Active".trim()) && row.getStatus().trim().equals("Inactive".trim())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Status can only be <b>'Active'</b> or <b>'Inactive'</b>.", i + 2));
        }

        if (!StringUtils.isEmpty(row.getEmail())) {
          if (row.getEmail().length() > 320) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Email cannot contain more than 320 characters.", i + 2));
          }
          if (!Pattern.compile(EMAIL_PATTERN).matcher(row.getEmail()).matches()) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Email is invalid.", i + 2));
          }
          if (rows.stream().map(UserRow::getEmail).filter(Objects::nonNull).filter(s -> s.equals(row.getEmail())).count() > 1) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Email is duplicated.", i + 2));
          }
        }

        if (StringUtils.isEmpty(row.getFirstName())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Firstname cannot be null.", i + 2));
        } else if (row.getFirstName().length() > 50) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Firstname cannot contain more than 50 characters.", i + 2));
        }
        if (StringUtils.isEmpty(row.getLastName())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Lastname cannot be null.", i + 2));
        } else if (row.getLastName().length() > 50) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Lastname cannot contain more than 50 characters.", i + 2));
        }
        if (!StringUtils.isEmpty(row.getTitle()) && row.getTitle().length() > 15) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Title cannot contain more than 15 characters.", i + 2));
        }
        if (!StringUtils.isEmpty(row.getHierarchyName()) && hierarchyNodes.stream().noneMatch(v -> v.equals(row.getHierarchyName()))) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Hierarchy name doesn''t exist in the Organization hierarchy.", i + 2));
        }
        if (!StringUtils.isEmpty(row.getRoles()) &&  !fileRoles.stream().allMatch(r -> roles.stream().anyMatch(fr -> fr.equals(r)))) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: One or more roles does not exist.", i + 2));
        }
        if (StringUtils.isEmpty(row.getEffectiveDate())) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Effective date cannot be null.", i + 2));
        } else {
          try {
            effectiveDate = LocalDate.parse(fileEffectiveDate);
          } catch (Exception e) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Effective date is invalid. Valid format: YYYY-MM-DD", i + 2));
            validEffectiveDate = false;
          }
        }
        if (!StringUtils.isEmpty(row.getTerminationDate())) {
          try {
            terminationDate = LocalDate.parse(fileTerminationDate);
          } catch (Exception e) {
            errors.add(MessageFormat.format("<b>Row #{0}</b>: Termination date is invalid. Valid format: YYYY-MM-DD", i + 2));
            validTerminationDate = false;
          }
        }
        if (validEffectiveDate && validTerminationDate && effectiveDate != null && terminationDate != null && row.getTerminationDate() != null && effectiveDate.isAfter(terminationDate)) {
          errors.add(MessageFormat.format("<b>Row #{0}</b>: Effective Date cannot be after Termination Date.", i + 2));
        }
      } catch (NumberFormatException e) {
        errors.add(MessageFormat.format("<b>Row #{0}</b>: A value length is over the max supported by the column data type.", i + 2));
      }
    }

    return errors;
  }
}


