package com.dorsey.core.service.dataload.processors.rows;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;


@Data
public class HierarchyTreeRow {
  private String level;
  private String hierarchyValue;
  private String parent;

  public void setLevel(String level) {
    this.level = StringUtils.trimToNull(level);
  }

  public void setHierarchyValue(String hierarchyValue) {
    this.hierarchyValue = StringUtils.trimToNull(hierarchyValue);
  }

  public void setParent(String parent) {
    this.parent = StringUtils.trimToNull(parent);
  }
}
