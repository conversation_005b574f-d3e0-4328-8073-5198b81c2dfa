package com.dorsey.core.service.organization;

import com.dorsey.core.dto.organization.HierarchyLevelDTO;
import com.dorsey.core.dto.organization.HierarchyNodeDTO;
import com.dorsey.core.dto.organization.HierarchyTreeDTO;
import com.dorsey.core.model.organization.HierarchyLevel;
import com.dorsey.core.model.organization.HierarchyNode;
import com.dorsey.core.repository.item.ItemRepo;
import com.dorsey.core.repository.organization.HierarchyLevelRepo;
import com.dorsey.core.repository.organization.HierarchyNodeRepo;
import com.dorsey.core.service.AbstractService;
import com.dorsey.core.service.user.UserService;
import com.dorsey.core.util.OrganizationHierarchyUtil;
import com.dorsey.core.util.UserUtil;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;


@Service
@AllArgsConstructor
public class OrganizationService extends AbstractService {

  private HierarchyLevelRepo hierarchyLevelRepo;
  private HierarchyNodeRepo hierarchyNodeRepo;
  private UserUtil userUtil;
  private UserService userService;
  private ItemRepo hierarchyItemRepo;

  public List<HierarchyLevelDTO> retrieveAllLevels() {
    var dtos = hierarchyLevelRepo.findAll().stream().map(entity -> modelMapper.map(entity, HierarchyLevelDTO.class)).toList();
    var tree = hierarchyNodeRepo.findAll();

    dtos.forEach(d -> d.setInUse(tree.stream().anyMatch(n -> n.getLevel().equals(d.getLevel()))));

    return dtos;
  }

  public void updateLevels(List<HierarchyLevelDTO> dtos) {
    Assert.notNull(dtos, "Hierarchy levels are null!");

    var entities = hierarchyLevelRepo.findAll();
    var deletedIds = entities.stream().map(HierarchyLevel::getLevel).filter(id -> !dtos.stream().map(HierarchyLevelDTO::getLevel).toList().contains(id)).toList();
    var addIds = entities.stream().map(HierarchyLevel::getLevel).toList();
    var newEntities = dtos.stream().filter(d -> !addIds.contains(d.getLevel())).toList();
    List<HierarchyLevel> updatedEntities = new ArrayList<>();

    for (HierarchyLevel entity : entities) {
      dtos.stream().filter(d -> entity.getLevel().equals(d.getLevel())).findFirst().ifPresent(entity::merge);
      if (entity.isDirty()) {
        updatedEntities.add(entity);
      }
    }

    for (HierarchyLevelDTO dto : newEntities) {
      updatedEntities.add(
          HierarchyLevel.builder()
              .level(dto.getLevel())
              .hierarchyName(dto.getHierarchyName())
              .description(dto.getDescription())
              .build());
    }

    hierarchyLevelRepo.saveAll(updatedEntities);
    hierarchyLevelRepo.deleteAllById(deletedIds);
  }

  public List<HierarchyNodeDTO> retrieveAllNodes() {
    var entities = hierarchyNodeRepo.findAll();
    var users = userService.retrieveAllUsers();

    List<HierarchyNodeDTO> dtos = new ArrayList<>();

    if (!entities.isEmpty()) {
      HashMap<Integer, List<Integer>> idMap = new HashMap<>();
      levelIdSetter(entities, Collections.max(entities.stream().map(HierarchyNode::getLevel).toList()));

      for (HierarchyNode entity : entities) {
        nodeIdTreeBuilder(entities, entity, new AtomicReference<>(entity), idMap, new ArrayList<>());
      }

      var itemsMap = hierarchyItemRepo.getItemsAmountGroupedByHierarchyId();
      HashMap<Integer, Boolean> canDeleteMap = new HashMap<>();

      for (HierarchyNode entity : entities) {
        var usersCount = users.stream().filter(u -> entity.getNodeId().equals(u.getHierarchyId())).toList().size();
        var itemsCount = itemsMap.get(entity.getNodeId()) != null ? itemsMap.get(entity.getNodeId()) : 0;

        canDeleteMap.put(entity.getNodeId(), (usersCount == 0 && itemsCount == 0) && !entity.getNodeId().equals(0));
      }

      for (HierarchyNode entity : entities) {
        var usersCount = users.stream().filter(u -> entity.getNodeId().equals(u.getHierarchyId())).toList().size();
        var itemsCount = itemsMap.get(entity.getNodeId()) != null ? itemsMap.get(entity.getNodeId()) : 0;

        if (usersCount > 0 || itemsCount > 0) {
          var currNode = new Gson().fromJson(new Gson().toJson(entity), HierarchyNode.class);
          while (currNode.getParentId() != null) {
            currNode = setParentNodeDelete(currNode, entities, canDeleteMap);
          }
        }
      }

    for (HierarchyNode entity : entities) {
      var parentId = idMap.get(entity.getNodeId()).stream().limit(!idMap.get(entity.getNodeId()).isEmpty() ? idMap.get(entity.getNodeId()).size() - 1 : 0).toList();
      var usersCount = users.stream().filter(u -> entity.getNodeId().equals(u.getHierarchyId())).toList().size();

      dtos.add(
          HierarchyNodeDTO.builder()
              .id(entity.getNodeId())
              .nodeId(idMap.get(entity.getNodeId()))
              .parentId(!parentId.isEmpty() ? parentId : null)
              .hierarchyValue(entity.getHierarchyValue())
              .users(usersCount)
              .items(itemsMap.get(entity.getNodeId()) != null ? itemsMap.get(entity.getNodeId()) : 0)
              .canDelete(canDeleteMap.get(entity.getNodeId()))
              .build()
      );
    }
  }
    return dtos;
}

  public void updateNodes(List<HierarchyNodeDTO> dtos) {
    Assert.notNull(dtos, "Hierarchy nodes are null!");

    var entities = hierarchyNodeRepo.findAll();
    var deletedIds = entities.stream().map(HierarchyNode::getNodeId).filter(id -> !dtos.stream().map(HierarchyNodeDTO::getId).toList().contains(id)).toList();
    var entityIds = entities.stream().map(HierarchyNode::getNodeId).toList();

    List<HierarchyNode> updatedEntities = new ArrayList<>();
    HashMap<List<Integer>, Integer> idMap = new HashMap<>();

    for (HierarchyNode entity : entities) {
      dtos.stream().filter(d -> entity.getNodeId().equals(d.getId())).findFirst().ifPresent(entity::merge);
      if (entity.isDirty()) {
        updatedEntities.add(entity);
      }
    }

    AtomicInteger nextId = new AtomicInteger(1);

    hierarchyNodeRepo.findFirstByOrderByNodeIdDesc().ifPresent(v -> nextId.set(v.getNodeId() + 1));

    for (HierarchyNodeDTO dto : dtos) {
      if (dto.getId() == null) {
        dto.setId(nextId.getAndIncrement());
      }

      idMap.put(dto.getNodeId(), dto.getId());
    }

    var newEntities = dtos.stream().filter(d -> !entityIds.contains(d.getId())).toList();

    for (HierarchyNodeDTO dto : newEntities) {
      updatedEntities.add(
          HierarchyNode.builder()
              .nodeId(idMap.get(dto.getNodeId()))
              .parentId(idMap.get(dto.getParentId()))
              .level(dto.getNodeId().size() - 1)
              .hierarchyValue(dto.getHierarchyValue())
              .build());
    }

    hierarchyNodeRepo.saveAll(updatedEntities);
    hierarchyNodeRepo.deleteAllById(deletedIds);
  }

  private void nodeIdTreeBuilder(List<HierarchyNode> nodes, HierarchyNode node, AtomicReference<HierarchyNode> nextNode, HashMap<Integer, List<Integer>> idMap, List<Integer> dtoNodeId) {
    if (nextNode.get().getParentId() != null) {
      nodes.stream()
          .filter(n -> n.getNodeId().equals(nextNode.get().getParentId()))
          .findFirst().ifPresent(n -> {
            dtoNodeId.add(n.getLevelId());
            nextNode.set(n);
          });

      nodeIdTreeBuilder(nodes, node, nextNode, idMap, dtoNodeId);
    } else {
      Collections.reverse(dtoNodeId);
      dtoNodeId.add(node.getLevelId());
      idMap.put(node.getNodeId(), dtoNodeId);
    }
  }

  private void levelIdSetter(List<HierarchyNode> nodes, int reducer) {
    if (reducer != -1) {
      int counter = 0;
      for (HierarchyNode entity : nodes) {
        if (entity.getLevel() == reducer) {
          entity.setLevelId(counter++);
        }
      }

      levelIdSetter(nodes, --reducer);
    }
  }

  public List<HierarchyTreeDTO> retrieveTree() {
    var nodes = hierarchyNodeRepo.findAll();
    var levels = hierarchyLevelRepo.findAll();
    List<HierarchyTreeDTO> tree = new ArrayList<>();

    treeBuilder(nodes, new ArrayList<>(), tree, 0, levels.stream().map(HierarchyLevel::getLevel).toList());

    return tree;
  }

  public List<HierarchyTreeDTO> retrieveUserTree() {
    var allNodes = hierarchyNodeRepo.findAll();
    Set<Integer> userNodeIds =  new HashSet<>();

    OrganizationHierarchyUtil.getUserNodeIds(userUtil.getCurrentUser().getHierarchyId(), userNodeIds, allNodes);

    var nodes = hierarchyNodeRepo.findAllByNodeIdIn(userNodeIds);
    var levels = hierarchyLevelRepo.findAll();
    List<HierarchyTreeDTO> tree = new ArrayList<>();

    treeBuilder(nodes, new ArrayList<>(), tree, 0, nodes.stream().map(HierarchyNode::getLevel).toList());

    return tree;
  }

  public Boolean checkNodeExistence(Integer nodeId) {
    Assert.notNull(nodeId, "Hierarchy node id is null.");

    return hierarchyNodeRepo.existsByNodeId(nodeId);
  }

  private void treeBuilder(List<HierarchyNode> nodes, List<HierarchyNode> nextNodes, List<HierarchyTreeDTO> tree, Integer level, List<Integer> levels) {
    if (nodes.stream().anyMatch(n -> n.getLevel().equals(level))) {
      if (levels.stream().min(Integer::compareTo).orElse(0).equals(level)) {
        var initialBranches = nodes.stream().filter(n -> n.getLevel().equals(level)).map(n -> HierarchyTreeDTO.builder()
            .children(new ArrayList<>())
            .hierarchyValue(n.getHierarchyValue())
            .id(n.getNodeId())
            .levelData(modelMapper.map(n.getLevelData(), HierarchyLevelDTO.class))
            .build()
        ).toList();
        tree.addAll(initialBranches);
        tree.forEach(b -> b.setChildren(new ArrayList<>()));

        treeBuilder(nodes, nodes.stream().filter(n -> n.getLevel().equals(level + 1)).toList(), tree, level + 1, levels);
      } else {

        for (HierarchyTreeDTO branch : tree) {
          for (HierarchyNode node : nextNodes) {
            if (branch.getId().equals(node.getParentId())) {
              branch.getChildren().add(
                  HierarchyTreeDTO.builder()
                      .id(node.getNodeId())
                      .hierarchyValue(node.getHierarchyValue())
                      .levelData(modelMapper.map(node.getLevelData(), HierarchyLevelDTO.class))
                      .children(new ArrayList<>())
                      .build()
              );
            }
          }

          treeBuilder(nodes, nodes.stream().filter(n -> n.getLevel().equals(level + 1)).toList(), branch.getChildren(), level + 1, levels);
        }
      }
    } else if (levels.stream().anyMatch(l -> l >= level)) {
      treeBuilder(nodes, new ArrayList<>(), tree, level + 1, levels);
    }

  }

  private HierarchyNode setParentNodeDelete(HierarchyNode currNode, List<HierarchyNode> nodes, HashMap<Integer, Boolean> canDeleteMap) {
    canDeleteMap.put(currNode.getParentId(), false);

    return nodes.stream().filter(n -> n.getNodeId().equals(currNode.getParentId())).findFirst().orElse(new HierarchyNode());
  }
}
