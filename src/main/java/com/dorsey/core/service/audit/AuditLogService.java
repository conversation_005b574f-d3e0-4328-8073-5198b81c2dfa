package com.dorsey.core.service.audit;

import com.dorsey.core.dto.audit.AuditLogFilter;
import com.dorsey.core.enums.AuditAction;
import com.dorsey.core.model.logging.AuditLog;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.audit.AuditLogReportingRepo;
import com.dorsey.core.repository.user.UserRepo;
import com.dorsey.core.service.AbstractService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@AllArgsConstructor
public class AuditLogService extends AbstractService {
  private AuditLogReportingRepo repo;
  private UserRepo userRepo;

  public AuditLogFilter getFilter() {
    AuditLogFilter ret = new AuditLogFilter();

    for (AuditAction action : AuditAction.values()) {
      ret.getActions().add(action.getLabel());
    }

    ret.setEntityTypes(repo.findUniqueEntityCodes());

    for (UUID userId : repo.findUniqueUsers()) {
      String email = "unknown";
      Optional<Users> user = userRepo.findById(userId);
      if (user.isPresent()) {
        email = user.get().getEmail();
      }
      ret.getUserEmails().add(email);
    }

    return ret;
  }

  public List<AuditLog> searchAuditLogs(String userEmail, String action, String entityType, LocalDateTime startDate, LocalDateTime endDate) {
    UUID userId = null;

    if (StringUtils.isNotBlank(userEmail) && !userEmail.equalsIgnoreCase("All")) {
      Optional<Users> user = userRepo.findByEmailIgnoreCase(userEmail);
      if (user.isPresent()) {
        userId = user.get().getUserId();
      }
    }

    return repo.search(userId, action, entityType, startDate, endDate);
  }
}
