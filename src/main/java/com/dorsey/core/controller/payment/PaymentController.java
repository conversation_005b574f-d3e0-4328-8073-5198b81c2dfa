package com.dorsey.core.controller.payment;

import com.dorsey.core.controller.AbstractController;
import com.dorsey.core.dto.users.UserBillingInformationDTO;
import com.dorsey.core.model.user.UserBillingInformationXref;
import com.dorsey.core.model.user.UserCreditCard;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.user.UserBillingInformationXrefRepo;
import com.dorsey.core.repository.user.UserCreditCardRepo;
import com.dorsey.core.service.statemachine.StateMachineService;
import com.dorsey.core.util.UserUtil;
import com.google.gson.JsonSyntaxException;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.model.PaymentMethod;
import com.stripe.model.PaymentMethodCollection;
import com.stripe.model.SetupIntent;
import com.stripe.net.ApiResource;
import com.stripe.net.Webhook;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.CustomerListPaymentMethodsParams;
import com.stripe.param.CustomerUpdateParams;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.PaymentMethodAttachParams;
import com.stripe.param.PaymentMethodCreateParams;
import com.stripe.param.PaymentMethodDetachParams;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/payment")
@CrossOrigin
//@AllArgsConstructor
@RequiredArgsConstructor
public class PaymentController extends AbstractController {
  private static final Logger log = LoggerFactory.getLogger(StateMachineService.class);
  @Value("${stripe.api.key}")
  private String stripeApiKey;
  @Value("${stripe.api.endpoint-secret}")
  private String endpointSecret;

  private final UserUtil userUtil;
  private final UserBillingInformationXrefRepo userBillingInformationXrefRepo;
  private final UserCreditCardRepo userCreditCardRepo;

  @PostConstruct
  public void init() {
    Stripe.apiKey = stripeApiKey;
  }

  @GetMapping("/has-card")
  public Boolean hasCard() {
    Users user = userUtil.getCurrentUser();

    if (user.getBillingInformation() == null || !userBillingInformationXrefRepo.existsByStripeCustomerId(user.getBillingInformation().getStripeCustomerId())) {
      return false;
    } else {
      return userCreditCardRepo.existsByStripeCustomerId(user.getBillingInformation().getStripeCustomerId());
    }
  }

  @GetMapping("/cards")
  public List<UserBillingInformationDTO> getCards() throws StripeException {
    Users user = userUtil.getCurrentUser();

    if (user.getBillingInformation() == null || !userBillingInformationXrefRepo.existsByStripeCustomerId(user.getBillingInformation().getStripeCustomerId())) {
      return new ArrayList<>();
    }

    Customer resource = Customer.retrieve(user.getBillingInformation().getStripeCustomerId());
    CustomerListPaymentMethodsParams params =
        CustomerListPaymentMethodsParams.builder().build();

    PaymentMethodCollection paymentMethods = resource.listPaymentMethods(params);

    var cards = new ArrayList<UserBillingInformationDTO>();

    for (PaymentMethod pm: paymentMethods.getData()) {
      Long expMonth = pm.getCard().getExpMonth();
      Long expYear = pm.getCard().getExpYear();

      // Get the current month and year
      java.time.YearMonth currentYearMonth = java.time.YearMonth.now();
      int currentMonth = currentYearMonth.getMonthValue();
      int currentYear = currentYearMonth.getYear();

      cards.add(
          UserBillingInformationDTO.builder()
              .id(pm.getId())
              .cardholderName(pm.getBillingDetails().getName())
              .cardNumber("XXXX-XXXX-XXXX-".concat(pm.getCard().getLast4()))
              .brand(pm.getCard().getBrand())
              .expDate(String.join("/", pm.getCard().getExpMonth().toString(), pm.getCard().getExpYear().toString()))
              .isExpired((expYear < currentYear) || (expYear == currentYear && expMonth < currentMonth))
              .isPreferred(pm.getId().equals(resource.getInvoiceSettings().getDefaultPaymentMethod()))
              .country(pm.getBillingDetails().getAddress().getCountry())
              .state(pm.getBillingDetails().getAddress().getState())
              .city(pm.getBillingDetails().getAddress().getCity())
              .addressLine1(pm.getBillingDetails().getAddress().getLine1())
              .addressLine2(pm.getBillingDetails().getAddress().getLine2())
              .zipCode(pm.getBillingDetails().getAddress().getPostalCode())
              .build()
      );
    }

    return cards;
  }

  @PostMapping("/payment-intent")
  public ResponseEntity<Map<String, String>> createPaymentIntent(@RequestBody Map<String, Object> data) {
    int amount = (int) data.get("amount");

    try {
      // Create a PaymentIntent with the amount and currency
      PaymentIntentCreateParams params = PaymentIntentCreateParams.builder()
          .setAmount((long) amount)  // Amount in cents (e.g., $50 = 5000 cents)
          .setCustomer(this.userUtil.getCurrentUser().getBillingInformation().getStripeCustomerId())
          .setCurrency("usd")
          .build();

      PaymentIntent intent = PaymentIntent.create(params);

      Map<String, String> responseData = new HashMap<>();
      responseData.put(stripeApiKey, intent.getClientSecret());

      return ResponseEntity.ok(responseData);
    } catch (StripeException e) {
      Map<String, String> errorData = new HashMap<>();
      errorData.put("error", e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorData);
    }
  }

  @PostMapping("/create-card")
  public ResponseEntity<Void> addCard(@RequestBody String cardId) throws StripeException {
    Assert.notNull(cardId, "Card ID is null!");

    Customer customer;
    Users user = userUtil.getCurrentUser();

    String stripeCustomerId = Optional.ofNullable(user.getBillingInformation())
        .map(UserBillingInformationXref::getStripeCustomerId)
        .orElse(null);

    if (stripeCustomerId== null) {
      CustomerCreateParams params = CustomerCreateParams.builder()
              .setName(String.join(" ", user.getFirstName(), user.getLastName()))
              .setEmail(user.getEmail())
              .build();

      customer = Customer.create(params);

    } else {
      customer = Customer.retrieve(user.getBillingInformation().getStripeCustomerId());
    }

    if (!userBillingInformationXrefRepo.existsByStripeCustomerId(customer.getId())) {
      userBillingInformationXrefRepo.save(
          UserBillingInformationXref.builder()
              .stripeCustomerId(customer.getId())
              .userId(user.getUserId()).build()
      );
    }

    PaymentMethod resource = PaymentMethod.retrieve(cardId);
    PaymentMethodAttachParams params =
        PaymentMethodAttachParams.builder()
            .setCustomer(customer.getId())
            .build();
    PaymentMethod paymentMethod = resource.attach(params);

    userCreditCardRepo.save(
        UserCreditCard.builder()
            .stripeCustomerId(customer.getId())
            .stripeCardId(paymentMethod.getId())
            .build()
    );

    String defPaymentMethod = Optional.ofNullable(customer.getInvoiceSettings())
        .map(Customer.InvoiceSettings::getDefaultPaymentMethod)
        .orElse(null);

    if (defPaymentMethod == null) {
      setPreferredCard(customer, cardId);
    }

    return ResponseEntity.noContent().build();
  }

  @PutMapping("/update-card")
  public ResponseEntity<Void> updateCard(@RequestBody String cardId) throws StripeException {
    Assert.notNull(cardId, "Card ID is null!");

    Users user = userUtil.getCurrentUser();
    Customer customer = Customer.retrieve(user.getBillingInformation().getStripeCustomerId());

    setPreferredCard(customer, cardId);

    return ResponseEntity.noContent().build();
  }


  @DeleteMapping("/delete-card/{cardId}")
  public ResponseEntity<Void> deleteCard(@PathVariable("cardId") String cardId) throws StripeException {
    Assert.notNull(cardId, "Card ID is null!");

    Users user = userUtil.getCurrentUser();

    PaymentMethod resource = PaymentMethod.retrieve(cardId);
    PaymentMethodDetachParams params = PaymentMethodDetachParams.builder().build();
    resource.detach(params);

    userCreditCardRepo.delete(UserCreditCard.builder().stripeCardId(cardId).stripeCustomerId(user.getBillingInformation().getStripeCustomerId()).build());

    return ResponseEntity.noContent().build();
  }


  @PostMapping("/webhook")
  public ResponseEntity<String> handleStripeWebhook(@RequestBody String payload, @RequestHeader("Stripe-Signature") String sigHeader) {
    Event event;
    PaymentIntent paymentIntent;

    try {
      // Verify the webhook signature
      event = Webhook.constructEvent(payload, sigHeader, endpointSecret);

    } catch (SignatureVerificationException e) {
      // Invalid signature
      return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Webhook signature verification failed");
    }

    // Handle the event
    switch (event.getType()) {
      case "payment_intent.succeeded" -> {
        paymentIntent = (PaymentIntent) event.getData().getObject();
        // Fulfill the purchase
        log.info("Payment succeeded: " + paymentIntent.getId());
      }
      case "payment_intent.payment_failed" -> {
        paymentIntent = (PaymentIntent) event.getDataObjectDeserializer().getObject().orElse(null);
        log.info("Payment failed: " + paymentIntent.getId());
      }
      case "setup_intent.succeeded" -> {
        SetupIntent setupIntent = (SetupIntent) event.getDataObjectDeserializer().getObject().orElse(null);
        log.info(setupIntent.toString());
      }
      // Add more event types as needed
      default -> log.info("Unhandled event type: " + event.getType());
    }

    // Return 200 response
    return ResponseEntity.ok("Webhook received");
  }

  private void setPreferredCard(Customer customer, String cardId) throws StripeException {
    CustomerUpdateParams cusParams =
        CustomerUpdateParams.builder()
            .setInvoiceSettings(
                CustomerUpdateParams.InvoiceSettings.builder()
                    .setDefaultPaymentMethod(cardId)
                    .build()
            ).build();

    customer.update(cusParams);
  }
}
