package com.dorsey.core.controller.dataload;

import com.dorsey.core.controller.AbstractController;
import com.dorsey.core.dto.dataload.UploadedDataDTO;
import com.dorsey.core.service.dataload.CoreFileProcessorService;
import com.dorsey.core.service.dataload.UploadedDataService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/coreUploadedData")
@AllArgsConstructor
public class CoreUploadedDataController extends AbstractController {
  private UploadedDataService uploadedDataService;
  private ObjectMapper objectMapper;

  private CoreFileProcessorService fileProcessorService;

  @GetMapping
  public List<UploadedDataDTO> getAll() {
    return uploadedDataService.retrieveUploadedData();
  }

  @PostMapping
  public UploadedDataDTO addUploadedData(@RequestParam("file") MultipartFile file, @RequestParam("data") String data) throws IOException {
    var uploadedDataDTO = objectMapper.readValue(data, UploadedDataDTO.class);
    return uploadedDataService.updateUploadedData(fileProcessorService, uploadedDataDTO, file);
  }

  @GetMapping("/download/{id}")
  public ResponseEntity<Object> download(@PathVariable String id) {

    var fileOpt = uploadedDataService.getFile(id);
    if (fileOpt.isEmpty()) {
      return ResponseEntity.notFound().build();
    }
    var file = fileOpt.get();
    final FileSystemResource resource = new FileSystemResource(file);

    final HttpHeaders headers = new HttpHeaders();
    headers.setContentType(
        MediaTypeFactory
            .getMediaType(resource)
            .orElse(MediaType.APPLICATION_OCTET_STREAM)
    );

    headers.setContentDisposition(
        ContentDisposition
            .attachment()
            .filename(file.getName())
            .build()
    );

    return new ResponseEntity<>(resource, headers, HttpStatus.OK);
  }
}
