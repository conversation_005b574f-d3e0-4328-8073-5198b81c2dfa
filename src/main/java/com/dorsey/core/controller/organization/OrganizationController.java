package com.dorsey.core.controller.organization;

import com.dorsey.core.controller.AbstractController;
import com.dorsey.core.dto.organization.HierarchyLevelDTO;
import com.dorsey.core.dto.organization.HierarchyNodeDTO;
import com.dorsey.core.dto.organization.HierarchyTreeDTO;
import com.dorsey.core.repository.organization.HierarchyNodeRepo;
import com.dorsey.core.service.organization.OrganizationService;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/organization")
@AllArgsConstructor
public class OrganizationController extends AbstractController {
  private OrganizationService organizationService;
  private HierarchyNodeRepo hierarchyNodeRepo;

  @GetMapping("levels")
  public List<HierarchyLevelDTO> getLevels() {
    return organizationService.retrieveAllLevels();
  }

  @PutMapping("levels")
  public ResponseEntity<Void> updateLevels(@RequestBody List<HierarchyLevelDTO> dtos) {
    organizationService.updateLevels(dtos);
    return ResponseEntity.noContent().build();
  }

  @GetMapping("nodes")
  public List<HierarchyNodeDTO> getHierarchyNodes() {
    return organizationService.retrieveAllNodes();
  }

  @PutMapping("nodes")
  public ResponseEntity<Void> updateHierarchyNodes(@RequestBody List<HierarchyNodeDTO> dtos) {
    organizationService.updateNodes(dtos);
    return ResponseEntity.noContent().build();
  }

  @GetMapping("tree")
  public List<HierarchyTreeDTO> getOrgTree() {
    return organizationService.retrieveTree();
  }

  @GetMapping("userTree")
  public List<HierarchyTreeDTO> getUserOrgTree() {
    return organizationService.retrieveUserTree();
  }

  @GetMapping("checkNodes")
  public Boolean checkNodesExistence() {
    return hierarchyNodeRepo.checkIfNodeIdExist();
  }

  @GetMapping("checkNode/{nodeId}")
  public Boolean checkNodeExistence(@PathVariable Integer nodeId) {
    return organizationService.checkNodeExistence(nodeId);
  }
}
