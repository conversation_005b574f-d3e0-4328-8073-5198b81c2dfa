package com.dorsey.core.controller;

import com.dorsey.core.service.logging.ServiceCallLogger;
import org.apache.commons.lang3.ObjectUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

import static com.dorsey.core.util.AOPUtil.getCalledMethod;

@Aspect
@Component
public class ControllerLogAdvice {
  private final ServiceCallLogger serviceCallLogger;

  public ControllerLogAdvice(ServiceCallLogger serviceCallLogger) {
    this.serviceCallLogger = serviceCallLogger;
  }

  @Around("execution(public * com.dorsey.*.controller.*..*.*(..))")
  public Object retrieveProxy(ProceedingJoinPoint joinPoint) throws Throwable {
    return doCommon(joinPoint);
  }

  private Object doCommon(ProceedingJoinPoint joinPoint) throws Throwable {
    Object ret;
    Method calledMethod = getCalledMethod(joinPoint.getTarget().getClass(), joinPoint);

    try {
      serviceCallLogger.getRESTParameters(joinPoint.getTarget().getClass().getName(), calledMethod);
      serviceCallLogger.setResponseCode(HttpStatus.INTERNAL_SERVER_ERROR);

      ret = joinPoint.proceed();

      serviceCallLogger.setResponseObj(ObjectUtils.defaultIfNull(ret, "").toString());
    } catch (Exception e) {
      serviceCallLogger.handleError(e);
      serviceCallLogger.setResponseObj(null);
      throw e;
    }

    return ret;
  }
}
