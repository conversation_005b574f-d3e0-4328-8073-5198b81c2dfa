package com.dorsey.core.model.lov;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import org.checkerframework.checker.units.qual.Length;

@SuperBuilder
@AllArgsConstructor
@Entity
@NoArgsConstructor
@Getter
@Setter
public class RolesLov extends BaseLov {
  @Id
  private String code;

  public RolesLov(String code, String description) {
    this.code = code;
    this.description = description;
  }

  @Override
  public String key() {
    return code;
  }
}
