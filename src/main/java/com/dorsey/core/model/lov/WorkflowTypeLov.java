package com.dorsey.core.model.lov;

import com.dorsey.core.dto.task.WorkflowTypeDTO;
import com.dorsey.core.model.lov.json.TaskTypeLovData;
import com.dorsey.core.service.AuditListener;
import com.vladmihalcea.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;
import org.springframework.util.Assert;

import java.util.List;

@SuperBuilder(toBuilder = true)
@Builder
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class WorkflowTypeLov extends BaseLov {
  @Id
  private String code;
  @JdbcTypeCode(SqlTypes.JSON)
  @Type(JsonType.class)
  @Column(columnDefinition = "json")
  private List<TaskTypeLovData> data;

  @Override
  public String key() {
    return code;
  }
}
