package com.dorsey.core.model.lov;

import com.dorsey.core.dto.lov.BaseLovDTO;
import com.dorsey.core.model.AbstractModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.NotNull;

@SuperBuilder(toBuilder = true)
@MappedSuperclass
@Getter
@Setter
public abstract class BaseLov extends AbstractModel {
  @NotNull
  String description;
  boolean deprecated;

  protected BaseLov() {
  }

  public <T extends BaseLovDTO> void merge(T dto) {
    this.description = dto.getDescription();
    this.deprecated = dto.isDeprecated();
  }
}
