package com.dorsey.core.model.lov;

import com.dorsey.core.service.AuditListener;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import org.springframework.context.annotation.Profile;

@SuperBuilder
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class AuditActionLov extends BaseLov {
  @Id
  private String code;

  @Override
  public String key() {
    return code;
  }
}
