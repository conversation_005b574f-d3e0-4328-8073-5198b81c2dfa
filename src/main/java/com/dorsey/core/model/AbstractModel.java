package com.dorsey.core.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.Transient;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@SuperBuilder(toBuilder=true)
@Getter
@Setter
public abstract class AbstractModel {
  @Transient
  protected boolean dirty;

  protected AbstractModel() {
  }

  public abstract String key();

  protected String checkForDirty(Object entityValue, Object dtoValue) {
    return (String) checkDirty(entityValue, dtoValue);
  }

  protected Boolean checkForDirty(Boolean entityValue, Boolean dtoValue) {
    return (Boolean) checkDirty(entityValue, dtoValue);
  }

  protected BigDecimal checkForDirty(BigDecimal entityValue, BigDecimal dtoValue) {
    return (BigDecimal) checkDirty(entityValue, dtoValue);
  }

  protected LocalDateTime checkForDirty(LocalDateTime entityValue, LocalDateTime dtoValue) {
    return (LocalDateTime) checkDirty(entityValue, dtoValue);
  }

  protected Integer checkForDirty(Integer entityValue, Integer dtoValue) {
    return (Integer) checkDirty(entityValue, dtoValue);
  }

  protected Long checkForDirty(Long entityValue, Long dtoValue) {
    return (Long) checkDirty(entityValue, dtoValue);
  }

  private Object checkDirty(Object entityValue, Object dtoValue) {
    if ((entityValue == null && dtoValue != null) || (entityValue != null && !entityValue.equals(dtoValue))) {
      entityValue = dtoValue;
      setDirty(true);
    }
    return entityValue;
  }

}
