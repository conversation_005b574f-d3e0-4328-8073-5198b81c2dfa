package com.dorsey.core.model.workflow;


import com.dorsey.core.dto.task.WorkflowTaskDTO;
import com.dorsey.core.model.AbstractAuditModel;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.service.AuditListener;

import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;
import org.springframework.util.Assert;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import java.time.LocalDateTime;
import java.util.UUID;

//@Builder(toBuilder=true)
@SuperBuilder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class WorkflowTask extends AbstractAuditModel {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "task_type_id", referencedColumnName = "taskId")
  private WorkflowTaskType taskType;
  @JdbcTypeCode(SqlTypes.JSON)
  @Type(JsonType.class)
  @Column(columnDefinition = "json")
  private Object value;
  @JdbcTypeCode(SqlTypes.JSON)
  @Type(JsonType.class)
  @Column(columnDefinition = "json")
  private Object reqValue;
  private Integer seq;
  private String entityName;
  private boolean done;
  private boolean approved;
  private boolean deleted;
  private boolean workflowCompleted;
  private String justification;
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "workedBy", referencedColumnName = "userId")
  private Users workedBy;
  private LocalDateTime completedDate;
//  @Transient
////  @OneToMany(cascade= CascadeType.ALL, fetch = FetchType.EAGER, orphanRemoval = true, mappedBy = "code")
//  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
//  @JoinColumn(name = "taskTypeId", referencedColumnName = "taskTypeId")
//  private List<RoleTaskTypeXref> assignedTo;

  public void merge(WorkflowTaskDTO dto) {
    if (null != dto) {
      Assert.notNull(dto.getTaskType(), "Task type is null!");
      Assert.notNull(dto.getValue(), "Value is null!");

      this.value = checkForDirty(this.getValue(), dto.getValue());
      this.taskType.merge(dto.getTaskType());
    }
  }

  @Override
  public String key() {
    return id.toString();
  }
}
