package com.dorsey.core.model.workflow;

import com.dorsey.core.dto.task.WorkflowTypeDTO;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.lov.WorkflowTypeLov;
import com.dorsey.core.service.AuditListener;

import jakarta.persistence.CascadeType;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import org.springframework.util.Assert;

import java.util.UUID;

@SuperBuilder
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class WorkflowType extends AbstractModel {
  @Id
  private UUID id;
  private String name;
  private String description;
  @ManyToOne(cascade= CascadeType.MERGE, fetch = FetchType.EAGER)
  @JoinColumn(name = "entity", referencedColumnName = "code")
  private WorkflowTypeLov type;
  private Boolean deletable;
  private Boolean createdBySystem;

  public void merge(WorkflowTypeDTO dto) {
    if (null != dto) {
      Assert.notNull(dto.getName(), "Workflow name is null!");

      this.name = checkForDirty(this.getName(), dto.getName());
      this.description = checkForDirty(this.getDescription(), dto.getDescription());

//      String entity =  checkForDirty(this.getType().getCode(), dto.getType().getCode());
//      this.type = WorkflowTypeLov.builder().code(entity).description(this.getType().getDescription()).build();
    }
  }

  @Override
  public String key() {
    return id.toString();
  }
}
