package com.dorsey.core.model.workflow;

import com.dorsey.core.annotation.WorkflowObject;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.service.AuditListener;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import java.util.UUID;

@Builder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class Workflow extends AbstractModel {
  @Id
//  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;
  @ManyToOne
  @JoinColumn(name = "workflow_type_id", referencedColumnName = "id")
  private WorkflowType workflowType;
  private Integer version;
  private Boolean active;
  private Boolean completed;

  @Override
  public String key()  {
    return id.toString();
  }
}
