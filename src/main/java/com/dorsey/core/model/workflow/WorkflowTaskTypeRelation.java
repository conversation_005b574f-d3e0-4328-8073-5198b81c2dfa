package com.dorsey.core.model.workflow;

import com.dorsey.core.dto.task.WorkflowTaskTypeRelationDTO;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.service.AuditListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import java.util.UUID;

@Builder(toBuilder=true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class WorkflowTaskTypeRelation extends AbstractModel {
  @Id
  private WorkflowTaskTypeRelationId id;
  private UUID input;
  private UUID output;
  private Integer priority;
  private String relevantDataField;
  private String relevantDataValue;
  private String comparisonOperator;

  @OneToOne
  @JoinColumn(name = "input", referencedColumnName = "taskId", insertable = false, updatable = false)
  private WorkflowTaskType inputData;

  @OneToOne
  @JoinColumn(name = "output", referencedColumnName = "taskId", insertable = false, updatable = false)
  private WorkflowTaskType outputData;

  public void merge(WorkflowTaskTypeRelationDTO dto) {
    if (null != dto) {
      this.input = UUID.fromString(checkForDirty(this.input, dto.getInput()));
      this.output = UUID.fromString(checkForDirty(this.output, dto.getOutput()));
      this.priority = checkForDirty(this.priority, dto.getPriority());
      this.relevantDataField = checkForDirty(this.relevantDataField, dto.getRelevantDataField());
      this.relevantDataValue = checkForDirty(this.relevantDataValue, dto.getRelevantDataValue());
      this.comparisonOperator = checkForDirty(this.comparisonOperator, dto.getComparisonOperator());
    }
  }

  @Override
  public String key()  {
    return id.toString();
  }
}
