package com.dorsey.core.model;

import com.dorsey.core.model.user.Users;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;
import java.time.LocalDateTime;

@SuperBuilder(toBuilder = true)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public abstract class AbstractAuditModel extends AbstractModel {
  @CreatedDate
  private LocalDateTime createdDate;
  @LastModifiedDate
  private LocalDateTime modifiedDate;
  @LastModifiedBy
  @JoinColumn(name = "modified_by")
  @ManyToOne(fetch = FetchType.EAGER)
  private Users modifiedBy;
  @CreatedBy
  @JoinColumn(name = "created_by")
  @ManyToOne(fetch = FetchType.EAGER)
  private Users createdBy;
}
