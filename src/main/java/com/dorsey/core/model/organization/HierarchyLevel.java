package com.dorsey.core.model.organization;


import com.dorsey.core.dto.organization.HierarchyLevelDTO;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.service.AuditListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.Assert;

import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;

@Builder(toBuilder = true)
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class HierarchyLevel extends AbstractModel {
  @Id
  private Integer level;
  private String hierarchyName;
  private String description;

  public void merge(HierarchyLevelDTO dto) {
    if (null != dto) {
      Assert.notNull(dto.getHierarchyName(), "Hierarchy Name is null!");
      Assert.notNull(dto.getDescription(), "Description is null!");

      this.hierarchyName = checkForDirty(this.hierarchyName, dto.getHierarchyName());
      this.description = checkForDirty(this.description, dto.getDescription());
    }
  }

  @Override
  public String key() {
    return getLevel().toString();
  }
}
