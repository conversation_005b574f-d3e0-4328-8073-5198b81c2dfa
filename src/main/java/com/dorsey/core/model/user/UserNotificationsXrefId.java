package com.dorsey.core.model.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import jakarta.persistence.Embeddable;
import java.io.Serializable;
import java.util.UUID;

@Builder
@AllArgsConstructor
@Embeddable
@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
public class UserNotificationsXrefId implements Serializable {
  private UUID userId;
  private Integer id;
}
