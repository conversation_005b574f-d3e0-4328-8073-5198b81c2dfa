package com.dorsey.core.model.user;

import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.service.AuditListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;

@Builder
@AllArgsConstructor
@EntityListeners(AuditListener.class)
@Entity
@NoArgsConstructor
@Getter
@Setter
public class UserNotificationsConfigXref extends AbstractModel {
  @EmbeddedId
  private UserNotificationsConfigXrefId id;
  private UUID objectId;
  @NotNull
  private Boolean notify;

  @Override
  public String key() {
    return id.getUserId() + "|" + id.getType();
  }
}
