package com.dorsey.core.repository.organization;

import com.dorsey.core.model.organization.HierarchyNode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface HierarchyNodeRepo extends JpaRepository<HierarchyNode, Integer> {

  Optional<HierarchyNode> findFirstByOrderByNodeIdDesc();

  @Query(nativeQuery = true, value = """
WITH user_nodes AS (
	WITH RECURSIVE menu_tree (node_id, parent_id, level, hierarchy_value)
	AS (
  		SELECT node_id, parent_id, level, hierarchy_value FROM hierarchy_node WHERE parent_id=?1 OR node_id=?1
  		UNION
  		SELECT hn.node_id, hn.parent_id, hn.level, hn.hierarchy_value FROM hierarchy_node hn, menu_tree mt WHERE hn.parent_id = mt.node_id
	)
	SELECT * FROM menu_tree
)
SELECT * FROM user_nodes
""")
  List<HierarchyNode> findByUserHierarchyId(Integer nodeId);

  List<HierarchyNode> findAllByNodeIdIn(Set<Integer> nodeId);

  @Query("SELECT CASE WHEN COUNT(hn)>1 THEN true ELSE false END AS result FROM HierarchyNode hn WHERE hn.nodeId>0")
  Boolean checkIfNodeIdExist();

  Boolean existsByNodeId(Integer nodeId);
}
