//package com.dorsey.core.repository.workflow;
//
//import com.dorsey.core.model.workflow.WorkflowTaskRelation;
//import com.dorsey.core.model.workflow.WorkflowTaskRelationId;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//import java.util.UUID;
//
//public interface WorkflowTaskRelationRepo extends JpaRepository<WorkflowTaskRelation, WorkflowTaskRelationId> {
//
//  List<WorkflowTaskRelation> findAllById_WorkflowId(UUID workflowId);
//
//  @Transactional
//  void deleteAllById_WorkflowId(UUID workflowId);
//}
