package com.dorsey.core.repository.user;

import com.dorsey.core.model.user.UserNotificationsXref;
import com.dorsey.core.model.user.UserNotificationsXrefId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface UserNotificationsXrefRepo extends JpaRepository<UserNotificationsXref, UserNotificationsXrefId> {

  List<UserNotificationsXref> findByUserId (UUID userId);
}
