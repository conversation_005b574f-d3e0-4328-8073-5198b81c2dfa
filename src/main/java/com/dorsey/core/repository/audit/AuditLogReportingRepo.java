package com.dorsey.core.repository.audit;

import com.dorsey.core.model.logging.AuditLog;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.repository.Query;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@AllArgsConstructor
public class AuditLogReportingRepo {
  private static final String AND = " AND ";

  private JdbcTemplate jdbcTemplate;
  private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

  public List<String> findUniqueEntityCodes() {
    return jdbcTemplate.queryForList("SELECT DISTINCT a.entity_code FROM audit_log a", String.class);
  }

  public List<UUID> findUniqueUsers() {
    return jdbcTemplate.queryForList("SELECT DISTINCT a.user_id FROM audit_log a", UUID.class);
  }

  @Query("SELECT a FROM AuditLog a WHERE a.auditDate BETWEEN :startDate AND :endDate")
  public List<AuditLog> search(UUID userId, String action, String entityType, LocalDateTime startDate, LocalDateTime endDate) {
    String query = "SELECT a.* FROM audit_log a";
    StringBuilder where = new StringBuilder(" WHERE ");
    Map<String, Object> namedParameters = new HashMap<>();

    if (null != userId) {
      where.append(" a.user_id = :userId ");
      namedParameters.put("userId", userId);
    }

    if (StringUtils.isNotBlank(action) && !action.equalsIgnoreCase("All")) {
      where.append(namedParameters.isEmpty() ? "" : AND).append(" a.action_code = :action");
      namedParameters.put("action", action);
    }

    if (StringUtils.isNotBlank(entityType) && !entityType.equalsIgnoreCase("All")) {
      where.append(namedParameters.isEmpty() ? "" : AND).append(" a.entity_code = :entityType");
      namedParameters.put("entityType", entityType);
    }

    if (null != startDate || null != endDate) {
      if (null != startDate && null != endDate) {
        where.append(namedParameters.isEmpty() ? "" : AND).append(" audit_date BETWEEN :startDate AND :endDate");
        namedParameters.put("startDate", startDate.with(LocalTime.of(23, 59, 59)).minusDays(1));
        namedParameters.put("endDate", endDate.with(LocalTime.of(23, 59, 59)));
      } else if (null != startDate) {
        where.append(namedParameters.isEmpty() ? "" : AND).append(" audit_date > :startDate");
        namedParameters.put("startDate", startDate.with(LocalTime.of(23, 59, 59)).minusDays(1));
      } else {
        where.append(namedParameters.isEmpty() ? "" : AND).append(" audit_date < :endDate");
        namedParameters.put("endDate", endDate.with(LocalTime.of(23, 59, 59)));
      }
    }

    if (!namedParameters.isEmpty()) {
      query += where.toString();
    }

    return namedParameterJdbcTemplate.query(query, namedParameters, (rs, rowNum) -> new AuditLog(rs.getString("audit_id"),
        rs.getObject("user_id", java.util.UUID.class), rs.getString("action_code"), rs.getString("entity_code"),
        rs.getString("entity_key"), rs.getTimestamp("audit_date").toLocalDateTime(),
        rs.getObject("entity_value")));
  }
}
