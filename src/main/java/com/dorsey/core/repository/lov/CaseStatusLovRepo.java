package com.dorsey.core.repository.lov;

import com.dorsey.core.model.lov.CaseStatusLov;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

@Transactional
public interface CaseStatusLovRepo extends JpaRepository<CaseStatusLov, String> {

  @Modifying
  @Query("DELETE FROM CaseStatusLov csl WHERE csl.code NOT IN ('open', 'close')")
  void deleteAllExceptDefault();
}
