<?xml version="1.0" encoding="ISO-8859-1"?>
<workbook>
  <worksheet name="Hierarchy Levels">
    <section startRow="0" endRow="0">
      <mapping cell="A1">levelHeader.level</mapping>
      <mapping cell="B1">levelHeader.hierarchyName</mapping>
      <mapping cell="C1">levelHeader.description</mapping>
    </section>
    <loop startRow="0" endRow="0" items="levelRows" var="row"
          varType="com.dorsey.core.service.dataload.processors.rows.HierarchyLevelRow">
      <section startRow="0" endRow="0">
        <mapping row="0" col="0" nullAllowed="true">row.level</mapping>
        <mapping row="0" col="1" nullAllowed="true">row.hierarchyName</mapping>
        <mapping row="0" col="2" nullAllowed="true">row.description</mapping>
      </section>
      <loopbreakcondition>
        <rowcheck offset="0">
        </rowcheck>
      </loopbreakcondition>
    </loop>
  </worksheet>
  <worksheet name="Hierarchy Tree">
    <section startRow="0" endRow="0">
      <mapping cell="A1">treeHeader.level</mapping>
      <mapping cell="B1">treeHeader.hierarchyValue</mapping>
      <mapping cell="C1">treeHeader.parent</mapping>
    </section>
    <loop startRow="0" endRow="0" items="treeRows" var="row"
          varType="com.dorsey.core.service.dataload.processors.rows.HierarchyTreeRow">
      <section startRow="0" endRow="0">
        <mapping row="0" col="0" nullAllowed="true">row.level</mapping>
        <mapping row="0" col="1" nullAllowed="true">row.hierarchyValue</mapping>
        <mapping row="0" col="2" nullAllowed="true">row.parent</mapping>
      </section>
      <loopbreakcondition>
        <rowcheck offset="0">
        </rowcheck>
      </loopbreakcondition>
    </loop>
  </worksheet>
</workbook>
