package com.dorsey.core.service.user;

import com.dorsey.core.dto.users.User;
import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.user.UserRepo;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.cache.CacheManager;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepo userRepository;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private UserUtil userUtil;

    @Mock
    private ModelMapper modelMapper;

    @Mock
    private MultipartFile profileImage;

    @InjectMocks
    private UserService userService;

    private Users userEntity1;
    private Users userEntity2;
    private User userDTO1;
    private User userDTO2;
    private UUID userId1;
    private UUID userId2;

    @BeforeEach
    void setUp() {
        userId1 = UUID.randomUUID();
        userId2 = UUID.randomUUID();

        userEntity1 = Users.builder()
                .userId(userId1)
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .build();

        userEntity2 = Users.builder()
                .userId(userId2)
                .email("<EMAIL>")
                .firstName("Jane")
                .lastName("Smith")
                .build();

        userDTO1 = User.builder()
                .userId(userId1)
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .build();

        userDTO2 = User.builder()
                .userId(userId2)
                .email("<EMAIL>")
                .firstName("Jane")
                .lastName("Smith")
                .build();

        ReflectionTestUtils.setField(userService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveAllUsers() {
        // Given
        List<Users> userEntities = Arrays.asList(userEntity1, userEntity2);
        when(userRepository.findAll()).thenReturn(userEntities);

        // When
        List<User> result = userService.retrieveAllUsers();

        // Then
        assertEquals(2, result.size());
        verify(userRepository).findAll();
        // Note: The actual User constructor is called, so we can't easily verify the exact mapping
    }

    @Test
    void testRetrieveAllUsersEmpty() {
        // Given
        when(userRepository.findAll()).thenReturn(Arrays.asList());

        // When
        List<User> result = userService.retrieveAllUsers();

        // Then
        assertTrue(result.isEmpty());
        verify(userRepository).findAll();
    }

    @Test
    void testRetrieveUserByEmail() {
        // Given
        String email = "<EMAIL>";
        when(userRepository.findByEmailIgnoreCase(email)).thenReturn(Optional.of(userEntity1));

        // When
        User result = userService.retrieveUser(email);

        // Then
        assertNotNull(result);
        verify(userRepository).findByEmailIgnoreCase(email);
    }

    @Test
    void testRetrieveUserByEmailNotFound() {
        // Given
        String email = "<EMAIL>";
        when(userRepository.findByEmailIgnoreCase(email)).thenReturn(Optional.empty());

        // When
        User result = userService.retrieveUser(email);

        // Then
        assertNotNull(result); // Should return UserUtil.VISITOR
        verify(userRepository).findByEmailIgnoreCase(email);
    }

    @Test
    void testRetrieveUserProfile() {
        // Given
        when(userUtil.getCurrentUser()).thenReturn(userEntity1);

        // When
        User result = userService.retrieveUserProfile();

        // Then
        assertNotNull(result);
        verify(userUtil).getCurrentUser();
    }

    @Test
    void testUpdateUserWithoutProfileImage() throws IOException {
        // Given
        when(userRepository.findById(userId1)).thenReturn(Optional.of(userEntity1));
        when(userRepository.save(userEntity1)).thenReturn(userEntity1);

        // When
        userService.updateUser(userDTO1, null);

        // Then
        verify(userRepository).findById(userId1);
        verify(userRepository).save(userEntity1);
        verify(userEntity1).merge(userDTO1);
    }

    @Test
    void testUpdateUserWithProfileImage() throws IOException {
        // Given
        byte[] imageBytes = "image data".getBytes();
        when(userRepository.findById(userId1)).thenReturn(Optional.of(userEntity1));
        when(userRepository.save(userEntity1)).thenReturn(userEntity1);
        when(profileImage.getBytes()).thenReturn(imageBytes);

        // When
        userService.updateUser(userDTO1, profileImage);

        // Then
        verify(userRepository).findById(userId1);
        verify(userRepository).save(userEntity1);
        verify(userEntity1).merge(userDTO1);
        verify(userEntity1).setProfileImage(imageBytes);
        verify(profileImage).getBytes();
    }

    @Test
    void testUpdateUserNotFound() {
        // Given
        when(userRepository.findById(userId1)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ServiceException.class, 
            () -> userService.updateUser(userDTO1, null));
        
        verify(userRepository).findById(userId1);
        verify(userRepository, never()).save(any());
    }

    @Test
    void testUpdateUserWithNullUser() {
        // When & Then
        assertThrows(IllegalArgumentException.class, 
            () -> userService.updateUser(null, null));
        
        verifyNoInteractions(userRepository);
    }

    @Test
    void testUpdateUserWithIOException() throws IOException {
        // Given
        when(userRepository.findById(userId1)).thenReturn(Optional.of(userEntity1));
        when(profileImage.getBytes()).thenThrow(new IOException("File read error"));

        // When & Then
        assertThrows(IOException.class, 
            () -> userService.updateUser(userDTO1, profileImage));
        
        verify(userRepository).findById(userId1);
        verify(userRepository, never()).save(any());
    }

    @Test
    void testUpdateUsers() {
        // Given
        List<User> userDTOs = Arrays.asList(userDTO1, userDTO2);
        List<Users> userEntities = Arrays.asList(userEntity1, userEntity2);
        
        when(userRepository.findAll()).thenReturn(userEntities);
        when(userRepository.saveAll(anyList())).thenReturn(userEntities);

        // When
        userService.updateUsers(userDTOs);

        // Then
        verify(userRepository).findAll();
        verify(userRepository).saveAll(anyList());
    }

    @Test
    void testUpdateUsersEmpty() {
        // Given
        List<User> emptyUserDTOs = Arrays.asList();

        // When
        userService.updateUsers(emptyUserDTOs);

        // Then
        verifyNoInteractions(userRepository);
    }

    @Test
    void testUpdateUsersWithNewUsers() {
        // Given
        User newUserDTO = User.builder()
                .userId(UUID.randomUUID())
                .email("<EMAIL>")
                .firstName("New")
                .lastName("User")
                .build();
        
        List<User> userDTOs = Arrays.asList(userDTO1, newUserDTO);
        List<Users> existingEntities = Arrays.asList(userEntity1);
        
        when(userRepository.findAll()).thenReturn(existingEntities);
        when(userRepository.saveAll(anyList())).thenReturn(existingEntities);

        // When
        userService.updateUsers(userDTOs);

        // Then
        verify(userRepository).findAll();
        verify(userRepository).saveAll(anyList());
    }
}
