package com.dorsey.core.service;

import com.dorsey.core.enums.AuditAction;
import com.dorsey.core.model.AbstractModel;
import com.dorsey.core.model.logging.AuditLog;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.audit.AuditLogRepo;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuditListenerTest {

    @Mock
    private AuditLogRepo auditLogRepo;

    @Mock
    private UserUtil userUtil;

    private AuditListener auditListener;
    private Users mockUser;

    @BeforeEach
    void setUp() {
        mockUser = Users.builder()
                .userId(UUID.randomUUID())
                .email("<EMAIL>")
                .build();

        auditListener = new AuditListener();
        auditListener.init(auditLogRepo, userUtil);

        when(userUtil.getCurrentUser()).thenReturn(mockUser);
    }

    @Test
    void testInit() {
        // Given
        AuditLogRepo newRepo = mock(AuditLogRepo.class);
        UserUtil newUserUtil = mock(UserUtil.class);

        // When
        auditListener.init(newRepo, newUserUtil);

        // Then
        assertDoesNotThrow(() -> auditListener.init(newRepo, newUserUtil));
    }

    @Test
    void testDoCreate() throws Exception {
        // Given
        TestEntity entity = new TestEntity();
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // When
        ReflectionTestUtils.invokeMethod(auditListener, "doCreate", entity);

        // Then
        verify(auditLogRepo).save(auditLogCaptor.capture());
        AuditLog capturedLog = auditLogCaptor.getValue();

        assertNotNull(capturedLog);
        assertEquals(AuditAction.CREATE.getLabel(), capturedLog.getActionCode());
        assertEquals("TestEntity", capturedLog.getEntityCode());
        assertEquals("test-key", capturedLog.getEntityKey());
        assertEquals(mockUser.getUserId(), capturedLog.getUserId());
        assertEquals(entity, capturedLog.getEntityValue());
    }

    @Test
    void testDoUpdate() throws Exception {
        // Given
        TestEntity entity = new TestEntity();
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // When
        ReflectionTestUtils.invokeMethod(auditListener, "doUpdate", entity);

        // Then
        verify(auditLogRepo).save(auditLogCaptor.capture());
        AuditLog capturedLog = auditLogCaptor.getValue();

        assertNotNull(capturedLog);
        assertEquals(AuditAction.UPDATE.getLabel(), capturedLog.getActionCode());
        assertEquals("TestEntity", capturedLog.getEntityCode());
        assertEquals("test-key", capturedLog.getEntityKey());
        assertEquals(mockUser.getUserId(), capturedLog.getUserId());
        assertEquals(entity, capturedLog.getEntityValue());
    }

    @Test
    void testDoDelete() throws Exception {
        // Given
        TestEntity entity = new TestEntity();
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // When
        ReflectionTestUtils.invokeMethod(auditListener, "doDelete", entity);

        // Then
        verify(auditLogRepo).save(auditLogCaptor.capture());
        AuditLog capturedLog = auditLogCaptor.getValue();

        assertNotNull(capturedLog);
        assertEquals(AuditAction.DELETE.getLabel(), capturedLog.getActionCode());
        assertEquals("TestEntity", capturedLog.getEntityCode());
        assertEquals("test-key", capturedLog.getEntityKey());
        assertEquals(mockUser.getUserId(), capturedLog.getUserId());
        assertEquals(entity, capturedLog.getEntityValue());
    }

    @Test
    void testInitWithNullParameters() {
        // Given & When & Then
        assertDoesNotThrow(() -> auditListener.init(null, null));
    }

    @Test
    void testDoCreateWithNullUser() throws Exception {
        // Given
        TestEntity entity = new TestEntity();
        when(userUtil.getCurrentUser()).thenReturn(null);
        ArgumentCaptor<AuditLog> auditLogCaptor = ArgumentCaptor.forClass(AuditLog.class);

        // When
        ReflectionTestUtils.invokeMethod(auditListener, "doCreate", entity);

        // Then
        verify(auditLogRepo).save(auditLogCaptor.capture());
        AuditLog capturedLog = auditLogCaptor.getValue();

        assertNotNull(capturedLog);
        assertEquals(UUID.fromString("00000000-0000-0000-0000-000000000000"), capturedLog.getUserId());
    }

    // Test entity class for testing purposes
    private static class TestEntity extends AbstractModel {
        private String name = "test";

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String key() {
            return "test-key";
        }
    }
}
