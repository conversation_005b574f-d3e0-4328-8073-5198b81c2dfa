package com.dorsey.core.service.audit;

import com.dorsey.core.dto.audit.AuditLogFilter;
import com.dorsey.core.enums.AuditAction;
import com.dorsey.core.model.logging.AuditLog;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.repository.audit.AuditLogReportingRepo;
import com.dorsey.core.repository.user.UserRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuditLogServiceTest {

    @Mock
    private AuditLogReportingRepo repo;

    @Mock
    private UserRepo userRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private AuditLogService auditLogService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(auditLogService, "modelMapper", modelMapper);
    }

    @Test
    void testGetFilter() {
        // Given
        List<String> entityTypes = Arrays.asList("User", "Role", "Organization");
        UUID userId1 = UUID.randomUUID();
        UUID userId2 = UUID.randomUUID();
        List<UUID> userIds = Arrays.asList(userId1, userId2);

        Users user1 = Users.builder()
                .userId(userId1)
                .email("<EMAIL>")
                .build();
        Users user2 = Users.builder()
                .userId(userId2)
                .email("<EMAIL>")
                .build();

        when(repo.findUniqueEntityCodes()).thenReturn(entityTypes);
        when(repo.findUniqueUsers()).thenReturn(userIds);
        when(userRepo.findById(userId1)).thenReturn(Optional.of(user1));
        when(userRepo.findById(userId2)).thenReturn(Optional.of(user2));

        // When
        AuditLogFilter result = auditLogService.getFilter();

        // Then
        assertNotNull(result);
        
        // Verify actions are populated with all AuditAction values
        assertEquals(AuditAction.values().length, result.getActions().size());
        for (AuditAction action : AuditAction.values()) {
            assertTrue(result.getActions().contains(action.getLabel()));
        }

        // Verify entity types
        assertEquals(entityTypes, result.getEntityTypes());

        // Verify user emails
        assertEquals(2, result.getUserEmails().size());
        assertTrue(result.getUserEmails().contains("<EMAIL>"));
        assertTrue(result.getUserEmails().contains("<EMAIL>"));

        verify(repo).findUniqueEntityCodes();
        verify(repo).findUniqueUsers();
        verify(userRepo).findById(userId1);
        verify(userRepo).findById(userId2);
    }

    @Test
    void testGetFilterWithUnknownUser() {
        // Given
        List<String> entityTypes = Arrays.asList("User");
        UUID unknownUserId = UUID.randomUUID();
        List<UUID> userIds = Arrays.asList(unknownUserId);

        when(repo.findUniqueEntityCodes()).thenReturn(entityTypes);
        when(repo.findUniqueUsers()).thenReturn(userIds);
        when(userRepo.findById(unknownUserId)).thenReturn(Optional.empty());

        // When
        AuditLogFilter result = auditLogService.getFilter();

        // Then
        assertNotNull(result);
        assertEquals(1, result.getUserEmails().size());
        assertTrue(result.getUserEmails().contains("unknown"));

        verify(repo).findUniqueEntityCodes();
        verify(repo).findUniqueUsers();
        verify(userRepo).findById(unknownUserId);
    }

    @Test
    void testGetFilterWithEmptyResults() {
        // Given
        when(repo.findUniqueEntityCodes()).thenReturn(Arrays.asList());
        when(repo.findUniqueUsers()).thenReturn(Arrays.asList());

        // When
        AuditLogFilter result = auditLogService.getFilter();

        // Then
        assertNotNull(result);
        assertEquals(AuditAction.values().length, result.getActions().size());
        assertTrue(result.getEntityTypes().isEmpty());
        assertTrue(result.getUserEmails().isEmpty());

        verify(repo).findUniqueEntityCodes();
        verify(repo).findUniqueUsers();
        verifyNoInteractions(userRepo);
    }

    @Test
    void testGetFilterWithMixedUserResults() {
        // Given
        List<String> entityTypes = Arrays.asList("User", "Role");
        UUID validUserId = UUID.randomUUID();
        UUID invalidUserId = UUID.randomUUID();
        List<UUID> userIds = Arrays.asList(validUserId, invalidUserId);

        Users validUser = Users.builder()
                .userId(validUserId)
                .email("<EMAIL>")
                .build();

        when(repo.findUniqueEntityCodes()).thenReturn(entityTypes);
        when(repo.findUniqueUsers()).thenReturn(userIds);
        when(userRepo.findById(validUserId)).thenReturn(Optional.of(validUser));
        when(userRepo.findById(invalidUserId)).thenReturn(Optional.empty());

        // When
        AuditLogFilter result = auditLogService.getFilter();

        // Then
        assertNotNull(result);
        assertEquals(2, result.getUserEmails().size());
        assertTrue(result.getUserEmails().contains("<EMAIL>"));
        assertTrue(result.getUserEmails().contains("unknown"));

        verify(repo).findUniqueEntityCodes();
        verify(repo).findUniqueUsers();
        verify(userRepo).findById(validUserId);
        verify(userRepo).findById(invalidUserId);
    }

    @Test
    void testGetFilterVerifyAllAuditActions() {
        // Given
        when(repo.findUniqueEntityCodes()).thenReturn(Arrays.asList());
        when(repo.findUniqueUsers()).thenReturn(Arrays.asList());

        // When
        AuditLogFilter result = auditLogService.getFilter();

        // Then
        assertNotNull(result);
        
        // Verify all audit actions are included
        for (AuditAction action : AuditAction.values()) {
            assertTrue(result.getActions().contains(action.getLabel()), 
                    "Missing action: " + action.getLabel());
        }
    }
}
