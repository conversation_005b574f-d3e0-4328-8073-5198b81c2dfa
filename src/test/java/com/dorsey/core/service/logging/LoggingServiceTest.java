package com.dorsey.core.service.logging;

import com.dorsey.core.model.logging.ServiceCallLog;
import com.dorsey.core.repository.logging.ServiceCallLogRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LoggingServiceTest {

    @Mock
    private ServiceCallLogRepo dao;

    @InjectMocks
    private LoggingService loggingService;

    private ServiceCallLog serviceCallLog;

    @BeforeEach
    void setUp() {
        serviceCallLog = mock(ServiceCallLog.class);
    }

    @Test
    void testSave() {
        // Given
        when(dao.save(serviceCallLog)).thenReturn(serviceCallLog);

        // When
        loggingService.save(serviceCallLog);

        // Then
        verify(dao).save(serviceCallLog);
    }

    @Test
    void testSaveWithNullEntry() {
        // Given
        ServiceCallLog nullEntry = null;

        // When
        loggingService.save(nullEntry);

        // Then
        verify(dao).save(nullEntry);
    }

    @Test
    void testSaveMultipleTimes() {
        // Given
        ServiceCallLog log1 = mock(ServiceCallLog.class);
        ServiceCallLog log2 = mock(ServiceCallLog.class);
        ServiceCallLog log3 = mock(ServiceCallLog.class);

        // When
        loggingService.save(log1);
        loggingService.save(log2);
        loggingService.save(log3);

        // Then
        verify(dao).save(log1);
        verify(dao).save(log2);
        verify(dao).save(log3);
        verify(dao, times(3)).save(any(ServiceCallLog.class));
    }

    @Test
    void testConstructorInjection() {
        // Given & When
        LoggingService service = new LoggingService(dao);

        // Then
        // Verify that the service was created successfully
        // and can perform operations
        service.save(serviceCallLog);
        verify(dao).save(serviceCallLog);
    }
}
