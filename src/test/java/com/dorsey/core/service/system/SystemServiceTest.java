package com.dorsey.core.service.system;

import com.dorsey.core.dto.system.SystemDTO;
import com.dorsey.core.exception.NotFoundException;
import com.dorsey.core.model.system.System;
import com.dorsey.core.model.lov.SystemFilesLov;
import com.dorsey.core.repository.system.SystemRepo;
import com.dorsey.core.repository.lov.SystemFilesLovRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SystemServiceTest {

    @Mock
    private SystemRepo systemRepo;

    @Mock
    private SystemFilesLovRepo systemFileLovRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private SystemService systemService;

    private System systemEntity;
    private SystemDTO systemDTO;
    private SystemFilesLov systemFile1;
    private SystemFilesLov systemFile2;

    @BeforeEach
    void setUp() {
        systemEntity = System.builder()
                .id("system-config")
                .theme(1)
                .colors("blue,green,red")
                .logo("logo.png")
                .build();

        systemDTO = SystemDTO.builder()
                .theme(1)
                .colors(Arrays.asList("blue", "green", "red"))
                .logo("logo.png")
                .build();

        systemFile1 = new SystemFilesLov();
        systemFile1.setCode("FILE1");
        systemFile1.setDescription("File 1");

        systemFile2 = new SystemFilesLov();
        systemFile2.setCode("FILE2");
        systemFile2.setDescription("File 2");

        ReflectionTestUtils.setField(systemService, "modelMapper", modelMapper);
    }

    @Test
    void testRetrieveConfiguration() {
        // Given
        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemEntity));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getTheme());
        assertEquals(Arrays.asList("blue", "green", "red"), result.getColors());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationNotFound() {
        // Given
        when(systemRepo.findById("system-config")).thenReturn(Optional.empty());

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNull(result);
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithNullColors() {
        // Given
        System systemWithNullColors = System.builder()
                .id("system-config")
                .theme(2)
                .colors(null)
                .logo("logo.png")
                .build();

        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithNullColors));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(2), result.getTheme());
        assertTrue(result.getColors().isEmpty());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithEmptyColors() {
        // Given
        System systemWithEmptyColors = System.builder()
                .id("system-config")
                .theme(3)
                .colors("")
                .logo("logo.png")
                .build();

        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithEmptyColors));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(3), result.getTheme());
        assertEquals(Arrays.asList(""), result.getColors()); // Empty string becomes single empty element
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithSingleColor() {
        // Given
        System systemWithSingleColor = System.builder()
                .id("system-config")
                .theme(4)
                .colors("blue")
                .logo("logo.png")
                .build();

        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithSingleColor));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(4), result.getTheme());
        assertEquals(Arrays.asList("blue"), result.getColors());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveConfigurationWithTrailingCommas() {
        // Given
        System systemWithTrailingCommas = System.builder()
                .id("system-config")
                .theme(5)
                .colors("blue,green,red,")
                .logo("logo.png")
                .build();

        when(systemRepo.findById("system-config")).thenReturn(Optional.of(systemWithTrailingCommas));

        // When
        SystemDTO result = systemService.retrieveConfiguration();

        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(5), result.getTheme());
        assertEquals(Arrays.asList("blue", "green", "red", ""), result.getColors());
        assertEquals("logo.png", result.getLogo());
        verify(systemRepo).findById("system-config");
    }

    @Test
    void testRetrieveSystemFile() {
        // Given
        String code = "FILE1";
        systemFile1.setFile("test file content".getBytes());
        when(systemFileLovRepo.findById(code)).thenReturn(Optional.of(systemFile1));

        // When
        String result = systemService.retrieveSystemFile(code);

        // Then
        assertNotNull(result);
        verify(systemFileLovRepo).findById(code);
    }

    @Test
    void testRetrieveSystemFileNotFound() {
        // Given
        String code = "NONEXISTENT";
        when(systemFileLovRepo.findById(code)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(NotFoundException.class,
            () -> systemService.retrieveSystemFile(code));

        verify(systemFileLovRepo).findById(code);
    }

    @Test
    void testUpdateConfiguration() {
        // Given
        when(systemRepo.save(any(System.class))).thenReturn(systemEntity);

        // When
        systemService.updateConfiguration(systemDTO);

        // Then
        verify(systemRepo).save(any(System.class));
    }

    @Test
    void testUpdateConfigurationWithNullColors() {
        // Given
        SystemDTO dtoWithNullColors = SystemDTO.builder()
                .theme(2)
                .colors(null)
                .logo("logo.png")
                .build();

        when(systemRepo.save(any(System.class))).thenReturn(systemEntity);

        // When
        systemService.updateConfiguration(dtoWithNullColors);

        // Then
        verify(systemRepo).save(any(System.class));
    }

    @Test
    void testUpdateConfigurationWithEmptyColors() {
        // Given
        SystemDTO dtoWithEmptyColors = SystemDTO.builder()
                .theme(2)
                .colors(Arrays.asList())
                .logo("logo.png")
                .build();

        when(systemRepo.save(any(System.class))).thenReturn(systemEntity);

        // When
        systemService.updateConfiguration(dtoWithEmptyColors);

        // Then
        verify(systemRepo).save(any(System.class));
    }

    @Test
    void testUpdateConfigurationWithNullDTO() {
        // When & Then
        assertThrows(IllegalArgumentException.class,
            () -> systemService.updateConfiguration(null));

        verifyNoInteractions(systemRepo);
    }

    @Test
    void testRetrieveSystemFileWithNullCode() {
        // When & Then
        assertThrows(IllegalArgumentException.class,
            () -> systemService.retrieveSystemFile(null));

        verifyNoInteractions(systemFileLovRepo);
    }
}
