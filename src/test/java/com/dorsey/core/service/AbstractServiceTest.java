package com.dorsey.core.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AbstractServiceTest {

    @Mock
    private ModelMapper modelMapper;

    private TestableAbstractService abstractService;

    @BeforeEach
    void setUp() {
        abstractService = new TestableAbstractService();
        ReflectionTestUtils.setField(abstractService, "modelMapper", modelMapper);
    }

    @Test
    void testModelMapperInjection() {
        // Given & When & Then
        assertNotNull(abstractService.getModelMapper());
        assertEquals(modelMapper, abstractService.getModelMapper());
    }

    @Test
    void testModelMapperUsage() {
        // Given
        String source = "test";
        String expected = "mapped";
        when(modelMapper.map(source, String.class)).thenReturn(expected);

        // When
        String result = abstractService.testMapping(source);

        // Then
        assertEquals(expected, result);
        verify(modelMapper).map(source, String.class);
    }

    // Test implementation of AbstractService for testing purposes
    private static class TestableAbstractService extends AbstractService {
        public ModelMapper getModelMapper() {
            return modelMapper;
        }

        public String testMapping(String source) {
            return modelMapper.map(source, String.class);
        }
    }
}
